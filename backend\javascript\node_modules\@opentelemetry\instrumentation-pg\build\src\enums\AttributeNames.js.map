{"version": 3, "file": "AttributeNames.js", "sourceRoot": "", "sources": ["../../../src/enums/AttributeNames.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,qEAAqE;AACrE,IAAY,cAKX;AALD,WAAY,cAAc;IACxB,oDAAkC,CAAA;IAClC,gDAA8B,CAAA;IAC9B,2EAAyD,CAAA;IACzD,yDAAuC,CAAA;AACzC,CAAC,EALW,cAAc,GAAd,sBAAc,KAAd,sBAAc,QAKzB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Postgresql specific attributes not covered by semantic conventions\nexport enum AttributeNames {\n  PG_VALUES = 'db.postgresql.values',\n  PG_PLAN = 'db.postgresql.plan',\n  IDLE_TIMEOUT_MILLIS = 'db.postgresql.idle.timeout.millis',\n  MAX_CLIENT = 'db.postgresql.max.client',\n}\n"]}