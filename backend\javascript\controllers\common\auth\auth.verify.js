// node_gemini_summarizer/controllers/auth/auth.verify.js
import User from '../../../models/User.js';
import VerificationCode from '../../../models/VerificationCode.js';
import { generateAuthToken, formatUserResponse } from './auth.helpers.js';

export const verifyUserEmail = async (req, res) => {
  const { email, verificationCode } = req.body;

  if (!email || !verificationCode) {
    return res.status(400).json({ error: 'Email and verification code are required.' });
  }

  try {
    const verificationEntry = await VerificationCode.findOne({ email, code: verificationCode });

    if (!verificationEntry) {
      return res.status(400).json({ error: 'Invalid or incorrect verification code.' });
    }

    if (verificationEntry.expiresAt < new Date()) {
      await VerificationCode.deleteOne({ _id: verificationEntry._id });
      return res.status(400).json({ error: 'Verification code has expired. Please request a new one.' });
    }

    const user = await User.findOne({ email });
    if (!user) {
      // This case should be rare if verification code exists, but good to check
      return res.status(404).json({ error: 'User not found.' });
    }

    user.isVerified = true;
    await user.save();
    await VerificationCode.deleteOne({ _id: verificationEntry._id });

    const token = generateAuthToken(user);

    res.json({
        message: 'Email verified successfully. You are now logged in.',
        token,
        user: formatUserResponse(user)
    });

  } catch (error) {
    console.error('Verify email error:', error.message, error.stack);
    res.status(500).json({ error: 'Server error during email verification.' });
  }
};