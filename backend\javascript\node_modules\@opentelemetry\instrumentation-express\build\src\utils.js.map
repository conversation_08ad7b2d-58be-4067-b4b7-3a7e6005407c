{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAQH,+DAA4D;AAC5D,2DAAwD;AACxD,qDAI0B;AAE1B;;;;GAIG;AACI,MAAM,cAAc,GAAG,CAAC,OAAuB,EAAE,KAAc,EAAE,EAAE;IACxE,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,uCAAsB,CAAC,CAAC,KAAK,KAAK,EAAE;QAC5D,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,uCAAsB,EAAE;YACrD,UAAU,EAAE,KAAK;YACjB,KAAK,EAAE,EAAE;SACV,CAAC,CAAC;KACJ;IACD,IAAI,KAAK,KAAK,SAAS;QAAE,OAAO;IAC/B,OAAO,CAAC,uCAAsB,CAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5D,CAAC,CAAC;AATW,QAAA,cAAc,kBASzB;AAEF;;;;;GAKG;AACI,MAAM,aAAa,GAAG,CAAC,IAAY,EAAE,KAAmB,EAAU,EAAE;;IACzE,MAAM,UAAU,GAAG,MAAA,MAAA,KAAK,CAAC,MAAM,0CAAE,KAAK,0CAAG,CAAC,CAAC,CAAC;IAE5C,IAAI,MAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,KAAK,0CAAE,IAAI,EAAE;QAC3B,OAAO,GAAG,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;KAC1C;IAED,IAAI,MAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,MAAM,0CAAE,KAAK,EAAE;QAC7B,OAAO,IAAA,qBAAa,EAAC,IAAI,EAAE,UAAU,CAAC,CAAC;KACxC;IAED,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAZW,QAAA,aAAa,iBAYxB;AAEF;;;;;GAKG;AACI,MAAM,gBAAgB,GAAG,CAC9B,KAAa,EACb,KAAmB,EACnB,SAAkB,EAIlB,EAAE;;IACF,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE;QAC3B,MAAM,eAAe,GAAG,IAAA,qBAAa,EAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QACjD,MAAM,mBAAmB,GAAG,eAAe;YACzC,CAAC,CAAC,eAAe;YACjB,CAAC,CAAC,SAAS,IAAI,KAAK,IAAI,GAAG,CAAC;QAE9B,OAAO;YACL,UAAU,EAAE;gBACV,CAAC,+BAAc,CAAC,YAAY,CAAC,EAAE,mBAAmB;gBAClD,CAAC,+BAAc,CAAC,YAAY,CAAC,EAAE,mCAAgB,CAAC,MAAM;aACvD;YACD,IAAI,EAAE,YAAY,mBAAmB,EAAE;SACxC,CAAC;KACH;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,gBAAgB,EAAE;QAC1C,OAAO;YACL,UAAU,EAAE;gBACV,CAAC,+BAAc,CAAC,YAAY,CAAC,EAC3B,MAAA,CAAC,KAAK,IAAI,SAAS,CAAC,mCAAI,iBAAiB;gBAC3C,CAAC,+BAAc,CAAC,YAAY,CAAC,EAAE,mCAAgB,CAAC,eAAe;aAChE;YACD,IAAI,EAAE,kBAAkB,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,KAAK,IAAI,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;SACvE,CAAC;KACH;SAAM;QACL,OAAO;YACL,UAAU,EAAE;gBACV,CAAC,+BAAc,CAAC,YAAY,CAAC,EAAE,KAAK,CAAC,IAAI;gBACzC,CAAC,+BAAc,CAAC,YAAY,CAAC,EAAE,mCAAgB,CAAC,UAAU;aAC3D;YACD,IAAI,EAAE,gBAAgB,KAAK,CAAC,IAAI,EAAE;SACnC,CAAC;KACH;AACH,CAAC,CAAC;AAvCW,QAAA,gBAAgB,oBAuC3B;AAEF;;;;;GAKG;AACH,MAAM,gBAAgB,GAAG,CACvB,QAAgB,EAChB,OAAsB,EACb,EAAE;IACX,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC/B,OAAO,OAAO,KAAK,QAAQ,CAAC;KAC7B;SAAM,IAAI,OAAO,YAAY,MAAM,EAAE;QACpC,OAAO,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC/B;SAAM,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;QACxC,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC;KAC1B;SAAM;QACL,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;KAC3D;AACH,CAAC,CAAC;AAEF;;;;;;;GAOG;AACI,MAAM,cAAc,GAAG,CAC5B,IAAY,EACZ,IAAsB,EACtB,MAAqC,EAC5B,EAAE;;IACX,IACE,KAAK,CAAC,OAAO,CAAC,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,gBAAgB,CAAC;SACvC,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,gBAAgB,0CAAE,QAAQ,CAAC,IAAI,CAAC,CAAA,EACxC;QACA,OAAO,IAAI,CAAC;KACb;IACD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,YAAY,CAAC,KAAK,KAAK;QAAE,OAAO,KAAK,CAAC;IAChE,IAAI;QACF,KAAK,MAAM,OAAO,IAAI,MAAO,CAAC,YAAa,EAAE;YAC3C,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;gBACnC,OAAO,IAAI,CAAC;aACb;SACF;KACF;IAAC,OAAO,CAAC,EAAE;QACV,gBAAgB;KACjB;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAvBW,QAAA,cAAc,kBAuBzB;AAEF;;;;;GAKG;AACI,MAAM,iBAAiB,GAAG,CAC/B,KAAc,EAC4B,EAAE,CAC5C,KAAK,YAAY,KAAK;IACpB,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC;IACxB,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AALxB,QAAA,iBAAiB,qBAKO;AAErC;;;;;GAKG;AACI,MAAM,YAAY,GAAG,CAC1B,IAA2D,EACvC,EAAE;IACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAEzB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;QAC3B,OAAO,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KAC1E;IAED,OAAO,uBAAuB,CAAC,QAAQ,CAAC,CAAC;AAC3C,CAAC,CAAC;AAVW,QAAA,YAAY,gBAUvB;AAEF,MAAM,uBAAuB,GAAG,CAAC,GAAqB,EAAE,EAAE;IACxD,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QAC3B,OAAO,GAAG,CAAC;KACZ;IAED,IAAI,GAAG,YAAY,MAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QACpD,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;KACvB;IAED,OAAO;AACT,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Attributes } from '@opentelemetry/api';\nimport {\n  IgnoreMatcher,\n  ExpressInstrumentationConfig,\n  LayerPathSegment,\n} from './types';\nimport { ExpressLayerType } from './enums/ExpressLayerType';\nimport { AttributeNames } from './enums/AttributeNames';\nimport {\n  ExpressLayer,\n  PatchedRequest,\n  _LAYERS_STORE_PROPERTY,\n} from './internal-types';\n\n/**\n * Store layers path in the request to be able to construct route later\n * @param request The request where\n * @param [value] the value to push into the array\n */\nexport const storeLayerPath = (request: PatchedRequest, value?: string) => {\n  if (Array.isArray(request[_LAYERS_STORE_PROPERTY]) === false) {\n    Object.defineProperty(request, _LAYERS_STORE_PROPERTY, {\n      enumerable: false,\n      value: [],\n    });\n  }\n  if (value === undefined) return;\n  (request[_LAYERS_STORE_PROPERTY] as string[]).push(value);\n};\n\n/**\n * Recursively search the router path from layer stack\n * @param path The path to reconstruct\n * @param layer The layer to reconstruct from\n * @returns The reconstructed path\n */\nexport const getRouterPath = (path: string, layer: ExpressLayer): string => {\n  const stackLayer = layer.handle?.stack?.[0];\n\n  if (stackLayer?.route?.path) {\n    return `${path}${stackLayer.route.path}`;\n  }\n\n  if (stackLayer?.handle?.stack) {\n    return getRouterPath(path, stackLayer);\n  }\n\n  return path;\n};\n\n/**\n * Parse express layer context to retrieve a name and attributes.\n * @param route The route of the layer\n * @param layer Express layer\n * @param [layerPath] if present, the path on which the layer has been mounted\n */\nexport const getLayerMetadata = (\n  route: string,\n  layer: ExpressLayer,\n  layerPath?: string\n): {\n  attributes: Attributes;\n  name: string;\n} => {\n  if (layer.name === 'router') {\n    const maybeRouterPath = getRouterPath('', layer);\n    const extractedRouterPath = maybeRouterPath\n      ? maybeRouterPath\n      : layerPath || route || '/';\n\n    return {\n      attributes: {\n        [AttributeNames.EXPRESS_NAME]: extractedRouterPath,\n        [AttributeNames.EXPRESS_TYPE]: ExpressLayerType.ROUTER,\n      },\n      name: `router - ${extractedRouterPath}`,\n    };\n  } else if (layer.name === 'bound dispatch') {\n    return {\n      attributes: {\n        [AttributeNames.EXPRESS_NAME]:\n          (route || layerPath) ?? 'request handler',\n        [AttributeNames.EXPRESS_TYPE]: ExpressLayerType.REQUEST_HANDLER,\n      },\n      name: `request handler${layer.path ? ` - ${route || layerPath}` : ''}`,\n    };\n  } else {\n    return {\n      attributes: {\n        [AttributeNames.EXPRESS_NAME]: layer.name,\n        [AttributeNames.EXPRESS_TYPE]: ExpressLayerType.MIDDLEWARE,\n      },\n      name: `middleware - ${layer.name}`,\n    };\n  }\n};\n\n/**\n * Check whether the given obj match pattern\n * @param constant e.g URL of request\n * @param obj obj to inspect\n * @param pattern Match pattern\n */\nconst satisfiesPattern = (\n  constant: string,\n  pattern: IgnoreMatcher\n): boolean => {\n  if (typeof pattern === 'string') {\n    return pattern === constant;\n  } else if (pattern instanceof RegExp) {\n    return pattern.test(constant);\n  } else if (typeof pattern === 'function') {\n    return pattern(constant);\n  } else {\n    throw new TypeError('Pattern is in unsupported datatype');\n  }\n};\n\n/**\n * Check whether the given request is ignored by configuration\n * It will not re-throw exceptions from `list` provided by the client\n * @param constant e.g URL of request\n * @param [list] List of ignore patterns\n * @param [onException] callback for doing something when an exception has\n *     occurred\n */\nexport const isLayerIgnored = (\n  name: string,\n  type: ExpressLayerType,\n  config?: ExpressInstrumentationConfig\n): boolean => {\n  if (\n    Array.isArray(config?.ignoreLayersType) &&\n    config?.ignoreLayersType?.includes(type)\n  ) {\n    return true;\n  }\n  if (Array.isArray(config?.ignoreLayers) === false) return false;\n  try {\n    for (const pattern of config!.ignoreLayers!) {\n      if (satisfiesPattern(name, pattern)) {\n        return true;\n      }\n    }\n  } catch (e) {\n    /* catch block*/\n  }\n\n  return false;\n};\n\n/**\n * Converts a user-provided error value into an error and error message pair\n *\n * @param error - User-provided error value\n * @returns Both an Error or string representation of the value and an error message\n */\nexport const asErrorAndMessage = (\n  error: unknown\n): [error: string | Error, message: string] =>\n  error instanceof Error\n    ? [error, error.message]\n    : [String(error), String(error)];\n\n/**\n * Extracts the layer path from the route arguments\n *\n * @param args - Arguments of the route\n * @returns The layer path\n */\nexport const getLayerPath = (\n  args: [LayerPathSegment | LayerPathSegment[], ...unknown[]]\n): string | undefined => {\n  const firstArg = args[0];\n\n  if (Array.isArray(firstArg)) {\n    return firstArg.map(arg => extractLayerPathSegment(arg) || '').join(',');\n  }\n\n  return extractLayerPathSegment(firstArg);\n};\n\nconst extractLayerPathSegment = (arg: LayerPathSegment) => {\n  if (typeof arg === 'string') {\n    return arg;\n  }\n\n  if (arg instanceof RegExp || typeof arg === 'number') {\n    return arg.toString();\n  }\n\n  return;\n};\n"]}