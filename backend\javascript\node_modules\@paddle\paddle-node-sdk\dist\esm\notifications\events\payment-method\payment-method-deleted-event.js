import { Event } from '../../../entities/events/event.js';
import { PaymentMethodDeletedNotification } from '../../entities/index.js';
import { EventName } from '../../helpers/index.js';
export class PaymentMethodDeletedEvent extends Event {
    constructor(response) {
        super(response);
        this.eventType = EventName.PaymentMethodDeleted;
        this.data = new PaymentMethodDeletedNotification(response.data);
    }
}
