{"version": 3, "file": "ProxyLogger.js", "sourceRoot": "", "sources": ["../../src/ProxyLogger.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAK3C;IAIE,qBACU,SAA0B,EAClB,IAAY,EACZ,OAA4B,EAC5B,OAAmC;QAH3C,cAAS,GAAT,SAAS,CAAiB;QAClB,SAAI,GAAJ,IAAI,CAAQ;QACZ,YAAO,GAAP,OAAO,CAAqB;QAC5B,YAAO,GAAP,OAAO,CAA4B;IAClD,CAAC;IAEJ;;;;OAIG;IACH,0BAAI,GAAJ,UAAK,SAAoB;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG;IACK,gCAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;YACX,OAAO,WAAW,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,kBAAC;AAAD,CAAC,AAvCD,IAuCC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NOOP_LOGGER } from './NoopLogger';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { LogRecord } from './types/LogRecord';\n\nexport class ProxyLogger implements Logger {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Logger;\n\n  constructor(\n    private _provider: LoggerDelegator,\n    public readonly name: string,\n    public readonly version?: string | undefined,\n    public readonly options?: LoggerOptions | undefined\n  ) {}\n\n  /**\n   * Emit a log record. This method should only be used by log appenders.\n   *\n   * @param logRecord\n   */\n  emit(logRecord: LogRecord): void {\n    this._getLogger().emit(logRecord);\n  }\n\n  /**\n   * Try to get a logger from the proxy logger provider.\n   * If the proxy logger provider has no delegate, return a noop logger.\n   */\n  private _getLogger() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n    const logger = this._provider.getDelegateLogger(\n      this.name,\n      this.version,\n      this.options\n    );\n    if (!logger) {\n      return NOOP_LOGGER;\n    }\n    this._delegate = logger;\n    return this._delegate;\n  }\n}\n\nexport interface LoggerDelegator {\n  getDelegateLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger | undefined;\n}\n"]}