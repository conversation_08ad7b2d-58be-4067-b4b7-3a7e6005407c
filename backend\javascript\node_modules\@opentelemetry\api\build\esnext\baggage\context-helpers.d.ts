import { Context } from '../context/types';
import { Baggage } from './types';
/**
 * Retrieve the current baggage from the given context
 *
 * @param {Context} Context that manage all context values
 * @returns {Baggage} Extracted baggage from the context
 */
export declare function getBaggage(context: Context): Baggage | undefined;
/**
 * Retrieve the current baggage from the active/current context
 *
 * @returns {Baggage} Extracted baggage from the context
 */
export declare function getActiveBaggage(): Baggage | undefined;
/**
 * Store a baggage in the given context
 *
 * @param {Context} Context that manage all context values
 * @param {Baggage} baggage that will be set in the actual context
 */
export declare function setBaggage(context: Context, baggage: Baggage): Context;
/**
 * Delete the baggage stored in the given context
 *
 * @param {Context} Context that manage all context values
 */
export declare function deleteBaggage(context: Context): Context;
//# sourceMappingURL=context-helpers.d.ts.map