var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { BaseResource, PathParameters, QueryParameters } from '../../internal/base/index.js';
import { Adjustment, AdjustmentCollection, AdjustmentCreditNotePDF } from '../../entities/index.js';
const AdjustmentPaths = {
    list: '/adjustments',
    create: '/adjustments',
    getCreditNotePDF: '/adjustments/{adjustment_id}/credit-note',
};
export * from './operations/index.js';
export class AdjustmentsResource extends BaseResource {
    list(queryParams) {
        const queryParameters = new QueryParameters(queryParams);
        return new AdjustmentCollection(this.client, AdjustmentPaths.list + queryParameters.toQueryString());
    }
    create(createAdjustmentParameters) {
        return __awaiter(this, void 0, void 0, function* () {
            const response = yield this.client.post(AdjustmentPaths.create, createAdjustmentParameters);
            const data = this.handleResponse(response);
            return new Adjustment(data);
        });
    }
    getCreditNotePDF(adjustmentId, queryParams) {
        return __awaiter(this, void 0, void 0, function* () {
            const urlWithPathParams = new PathParameters(AdjustmentPaths.getCreditNotePDF, {
                adjustment_id: adjustmentId,
            }).deriveUrl();
            const queryParameters = new QueryParameters(queryParams);
            const response = yield this.client.get(urlWithPathParams + queryParameters.toQueryString());
            const data = this.handleResponse(response);
            return new AdjustmentCreditNotePDF(data);
        });
    }
}
