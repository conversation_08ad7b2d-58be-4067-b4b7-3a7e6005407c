{"version": 3, "file": "noop-normalize.js", "sourceRoot": "", "sources": ["../../../../src/platform/browser/noop-normalize.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAE1C;;;;;;;;;;GAUG;AACH,MAAM,UAAU,SAAS,CAAC,IAAY;IACpC,IAAI,CAAC,IAAI,CACP,yOAAyO,CAC1O,CAAC;IACF,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\n\n/**\n * Placeholder normalize function to replace the node variant in browser runtimes,\n * this should never be called and will perform a no-op and warn if it is called regardless.\n *\n * This is a workaround to fix https://github.com/open-telemetry/opentelemetry-js/issues/4373 until the instrumentation\n * package can be made node-only.\n *\n * @param path input path\n * @return unmodified path\n * @internal\n */\nexport function normalize(path: string): string {\n  diag.warn(\n    'Path normalization is not implemented for this platform. To silence this warning, ensure no node-specific instrumentations are loaded, and node-specific types (e.g. InstrumentationNodeModuleFile), are not used in a browser context)'\n  );\n  return path;\n}\n"]}