import { Event } from '../../../entities/events/event.js';
import { EventName } from '../../helpers/index.js';
import { AdjustmentNotification } from '../../entities/index.js';
export class AdjustmentUpdatedEvent extends Event {
    constructor(response) {
        super(response);
        this.eventType = EventName.AdjustmentUpdated;
        this.data = new AdjustmentNotification(response.data);
    }
}
