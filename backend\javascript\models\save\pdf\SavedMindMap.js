// node_gemini_summarizer/models/SavedMindMap.js
import mongoose from 'mongoose';

const SavedMindMapSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  generationTaskId: { // This is the ID from the generation process
    type: String,
    // required: true, // Make it optional if a mind map can be saved without a generation task ID (e.g., created from scratch or updated)
  },
  fileName: {
    type: String,
    required: true,
  },
  mindMapData: { // This contains nodes, edges, layoutType, language etc.
    type: mongoose.Schema.Types.Mixed,
    required: true,
  },
  thumbnailUrl: {
    type: String,
  },
  // NEW FIELD for preview on saved items page
  summary: {
    type: String,
    default: 'Mind map data.', // A sensible default
  },
  savedAt: {
    type: Date,
    default: Date.now,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

SavedMindMapSchema.virtual('id').get(function(){
    return this._id.toHexString(); // This gives the MongoDB document _id as 'id'
});

// If you want generationTaskId to be part of a unique key with userId:
// But note: if you update a mind map, generationTaskId might stay the same.
// A mind map should have its own unique _id from MongoDB.
// SavedMindMapSchema.index({ userId: 1, generationTaskId: 1 }, { unique: true });
// It's usually better to just rely on the unique _id generated by MongoDB.
// If you want to prevent duplicate saves from the *same generation task*, this index is fine.

export default mongoose.models.SavedMindMap || mongoose.model('SavedMindMap', SavedMindMapSchema);