{"version": 3, "file": "experimental_metrics.js", "sourceRoot": "", "sources": ["../../src/experimental_metrics.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAEH,4GAA4G;AAC5G,2GAA2G;AAC3G,4GAA4G;AAE5G;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,oBAA6B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,mBAA4B,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,6BAA6B,GAAG,wBAAiC,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;;;GAIG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;GAIG;AACU,QAAA,uCAAuC,GAAG,kCAA2C,CAAC;AAEnG;;;;GAIG;AACU,QAAA,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;GAIG;AACU,QAAA,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;GAIG;AACU,QAAA,+BAA+B,GAAG,0BAAmC,CAAC;AAEnF;;;;GAIG;AACU,QAAA,4CAA4C,GAAG,uCAAgD,CAAC;AAE7G;;;;GAIG;AACU,QAAA,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;GAIG;AACU,QAAA,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;GAIG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,wCAAwC,GAAG,mCAA4C,CAAC;AAErG;;;;;;GAMG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,gCAAgC,GAAG,2BAAoC,CAAC;AAErF;;;;;;GAMG;AACU,QAAA,6CAA6C,GAAG,wCAAiD,CAAC;AAE/G;;;;;;GAMG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;;;GAMG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,sCAAsC,GAAG,iCAA0C,CAAC;AAEjG;;;;;;GAMG;AACU,QAAA,mCAAmC,GAAG,8BAAuC,CAAC;AAE3F;;;;GAIG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;;;;;;GAOG;AACU,QAAA,4BAA4B,GAAG,uBAAgC,CAAC;AAE7E;;;;;;;GAOG;AACU,QAAA,wBAAwB,GAAG,mBAA4B,CAAC;AAErE;;;;;;;GAOG;AACU,QAAA,4BAA4B,GAAG,uBAAgC,CAAC;AAE7E;;;;;;;GAOG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;;;;GAOG;AACU,QAAA,wDAAwD,GAAG,mDAA4D,CAAC;AAErI;;;;;;;GAOG;AACU,QAAA,0CAA0C,GAAG,qCAA8C,CAAC;AAEzG;;;;;;;GAOG;AACU,QAAA,sDAAsD,GAAG,iDAA0D,CAAC;AAEjI;;;;;;;GAOG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;;;;;;GAOG;AACU,QAAA,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;;;;GAOG;AACU,QAAA,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;;;;GAOG;AACU,QAAA,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;;;;GAOG;AACU,QAAA,sCAAsC,GAAG,iCAA0C,CAAC;AAEjG;;;;;;;GAOG;AACU,QAAA,+BAA+B,GAAG,0BAAmC,CAAC;AAEnF;;;;;;;GAOG;AACU,QAAA,8BAA8B,GAAG,yBAAkC,CAAC;AAEjF;;;;;;;GAOG;AACU,QAAA,wCAAwC,GAAG,mCAA4C,CAAC;AAErG;;;;;;;GAOG;AACU,QAAA,sCAAsC,GAAG,iCAA0C,CAAC;AAEjG;;;;;;;GAOG;AACU,QAAA,sCAAsC,GAAG,iCAA0C,CAAC;AAEjG;;;;;;;GAOG;AACU,QAAA,yCAAyC,GAAG,oCAA6C,CAAC;AAEvG;;;;;;;GAOG;AACU,QAAA,yBAAyB,GAAG,oBAA6B,CAAC;AAEvE;;;;GAIG;AACU,QAAA,sBAAsB,GAAG,iBAA0B,CAAC;AAEjE;;;;GAIG;AACU,QAAA,qBAAqB,GAAG,gBAAyB,CAAC;AAE/D;;;;GAIG;AACU,QAAA,kBAAkB,GAAG,aAAsB,CAAC;AAEzD;;;;GAIG;AACU,QAAA,yBAAyB,GAAG,oBAA6B,CAAC;AAEvE;;;;GAIG;AACU,QAAA,uBAAuB,GAAG,kBAA2B,CAAC;AAEnE;;;;GAIG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;;;GAIG;AACU,QAAA,qBAAqB,GAAG,gBAAyB,CAAC;AAE/D;;;;GAIG;AACU,QAAA,kBAAkB,GAAG,aAAsB,CAAC;AAEzD;;;;GAIG;AACU,QAAA,oBAAoB,GAAG,eAAwB,CAAC;AAE7D;;;;GAIG;AACU,QAAA,uCAAuC,GAAG,kCAA2C,CAAC;AAEnG;;;;GAIG;AACU,QAAA,gCAAgC,GAAG,2BAAoC,CAAC;AAErF;;;;GAIG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;GAIG;AACU,QAAA,0CAA0C,GAAG,qCAA8C,CAAC;AAEzG;;;;GAIG;AACU,QAAA,wCAAwC,GAAG,mCAA4C,CAAC;AAErG;;;;;;GAMG;AACU,QAAA,qBAAqB,GAAG,gBAAyB,CAAC;AAE/D;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,oBAA6B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,uBAAgC,CAAC;AAE7E;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,mBAA4B,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,sBAAsB,GAAG,iBAA0B,CAAC;AAEjE;;;;;;GAMG;AACU,QAAA,qBAAqB,GAAG,gBAAyB,CAAC;AAE/D;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,oBAA6B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;;;GAIG;AACU,QAAA,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;GAIG;AACU,QAAA,sCAAsC,GAAG,iCAA0C,CAAC;AAEjG;;;;GAIG;AACU,QAAA,mCAAmC,GAAG,8BAAuC,CAAC;AAE3F;;;;;;GAMG;AACU,QAAA,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;;;GAMG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;GAIG;AACU,QAAA,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;;;GAMG;AACU,QAAA,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;;;GAMG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;GAIG;AACU,QAAA,gBAAgB,GAAG,WAAoB,CAAC;AAErD;;;;GAIG;AACU,QAAA,gBAAgB,GAAG,WAAoB,CAAC;AAErD;;;;;;GAMG;AACU,QAAA,eAAe,GAAG,UAAmB,CAAC;AAEnD;;;;;;GAMG;AACU,QAAA,gBAAgB,GAAG,WAAoB,CAAC;AAErD;;;;GAIG;AACU,QAAA,uBAAuB,GAAG,kBAA2B,CAAC;AAEnE;;;;GAIG;AACU,QAAA,8BAA8B,GAAG,yBAAkC,CAAC;AAEjF;;;;;;GAMG;AACU,QAAA,8BAA8B,GAAG,yBAAkC,CAAC;AAEjF;;;;GAIG;AACU,QAAA,6BAA6B,GAAG,wBAAiC,CAAC;AAE/E;;;;GAIG;AACU,QAAA,sBAAsB,GAAG,iBAA0B,CAAC;AAEjE;;;;;;GAMG;AACU,QAAA,6BAA6B,GAAG,wBAAiC,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,mBAA4B,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,oBAA6B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,uBAAgC,CAAC;AAE7E;;;;;;GAMG;AACU,QAAA,uBAAuB,GAAG,kBAA2B,CAAC;AAEnE;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,mBAA4B,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;;;;;;GAOG;AACU,QAAA,yCAAyC,GAAG,oCAA6C,CAAC;AAEvG;;;;;;GAMG;AACU,QAAA,0CAA0C,GAAG,qCAA8C,CAAC;AAEzG;;;;;;GAMG;AACU,QAAA,0CAA0C,GAAG,qCAA8C,CAAC;AAEzG;;;;;;GAMG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,uBAAgC,CAAC;AAE7E;;;;;;GAMG;AACU,QAAA,mCAAmC,GAAG,8BAAuC,CAAC;AAE3F;;;;GAIG;AACU,QAAA,+BAA+B,GAAG,0BAAmC,CAAC;AAEnF;;;;GAIG;AACU,QAAA,uBAAuB,GAAG,kBAA2B,CAAC;AAEnE;;;;GAIG;AACU,QAAA,8BAA8B,GAAG,yBAAkC,CAAC;AAEjF;;;;GAIG;AACU,QAAA,sBAAsB,GAAG,iBAA0B,CAAC;AAEjE;;;;GAIG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;;;GAIG;AACU,QAAA,6BAA6B,GAAG,wBAAiC,CAAC;AAE/E;;;;GAIG;AACU,QAAA,yBAAyB,GAAG,oBAA6B,CAAC;AAEvE;;;;GAIG;AACU,QAAA,yCAAyC,GAAG,oCAA6C,CAAC;AAEvG;;;;GAIG;AACU,QAAA,4BAA4B,GAAG,uBAAgC,CAAC;AAE7E;;;;GAIG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;;;;GAKG;AACU,QAAA,qBAAqB,GAAG,gBAAyB,CAAC;AAE/D;;;;;;;;;GASG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,8BAA8B,GAAG,yBAAkC,CAAC;AAEjF;;;;;;;;GAQG;AACU,QAAA,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;;;GAMG;AACU,QAAA,+BAA+B,GAAG,0BAAmC,CAAC;AAEnF;;;;;;;;GAQG;AACU,QAAA,mCAAmC,GAAG,8BAAuC,CAAC;AAE3F;;;;;;;;;GASG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,8BAA8B,GAAG,yBAAkC,CAAC;AAEjF;;;;;;;;GAQG;AACU,QAAA,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;;;GAMG;AACU,QAAA,+BAA+B,GAAG,0BAAmC,CAAC;AAEnF;;;;;;;;GAQG;AACU,QAAA,mCAAmC,GAAG,8BAAuC,CAAC;AAE3F;;;;GAIG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;;;GAIG;AACU,QAAA,+BAA+B,GAAG,0BAAmC,CAAC;AAEnF;;;;GAIG;AACU,QAAA,gCAAgC,GAAG,2BAAoC,CAAC;AAErF;;;;GAIG;AACU,QAAA,sBAAsB,GAAG,iBAA0B,CAAC;AAEjE;;;;GAIG;AACU,QAAA,6BAA6B,GAAG,wBAAiC,CAAC;AAE/E;;GAEG;AACU,QAAA,qBAAqB,GAAG,gBAAyB,CAAC;AAE/D;;;;;;;;;;;GAWG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;;;GAIG;AACU,QAAA,wBAAwB,GAAG,mBAA4B,CAAC;AAErE;;GAEG;AACU,QAAA,yBAAyB,GAAG,oBAA6B,CAAC;AAEvE;;;;;;;;;GASG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;GAEG;AACU,QAAA,6BAA6B,GAAG,wBAAiC,CAAC;AAE/E;;;;GAIG;AACU,QAAA,8BAA8B,GAAG,yBAAkC,CAAC;AAEjF;;;;;;;GAOG;AACU,QAAA,8BAA8B,GAAG,yBAAkC,CAAC;AAEjF;;GAEG;AACU,QAAA,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;;;;;;;GAUG;AACU,QAAA,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;;;;;GAQG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;;;;;;GAOG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;;;;;;GAOG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;GAEG;AACU,QAAA,gCAAgC,GAAG,2BAAoC,CAAC;AAErF;;GAEG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;;;;;GAUG;AACU,QAAA,6BAA6B,GAAG,wBAAiC,CAAC;AAE/E;;;;;;;;;;GAUG;AACU,QAAA,4BAA4B,GAAG,uBAAgC,CAAC;AAE7E;;GAEG;AACU,QAAA,wBAAwB,GAAG,mBAA4B,CAAC;AAErE;;GAEG;AACU,QAAA,6BAA6B,GAAG,wBAAiC,CAAC;AAE/E;;GAEG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;GAEG;AACU,QAAA,+BAA+B,GAAG,0BAAmC,CAAC;AAEnF;;;;GAIG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;GAEG;AACU,QAAA,gCAAgC,GAAG,2BAAoC,CAAC;AAErF;;;;GAIG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;;;GAIG;AACU,QAAA,6BAA6B,GAAG,wBAAiC,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,uBAAuB,GAAG,kBAA2B,CAAC;AAEnE;;;;;;GAMG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;;;GAMG;AACU,QAAA,6BAA6B,GAAG,wBAAiC,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,uBAAgC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n//----------------------------------------------------------------------------------------------------------\n// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates/register/stable/metrics.ts.j2\n//----------------------------------------------------------------------------------------------------------\n\n/**\n * Total CPU time consumed\n * \n * @note Total CPU time consumed by the specific container on all available CPU cores\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_CONTAINER_CPU_TIME = 'container.cpu.time' as const;\n\n/**\n * Container's CPU usage, measured in cpus. Range from 0 to the number of allocatable CPUs\n * \n * @note CPU usage of the specific container on all available CPU cores, averaged over the sample window\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_CONTAINER_CPU_USAGE = 'container.cpu.usage' as const;\n\n/**\n * Disk bytes for the container.\n * \n * @note The total number of bytes read/written successfully (aggregated from all disks).\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_CONTAINER_DISK_IO = 'container.disk.io' as const;\n\n/**\n * Memory usage of the container.\n * \n * @note Memory usage of the container.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_CONTAINER_MEMORY_USAGE = 'container.memory.usage' as const;\n\n/**\n * Network bytes for the container.\n * \n * @note The number of bytes sent/received on all network interfaces by the container.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_CONTAINER_NETWORK_IO = 'container.network.io' as const;\n\n/**\n * The number of connections that are currently in state described by the `state` attribute\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DB_CLIENT_CONNECTION_COUNT = 'db.client.connection.count' as const;\n\n/**\n * The time it took to create a new connection\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DB_CLIENT_CONNECTION_CREATE_TIME = 'db.client.connection.create_time' as const;\n\n/**\n * The maximum number of idle open connections allowed\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DB_CLIENT_CONNECTION_IDLE_MAX = 'db.client.connection.idle.max' as const;\n\n/**\n * The minimum number of idle open connections allowed\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DB_CLIENT_CONNECTION_IDLE_MIN = 'db.client.connection.idle.min' as const;\n\n/**\n * The maximum number of open connections allowed\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DB_CLIENT_CONNECTION_MAX = 'db.client.connection.max' as const;\n\n/**\n * The number of current pending requests for an open connection\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS = 'db.client.connection.pending_requests' as const;\n\n/**\n * The number of connection timeouts that have occurred trying to obtain a connection from the pool\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DB_CLIENT_CONNECTION_TIMEOUTS = 'db.client.connection.timeouts' as const;\n\n/**\n * The time between borrowing a connection and returning it to the pool\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DB_CLIENT_CONNECTION_USE_TIME = 'db.client.connection.use_time' as const;\n\n/**\n * The time it took to obtain an open connection from the pool\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DB_CLIENT_CONNECTION_WAIT_TIME = 'db.client.connection.wait_time' as const;\n\n/**\n * Deprecated, use `db.client.connection.create_time` instead. Note: the unit also changed from `ms` to `s`.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n * \n * @deprecated Replaced by `db.client.connection.create_time`. Note: the unit also changed from `ms` to `s`.\n */\nexport const METRIC_DB_CLIENT_CONNECTIONS_CREATE_TIME = 'db.client.connections.create_time' as const;\n\n/**\n * Deprecated, use `db.client.connection.idle.max` instead.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n * \n * @deprecated Replaced by `db.client.connection.idle.max`.\n */\nexport const METRIC_DB_CLIENT_CONNECTIONS_IDLE_MAX = 'db.client.connections.idle.max' as const;\n\n/**\n * Deprecated, use `db.client.connection.idle.min` instead.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n * \n * @deprecated Replaced by `db.client.connection.idle.min`.\n */\nexport const METRIC_DB_CLIENT_CONNECTIONS_IDLE_MIN = 'db.client.connections.idle.min' as const;\n\n/**\n * Deprecated, use `db.client.connection.max` instead.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n * \n * @deprecated Replaced by `db.client.connection.max`.\n */\nexport const METRIC_DB_CLIENT_CONNECTIONS_MAX = 'db.client.connections.max' as const;\n\n/**\n * Deprecated, use `db.client.connection.pending_requests` instead.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n * \n * @deprecated Replaced by `db.client.connection.pending_requests`.\n */\nexport const METRIC_DB_CLIENT_CONNECTIONS_PENDING_REQUESTS = 'db.client.connections.pending_requests' as const;\n\n/**\n * Deprecated, use `db.client.connection.timeouts` instead.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n * \n * @deprecated Replaced by `db.client.connection.timeouts`.\n */\nexport const METRIC_DB_CLIENT_CONNECTIONS_TIMEOUTS = 'db.client.connections.timeouts' as const;\n\n/**\n * Deprecated, use `db.client.connection.count` instead.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n * \n * @deprecated Replaced by `db.client.connection.count`.\n */\nexport const METRIC_DB_CLIENT_CONNECTIONS_USAGE = 'db.client.connections.usage' as const;\n\n/**\n * Deprecated, use `db.client.connection.use_time` instead. Note: the unit also changed from `ms` to `s`.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n * \n * @deprecated Replaced by `db.client.connection.use_time`. Note: the unit also changed from `ms` to `s`.\n */\nexport const METRIC_DB_CLIENT_CONNECTIONS_USE_TIME = 'db.client.connections.use_time' as const;\n\n/**\n * Deprecated, use `db.client.connection.wait_time` instead. Note: the unit also changed from `ms` to `s`.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n * \n * @deprecated Replaced by `db.client.connection.wait_time`. Note: the unit also changed from `ms` to `s`.\n */\nexport const METRIC_DB_CLIENT_CONNECTIONS_WAIT_TIME = 'db.client.connections.wait_time' as const;\n\n/**\n * Duration of database client operations.\n * \n * @note Batch operations **SHOULD** be recorded as a single operation.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DB_CLIENT_OPERATION_DURATION = 'db.client.operation.duration' as const;\n\n/**\n * Measures the time taken to perform a DNS lookup.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DNS_LOOKUP_DURATION = 'dns.lookup.duration' as const;\n\n/**\n * The number of .NET assemblies that are currently loaded.\n * \n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`AppDomain.CurrentDomain.GetAssemblies().Length`](https://learn.microsoft.com/dotnet/api/system.appdomain.getassemblies).\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DOTNET_ASSEMBLY_COUNT = 'dotnet.assembly.count' as const;\n\n/**\n * The number of exceptions that have been thrown in managed code.\n * \n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as counting calls to [`AppDomain.CurrentDomain.FirstChanceException`](https://learn.microsoft.com/dotnet/api/system.appdomain.firstchanceexception).\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DOTNET_EXCEPTIONS = 'dotnet.exceptions' as const;\n\n/**\n * The number of garbage collections that have occurred since the process has started.\n * \n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric uses the [`GC.CollectionCount(int generation)`](https://learn.microsoft.com/dotnet/api/system.gc.collectioncount) API to calculate exclusive collections per generation.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DOTNET_GC_COLLECTIONS = 'dotnet.gc.collections' as const;\n\n/**\n * The *approximate* number of bytes allocated on the managed GC heap since the process has started. The returned value does not include any native allocations.\n * \n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`GC.GetTotalAllocatedBytes()`](https://learn.microsoft.com/dotnet/api/system.gc.gettotalallocatedbytes).\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DOTNET_GC_HEAP_TOTAL_ALLOCATED = 'dotnet.gc.heap.total_allocated' as const;\n\n/**\n * The heap fragmentation, as observed during the latest garbage collection.\n * \n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`GC.GetGCMemoryInfo().GenerationInfo.FragmentationAfterBytes`](https://learn.microsoft.com/dotnet/api/system.gcgenerationinfo.fragmentationafterbytes).\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DOTNET_GC_LAST_COLLECTION_HEAP_FRAGMENTATION_SIZE = 'dotnet.gc.last_collection.heap.fragmentation.size' as const;\n\n/**\n * The managed GC heap size (including fragmentation), as observed during the latest garbage collection.\n * \n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`GC.GetGCMemoryInfo().GenerationInfo.SizeAfterBytes`](https://learn.microsoft.com/dotnet/api/system.gcgenerationinfo.sizeafterbytes).\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DOTNET_GC_LAST_COLLECTION_HEAP_SIZE = 'dotnet.gc.last_collection.heap.size' as const;\n\n/**\n * The amount of committed virtual memory in use by the .NET GC, as observed during the latest garbage collection.\n * \n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`GC.GetGCMemoryInfo().TotalCommittedBytes`](https://learn.microsoft.com/dotnet/api/system.gcmemoryinfo.totalcommittedbytes). Committed virtual memory may be larger than the heap size because it includes both memory for storing existing objects (the heap size) and some extra memory that is ready to handle newly allocated objects in the future.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DOTNET_GC_LAST_COLLECTION_MEMORY_COMMITTED_SIZE = 'dotnet.gc.last_collection.memory.committed_size' as const;\n\n/**\n * The total amount of time paused in GC since the process has started.\n * \n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`GC.GetTotalPauseDuration()`](https://learn.microsoft.com/dotnet/api/system.gc.gettotalpauseduration).\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DOTNET_GC_PAUSE_TIME = 'dotnet.gc.pause.time' as const;\n\n/**\n * The amount of time the JIT compiler has spent compiling methods since the process has started.\n * \n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`JitInfo.GetCompilationTime()`](https://learn.microsoft.com/dotnet/api/system.runtime.jitinfo.getcompilationtime).\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DOTNET_JIT_COMPILATION_TIME = 'dotnet.jit.compilation.time' as const;\n\n/**\n * Count of bytes of intermediate language that have been compiled since the process has started.\n * \n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`JitInfo.GetCompiledILBytes()`](https://learn.microsoft.com/dotnet/api/system.runtime.jitinfo.getcompiledilbytes).\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DOTNET_JIT_COMPILED_IL_SIZE = 'dotnet.jit.compiled_il.size' as const;\n\n/**\n * The number of times the JIT compiler (re)compiled methods since the process has started.\n * \n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`JitInfo.GetCompiledMethodCount()`](https://learn.microsoft.com/dotnet/api/system.runtime.jitinfo.getcompiledmethodcount).\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DOTNET_JIT_COMPILED_METHODS = 'dotnet.jit.compiled_methods' as const;\n\n/**\n * The number of times there was contention when trying to acquire a monitor lock since the process has started.\n * \n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`Monitor.LockContentionCount`](https://learn.microsoft.com/dotnet/api/system.threading.monitor.lockcontentioncount).\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DOTNET_MONITOR_LOCK_CONTENTIONS = 'dotnet.monitor.lock_contentions' as const;\n\n/**\n * The number of processors available to the process.\n * \n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as accessing [`Environment.ProcessorCount`](https://learn.microsoft.com/dotnet/api/system.environment.processorcount).\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DOTNET_PROCESS_CPU_COUNT = 'dotnet.process.cpu.count' as const;\n\n/**\n * CPU time used by the process.\n * \n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as accessing the corresponding processor time properties on [`System.Diagnostics.Process`](https://learn.microsoft.com/dotnet/api/system.diagnostics.process).\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DOTNET_PROCESS_CPU_TIME = 'dotnet.process.cpu.time' as const;\n\n/**\n * The number of bytes of physical memory mapped to the process context.\n * \n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`Environment.WorkingSet`](https://learn.microsoft.com/dotnet/api/system.environment.workingset).\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DOTNET_PROCESS_MEMORY_WORKING_SET = 'dotnet.process.memory.working_set' as const;\n\n/**\n * The number of work items that are currently queued to be processed by the thread pool.\n * \n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`ThreadPool.PendingWorkItemCount`](https://learn.microsoft.com/dotnet/api/system.threading.threadpool.pendingworkitemcount).\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DOTNET_THREAD_POOL_QUEUE_LENGTH = 'dotnet.thread_pool.queue.length' as const;\n\n/**\n * The number of thread pool threads that currently exist.\n * \n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`ThreadPool.ThreadCount`](https://learn.microsoft.com/dotnet/api/system.threading.threadpool.threadcount).\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DOTNET_THREAD_POOL_THREAD_COUNT = 'dotnet.thread_pool.thread.count' as const;\n\n/**\n * The number of work items that the thread pool has completed since the process has started.\n * \n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`ThreadPool.CompletedWorkItemCount`](https://learn.microsoft.com/dotnet/api/system.threading.threadpool.completedworkitemcount).\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DOTNET_THREAD_POOL_WORK_ITEM_COUNT = 'dotnet.thread_pool.work_item.count' as const;\n\n/**\n * The number of timer instances that are currently active.\n * \n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`Timer.ActiveCount`](https://learn.microsoft.com/dotnet/api/system.threading.timer.activecount).\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DOTNET_TIMER_COUNT = 'dotnet.timer.count' as const;\n\n/**\n * Number of invocation cold starts\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_FAAS_COLDSTARTS = 'faas.coldstarts' as const;\n\n/**\n * Distribution of CPU usage per invocation\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_FAAS_CPU_USAGE = 'faas.cpu_usage' as const;\n\n/**\n * Number of invocation errors\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_FAAS_ERRORS = 'faas.errors' as const;\n\n/**\n * Measures the duration of the function's initialization, such as a cold start\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_FAAS_INIT_DURATION = 'faas.init_duration' as const;\n\n/**\n * Number of successful invocations\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_FAAS_INVOCATIONS = 'faas.invocations' as const;\n\n/**\n * Measures the duration of the function's logic execution\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_FAAS_INVOKE_DURATION = 'faas.invoke_duration' as const;\n\n/**\n * Distribution of max memory usage per invocation\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_FAAS_MEM_USAGE = 'faas.mem_usage' as const;\n\n/**\n * Distribution of net I/O usage per invocation\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_FAAS_NET_IO = 'faas.net_io' as const;\n\n/**\n * Number of invocation timeouts\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_FAAS_TIMEOUTS = 'faas.timeouts' as const;\n\n/**\n * GenAI operation duration\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GEN_AI_CLIENT_OPERATION_DURATION = 'gen_ai.client.operation.duration' as const;\n\n/**\n * Measures number of input and output tokens used\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GEN_AI_CLIENT_TOKEN_USAGE = 'gen_ai.client.token.usage' as const;\n\n/**\n * Generative AI server request duration such as time-to-last byte or last output token\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GEN_AI_SERVER_REQUEST_DURATION = 'gen_ai.server.request.duration' as const;\n\n/**\n * Time per output token generated after the first token for successful responses\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GEN_AI_SERVER_TIME_PER_OUTPUT_TOKEN = 'gen_ai.server.time_per_output_token' as const;\n\n/**\n * Time to generate first token for successful responses\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GEN_AI_SERVER_TIME_TO_FIRST_TOKEN = 'gen_ai.server.time_to_first_token' as const;\n\n/**\n * Heap size target percentage configured by the user, otherwise 100.\n * \n * @note The value range is [0.0,100.0]. Computed from `/gc/gogc:percent`.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GO_CONFIG_GOGC = 'go.config.gogc' as const;\n\n/**\n * Count of live goroutines.\n * \n * @note Computed from `/sched/goroutines:goroutines`.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GO_GOROUTINE_COUNT = 'go.goroutine.count' as const;\n\n/**\n * Memory allocated to the heap by the application.\n * \n * @note Computed from `/gc/heap/allocs:bytes`.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GO_MEMORY_ALLOCATED = 'go.memory.allocated' as const;\n\n/**\n * Count of allocations to the heap by the application.\n * \n * @note Computed from `/gc/heap/allocs:objects`.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GO_MEMORY_ALLOCATIONS = 'go.memory.allocations' as const;\n\n/**\n * Heap size target for the end of the GC cycle.\n * \n * @note Computed from `/gc/heap/goal:bytes`.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GO_MEMORY_GC_GOAL = 'go.memory.gc.goal' as const;\n\n/**\n * Go runtime memory limit configured by the user, if a limit exists.\n * \n * @note Computed from `/gc/gomemlimit:bytes`. This metric is excluded if the limit obtained from the Go runtime is math.MaxInt64.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GO_MEMORY_LIMIT = 'go.memory.limit' as const;\n\n/**\n * Memory used by the Go runtime.\n * \n * @note Computed from `(/memory/classes/total:bytes - /memory/classes/heap/released:bytes)`.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GO_MEMORY_USED = 'go.memory.used' as const;\n\n/**\n * The number of OS threads that can execute user-level Go code simultaneously.\n * \n * @note Computed from `/sched/gomaxprocs:threads`.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GO_PROCESSOR_LIMIT = 'go.processor.limit' as const;\n\n/**\n * The time goroutines have spent in the scheduler in a runnable state before actually running.\n * \n * @note Computed from `/sched/latencies:seconds`. Bucket boundaries are provided by the runtime, and are subject to change.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GO_SCHEDULE_DURATION = 'go.schedule.duration' as const;\n\n/**\n * Number of active HTTP requests.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HTTP_CLIENT_ACTIVE_REQUESTS = 'http.client.active_requests' as const;\n\n/**\n * The duration of the successfully established outbound HTTP connections.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HTTP_CLIENT_CONNECTION_DURATION = 'http.client.connection.duration' as const;\n\n/**\n * Number of outbound HTTP connections that are currently active or idle on the client.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HTTP_CLIENT_OPEN_CONNECTIONS = 'http.client.open_connections' as const;\n\n/**\n * Size of HTTP client request bodies.\n * \n * @note The size of the request payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HTTP_CLIENT_REQUEST_BODY_SIZE = 'http.client.request.body.size' as const;\n\n/**\n * Size of HTTP client response bodies.\n * \n * @note The size of the response payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HTTP_CLIENT_RESPONSE_BODY_SIZE = 'http.client.response.body.size' as const;\n\n/**\n * Number of active HTTP server requests.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HTTP_SERVER_ACTIVE_REQUESTS = 'http.server.active_requests' as const;\n\n/**\n * Size of HTTP server request bodies.\n * \n * @note The size of the request payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HTTP_SERVER_REQUEST_BODY_SIZE = 'http.server.request.body.size' as const;\n\n/**\n * Size of HTTP server response bodies.\n * \n * @note The size of the response payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HTTP_SERVER_RESPONSE_BODY_SIZE = 'http.server.response.body.size' as const;\n\n/**\n * Energy consumed by the component\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HW_ENERGY = 'hw.energy' as const;\n\n/**\n * Number of errors encountered by the component\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HW_ERRORS = 'hw.errors' as const;\n\n/**\n * Instantaneous power consumed by the component\n * \n * @note It is recommended to report `hw.energy` instead of `hw.power` when possible.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HW_POWER = 'hw.power' as const;\n\n/**\n * Operational status: `1` (true) or `0` (false) for each of the possible states\n * \n * @note `hw.status` is currently specified as an *UpDownCounter* but would ideally be represented using a [*StateSet* as defined in OpenMetrics](https://github.com/OpenObservability/OpenMetrics/blob/main/specification/OpenMetrics.md#stateset). This semantic convention will be updated once *StateSet* is specified in OpenTelemetry. This planned change is not expected to have any consequence on the way users query their timeseries backend to retrieve the values of `hw.status` over time.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HW_STATUS = 'hw.status' as const;\n\n/**\n * Number of buffers in the pool.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_JVM_BUFFER_COUNT = 'jvm.buffer.count' as const;\n\n/**\n * Measure of total memory capacity of buffers.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_JVM_BUFFER_MEMORY_LIMIT = 'jvm.buffer.memory.limit' as const;\n\n/**\n * Deprecated, use `jvm.buffer.memory.used` instead.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n * \n * @deprecated Replaced by `jvm.buffer.memory.used`.\n */\nexport const METRIC_JVM_BUFFER_MEMORY_USAGE = 'jvm.buffer.memory.usage' as const;\n\n/**\n * Measure of memory used by buffers.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_JVM_BUFFER_MEMORY_USED = 'jvm.buffer.memory.used' as const;\n\n/**\n * Measure of initial memory requested.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_JVM_MEMORY_INIT = 'jvm.memory.init' as const;\n\n/**\n * Average CPU load of the whole system for the last minute as reported by the JVM.\n * \n * @note The value range is [0,n], where n is the number of CPU cores - or a negative number if the value is not available. This utilization is not defined as being for the specific interval since last measurement (unlike `system.cpu.utilization`). [Reference](https://docs.oracle.com/en/java/javase/17/docs/api/java.management/java/lang/management/OperatingSystemMXBean.html#getSystemLoadAverage()).\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_JVM_SYSTEM_CPU_LOAD_1M = 'jvm.system.cpu.load_1m' as const;\n\n/**\n * Recent CPU utilization for the whole system as reported by the JVM.\n * \n * @note The value range is [0.0,1.0]. This utilization is not defined as being for the specific interval since last measurement (unlike `system.cpu.utilization`). [Reference](https://docs.oracle.com/en/java/javase/17/docs/api/jdk.management/com/sun/management/OperatingSystemMXBean.html#getCpuLoad()).\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_JVM_SYSTEM_CPU_UTILIZATION = 'jvm.system.cpu.utilization' as const;\n\n/**\n * Total CPU time consumed\n * \n * @note Total CPU time consumed by the specific Node on all available CPU cores\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_NODE_CPU_TIME = 'k8s.node.cpu.time' as const;\n\n/**\n * Node's CPU usage, measured in cpus. Range from 0 to the number of allocatable CPUs\n * \n * @note CPU usage of the specific Node on all available CPU cores, averaged over the sample window\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_NODE_CPU_USAGE = 'k8s.node.cpu.usage' as const;\n\n/**\n * Memory usage of the Node\n * \n * @note Total memory usage of the Node\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_NODE_MEMORY_USAGE = 'k8s.node.memory.usage' as const;\n\n/**\n * Total CPU time consumed\n * \n * @note Total CPU time consumed by the specific Pod on all available CPU cores\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_POD_CPU_TIME = 'k8s.pod.cpu.time' as const;\n\n/**\n * Pod's CPU usage, measured in cpus. Range from 0 to the number of allocatable CPUs\n * \n * @note CPU usage of the specific Pod on all available CPU cores, averaged over the sample window\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_POD_CPU_USAGE = 'k8s.pod.cpu.usage' as const;\n\n/**\n * Memory usage of the Pod\n * \n * @note Total memory usage of the Pod\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_POD_MEMORY_USAGE = 'k8s.pod.memory.usage' as const;\n\n/**\n * Number of messages that were delivered to the application.\n * \n * @note Records the number of messages pulled from the broker or number of messages dispatched to the application in push-based scenarios.\n * The metric **SHOULD** be reported once per message delivery. For example, if receiving and processing operations are both instrumented for a single message delivery, this counter is incremented when the message is received and not reported when it is processed.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_MESSAGING_CLIENT_CONSUMED_MESSAGES = 'messaging.client.consumed.messages' as const;\n\n/**\n * Duration of messaging operation initiated by a producer or consumer client.\n * \n * @note This metric **SHOULD NOT** be used to report processing duration - processing duration is reported in `messaging.process.duration` metric.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_MESSAGING_CLIENT_OPERATION_DURATION = 'messaging.client.operation.duration' as const;\n\n/**\n * Deprecated. Use `messaging.client.sent.messages` instead.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n * \n * @deprecated Replaced by `messaging.client.sent.messages`.\n */\nexport const METRIC_MESSAGING_CLIENT_PUBLISHED_MESSAGES = 'messaging.client.published.messages' as const;\n\n/**\n * Number of messages producer attempted to send to the broker.\n * \n * @note This metric **MUST NOT** count messages that were created but haven't yet been sent.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_MESSAGING_CLIENT_SENT_MESSAGES = 'messaging.client.sent.messages' as const;\n\n/**\n * Duration of processing operation.\n * \n * @note This metric **MUST** be reported for operations with `messaging.operation.type` that matches `process`.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_MESSAGING_PROCESS_DURATION = 'messaging.process.duration' as const;\n\n/**\n * Deprecated. Use `messaging.client.consumed.messages` instead.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n * \n * @deprecated Replaced by `messaging.client.consumed.messages`.\n */\nexport const METRIC_MESSAGING_PROCESS_MESSAGES = 'messaging.process.messages' as const;\n\n/**\n * Deprecated. Use `messaging.client.operation.duration` instead.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n * \n * @deprecated Replaced by `messaging.client.operation.duration`.\n */\nexport const METRIC_MESSAGING_PUBLISH_DURATION = 'messaging.publish.duration' as const;\n\n/**\n * Deprecated. Use `messaging.client.produced.messages` instead.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n * \n * @deprecated Replaced by `messaging.client.produced.messages`.\n */\nexport const METRIC_MESSAGING_PUBLISH_MESSAGES = 'messaging.publish.messages' as const;\n\n/**\n * Deprecated. Use `messaging.client.operation.duration` instead.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n * \n * @deprecated Replaced by `messaging.client.operation.duration`.\n */\nexport const METRIC_MESSAGING_RECEIVE_DURATION = 'messaging.receive.duration' as const;\n\n/**\n * Deprecated. Use `messaging.client.consumed.messages` instead.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n * \n * @deprecated Replaced by `messaging.client.consumed.messages`.\n */\nexport const METRIC_MESSAGING_RECEIVE_MESSAGES = 'messaging.receive.messages' as const;\n\n/**\n * Event loop maximum delay.\n * \n * @note Value can be retrieved from value `histogram.max` of [`perf_hooks.monitorEventLoopDelay([options])`](https://nodejs.org/api/perf_hooks.html#perf_hooksmonitoreventloopdelayoptions)\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_NODEJS_EVENTLOOP_DELAY_MAX = 'nodejs.eventloop.delay.max' as const;\n\n/**\n * Event loop mean delay.\n * \n * @note Value can be retrieved from value `histogram.mean` of [`perf_hooks.monitorEventLoopDelay([options])`](https://nodejs.org/api/perf_hooks.html#perf_hooksmonitoreventloopdelayoptions)\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_NODEJS_EVENTLOOP_DELAY_MEAN = 'nodejs.eventloop.delay.mean' as const;\n\n/**\n * Event loop minimum delay.\n * \n * @note Value can be retrieved from value `histogram.min` of [`perf_hooks.monitorEventLoopDelay([options])`](https://nodejs.org/api/perf_hooks.html#perf_hooksmonitoreventloopdelayoptions)\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_NODEJS_EVENTLOOP_DELAY_MIN = 'nodejs.eventloop.delay.min' as const;\n\n/**\n * Event loop 50 percentile delay.\n * \n * @note Value can be retrieved from value `histogram.percentile(50)` of [`perf_hooks.monitorEventLoopDelay([options])`](https://nodejs.org/api/perf_hooks.html#perf_hooksmonitoreventloopdelayoptions)\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_NODEJS_EVENTLOOP_DELAY_P50 = 'nodejs.eventloop.delay.p50' as const;\n\n/**\n * Event loop 90 percentile delay.\n * \n * @note Value can be retrieved from value `histogram.percentile(90)` of [`perf_hooks.monitorEventLoopDelay([options])`](https://nodejs.org/api/perf_hooks.html#perf_hooksmonitoreventloopdelayoptions)\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_NODEJS_EVENTLOOP_DELAY_P90 = 'nodejs.eventloop.delay.p90' as const;\n\n/**\n * Event loop 99 percentile delay.\n * \n * @note Value can be retrieved from value `histogram.percentile(99)` of [`perf_hooks.monitorEventLoopDelay([options])`](https://nodejs.org/api/perf_hooks.html#perf_hooksmonitoreventloopdelayoptions)\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_NODEJS_EVENTLOOP_DELAY_P99 = 'nodejs.eventloop.delay.p99' as const;\n\n/**\n * Event loop standard deviation delay.\n * \n * @note Value can be retrieved from value `histogram.stddev` of [`perf_hooks.monitorEventLoopDelay([options])`](https://nodejs.org/api/perf_hooks.html#perf_hooksmonitoreventloopdelayoptions)\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_NODEJS_EVENTLOOP_DELAY_STDDEV = 'nodejs.eventloop.delay.stddev' as const;\n\n/**\n * Cumulative duration of time the event loop has been in each state.\n * \n * @note Value can be retrieved from [`performance.eventLoopUtilization([utilization1[, utilization2]])`](https://nodejs.org/api/perf_hooks.html#performanceeventlooputilizationutilization1-utilization2)\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_NODEJS_EVENTLOOP_TIME = 'nodejs.eventloop.time' as const;\n\n/**\n * Event loop utilization.\n * \n * @note The value range is [0.0, 1.0] and can be retrieved from [`performance.eventLoopUtilization([utilization1[, utilization2]])`](https://nodejs.org/api/perf_hooks.html#performanceeventlooputilizationutilization1-utilization2)\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_NODEJS_EVENTLOOP_UTILIZATION = 'nodejs.eventloop.utilization' as const;\n\n/**\n * Number of times the process has been context switched.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_PROCESS_CONTEXT_SWITCHES = 'process.context_switches' as const;\n\n/**\n * Total CPU seconds broken down by different states.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_PROCESS_CPU_TIME = 'process.cpu.time' as const;\n\n/**\n * Difference in process.cpu.time since the last measurement, divided by the elapsed time and number of CPUs available to the process.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_PROCESS_CPU_UTILIZATION = 'process.cpu.utilization' as const;\n\n/**\n * Disk bytes transferred.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_PROCESS_DISK_IO = 'process.disk.io' as const;\n\n/**\n * The amount of physical memory in use.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_PROCESS_MEMORY_USAGE = 'process.memory.usage' as const;\n\n/**\n * The amount of committed virtual memory.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_PROCESS_MEMORY_VIRTUAL = 'process.memory.virtual' as const;\n\n/**\n * Network bytes transferred.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_PROCESS_NETWORK_IO = 'process.network.io' as const;\n\n/**\n * Number of file descriptors in use by the process.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_PROCESS_OPEN_FILE_DESCRIPTOR_COUNT = 'process.open_file_descriptor.count' as const;\n\n/**\n * Number of page faults the process has made.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_PROCESS_PAGING_FAULTS = 'process.paging.faults' as const;\n\n/**\n * Process threads count.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_PROCESS_THREAD_COUNT = 'process.thread.count' as const;\n\n/**\n * The time the process has been running.\n * \n * @note Instrumentations **SHOULD** use counter with type `double` and measure uptime with at least millisecond precision\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_PROCESS_UPTIME = 'process.uptime' as const;\n\n/**\n * Measures the duration of outbound RPC.\n * \n * @note While streaming RPCs may record this metric as start-of-batch\n * to end-of-batch, it's hard to interpret in practice.\n * \n * **Streaming**: N/A.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_RPC_CLIENT_DURATION = 'rpc.client.duration' as const;\n\n/**\n * Measures the size of RPC request messages (uncompressed).\n * \n * @note **Streaming**: Recorded per message in a streaming batch\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_RPC_CLIENT_REQUEST_SIZE = 'rpc.client.request.size' as const;\n\n/**\n * Measures the number of messages received per RPC.\n * \n * @note Should be 1 for all non-streaming RPCs.\n * \n * **Streaming**: This metric is required for server and client streaming RPCs\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_RPC_CLIENT_REQUESTS_PER_RPC = 'rpc.client.requests_per_rpc' as const;\n\n/**\n * Measures the size of RPC response messages (uncompressed).\n * \n * @note **Streaming**: Recorded per response in a streaming batch\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_RPC_CLIENT_RESPONSE_SIZE = 'rpc.client.response.size' as const;\n\n/**\n * Measures the number of messages sent per RPC.\n * \n * @note Should be 1 for all non-streaming RPCs.\n * \n * **Streaming**: This metric is required for server and client streaming RPCs\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_RPC_CLIENT_RESPONSES_PER_RPC = 'rpc.client.responses_per_rpc' as const;\n\n/**\n * Measures the duration of inbound RPC.\n * \n * @note While streaming RPCs may record this metric as start-of-batch\n * to end-of-batch, it's hard to interpret in practice.\n * \n * **Streaming**: N/A.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_RPC_SERVER_DURATION = 'rpc.server.duration' as const;\n\n/**\n * Measures the size of RPC request messages (uncompressed).\n * \n * @note **Streaming**: Recorded per message in a streaming batch\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_RPC_SERVER_REQUEST_SIZE = 'rpc.server.request.size' as const;\n\n/**\n * Measures the number of messages received per RPC.\n * \n * @note Should be 1 for all non-streaming RPCs.\n * \n * **Streaming** : This metric is required for server and client streaming RPCs\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_RPC_SERVER_REQUESTS_PER_RPC = 'rpc.server.requests_per_rpc' as const;\n\n/**\n * Measures the size of RPC response messages (uncompressed).\n * \n * @note **Streaming**: Recorded per response in a streaming batch\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_RPC_SERVER_RESPONSE_SIZE = 'rpc.server.response.size' as const;\n\n/**\n * Measures the number of messages sent per RPC.\n * \n * @note Should be 1 for all non-streaming RPCs.\n * \n * **Streaming**: This metric is required for server and client streaming RPCs\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_RPC_SERVER_RESPONSES_PER_RPC = 'rpc.server.responses_per_rpc' as const;\n\n/**\n * Reports the current frequency of the CPU in Hz\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_CPU_FREQUENCY = 'system.cpu.frequency' as const;\n\n/**\n * Reports the number of logical (virtual) processor cores created by the operating system to manage multitasking\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_CPU_LOGICAL_COUNT = 'system.cpu.logical.count' as const;\n\n/**\n * Reports the number of actual physical processor cores on the hardware\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_CPU_PHYSICAL_COUNT = 'system.cpu.physical.count' as const;\n\n/**\n * Seconds each logical CPU spent on each mode\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_CPU_TIME = 'system.cpu.time' as const;\n\n/**\n * Difference in system.cpu.time since the last measurement, divided by the elapsed time and number of logical CPUs\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_CPU_UTILIZATION = 'system.cpu.utilization' as const;\n\n/**\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_DISK_IO = 'system.disk.io' as const;\n\n/**\n * Time disk spent activated\n * \n * @note The real elapsed time (\"wall clock\") used in the I/O path (time from operations running in parallel are not counted). Measured as:\n * \n *   - Linux: Field 13 from [procfs-diskstats](https://www.kernel.org/doc/Documentation/ABI/testing/procfs-diskstats)\n *   - Windows: The complement of\n *     [\"Disk% Idle Time\"](https://learn.microsoft.com/archive/blogs/askcore/windows-performance-monitor-disk-counters-explained#windows-performance-monitor-disk-counters-explained)\n *     performance counter: `uptime * (100 - \"Disk\\% Idle Time\") / 100`\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_DISK_IO_TIME = 'system.disk.io_time' as const;\n\n/**\n * The total storage capacity of the disk\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_DISK_LIMIT = 'system.disk.limit' as const;\n\n/**\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_DISK_MERGED = 'system.disk.merged' as const;\n\n/**\n * Sum of the time each operation took to complete\n * \n * @note Because it is the sum of time each request took, parallel-issued requests each contribute to make the count grow. Measured as:\n * \n *   - Linux: Fields 7 & 11 from [procfs-diskstats](https://www.kernel.org/doc/Documentation/ABI/testing/procfs-diskstats)\n *   - Windows: \"Avg. Disk sec/Read\" perf counter multiplied by \"Disk Reads/sec\" perf counter (similar for Writes)\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_DISK_OPERATION_TIME = 'system.disk.operation_time' as const;\n\n/**\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_DISK_OPERATIONS = 'system.disk.operations' as const;\n\n/**\n * The total storage capacity of the filesystem\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_FILESYSTEM_LIMIT = 'system.filesystem.limit' as const;\n\n/**\n * Reports a filesystem's space usage across different states.\n * \n * @note The sum of all `system.filesystem.usage` values over the different `system.filesystem.state` attributes\n * **SHOULD** equal the total storage capacity of the filesystem, that is `system.filesystem.limit`.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_FILESYSTEM_USAGE = 'system.filesystem.usage' as const;\n\n/**\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_FILESYSTEM_UTILIZATION = 'system.filesystem.utilization' as const;\n\n/**\n * An estimate of how much memory is available for starting new applications, without causing swapping\n * \n * @note This is an alternative to `system.memory.usage` metric with `state=free`.\n * Linux starting from 3.14 exports \"available\" memory. It takes \"free\" memory as a baseline, and then factors in kernel-specific values.\n * This is supposed to be more accurate than just \"free\" memory.\n * For reference, see the calculations [here](https://superuser.com/a/980821).\n * See also `MemAvailable` in [/proc/meminfo](https://man7.org/linux/man-pages/man5/proc.5.html).\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_LINUX_MEMORY_AVAILABLE = 'system.linux.memory.available' as const;\n\n/**\n * Reports the memory used by the Linux kernel for managing caches of frequently used objects.\n * \n * @note The sum over the `reclaimable` and `unreclaimable` state values in `linux.memory.slab.usage` **SHOULD** be equal to the total slab memory available on the system.\n * Note that the total slab memory is not constant and may vary over time.\n * See also the [Slab allocator](https://blogs.oracle.com/linux/post/understanding-linux-kernel-memory-statistics) and `Slab` in [/proc/meminfo](https://man7.org/linux/man-pages/man5/proc.5.html).\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_LINUX_MEMORY_SLAB_USAGE = 'system.linux.memory.slab.usage' as const;\n\n/**\n * Total memory available in the system.\n * \n * @note Its value **SHOULD** equal the sum of `system.memory.state` over all states.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_MEMORY_LIMIT = 'system.memory.limit' as const;\n\n/**\n * Shared memory used (mostly by tmpfs).\n * \n * @note Equivalent of `shared` from [`free` command](https://man7.org/linux/man-pages/man1/free.1.html) or\n * `Shmem` from [`/proc/meminfo`](https://man7.org/linux/man-pages/man5/proc.5.html)\"\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_MEMORY_SHARED = 'system.memory.shared' as const;\n\n/**\n * Reports memory in use by state.\n * \n * @note The sum over all `system.memory.state` values **SHOULD** equal the total memory\n * available on the system, that is `system.memory.limit`.\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_MEMORY_USAGE = 'system.memory.usage' as const;\n\n/**\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_MEMORY_UTILIZATION = 'system.memory.utilization' as const;\n\n/**\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_NETWORK_CONNECTIONS = 'system.network.connections' as const;\n\n/**\n * Count of packets that are dropped or discarded even though there was no error\n * \n * @note Measured as:\n * \n *   - Linux: the `drop` column in `/proc/dev/net` ([source](https://web.archive.org/web/20180321091318/http://www.onlamp.com/pub/a/linux/2000/11/16/LinuxAdmin.html))\n *   - Windows: [`InDiscards`/`OutDiscards`](https://docs.microsoft.com/windows/win32/api/netioapi/ns-netioapi-mib_if_row2)\n *     from [`GetIfEntry2`](https://docs.microsoft.com/windows/win32/api/netioapi/nf-netioapi-getifentry2)\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_NETWORK_DROPPED = 'system.network.dropped' as const;\n\n/**\n * Count of network errors detected\n * \n * @note Measured as:\n * \n *   - Linux: the `errs` column in `/proc/dev/net` ([source](https://web.archive.org/web/20180321091318/http://www.onlamp.com/pub/a/linux/2000/11/16/LinuxAdmin.html)).\n *   - Windows: [`InErrors`/`OutErrors`](https://docs.microsoft.com/windows/win32/api/netioapi/ns-netioapi-mib_if_row2)\n *     from [`GetIfEntry2`](https://docs.microsoft.com/windows/win32/api/netioapi/nf-netioapi-getifentry2).\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_NETWORK_ERRORS = 'system.network.errors' as const;\n\n/**\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_NETWORK_IO = 'system.network.io' as const;\n\n/**\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_NETWORK_PACKETS = 'system.network.packets' as const;\n\n/**\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_PAGING_FAULTS = 'system.paging.faults' as const;\n\n/**\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_PAGING_OPERATIONS = 'system.paging.operations' as const;\n\n/**\n * Unix swap or windows pagefile usage\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_PAGING_USAGE = 'system.paging.usage' as const;\n\n/**\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_PAGING_UTILIZATION = 'system.paging.utilization' as const;\n\n/**\n * Total number of processes in each state\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_PROCESS_COUNT = 'system.process.count' as const;\n\n/**\n * Total number of processes created over uptime of the host\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_PROCESS_CREATED = 'system.process.created' as const;\n\n/**\n * Garbage collection duration.\n * \n * @note The values can be retrieve from [`perf_hooks.PerformanceObserver(...).observe({ entryTypes: ['gc'] })`](https://nodejs.org/api/perf_hooks.html#performanceobserverobserveoptions)\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_V8JS_GC_DURATION = 'v8js.gc.duration' as const;\n\n/**\n * Heap space available size.\n * \n * @note Value can be retrieved from value `space_available_size` of [`v8.getHeapSpaceStatistics()`](https://nodejs.org/api/v8.html#v8getheapspacestatistics)\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_V8JS_HEAP_SPACE_AVAILABLE_SIZE = 'v8js.heap.space.available_size' as const;\n\n/**\n * Committed size of a heap space.\n * \n * @note Value can be retrieved from value `physical_space_size` of [`v8.getHeapSpaceStatistics()`](https://nodejs.org/api/v8.html#v8getheapspacestatistics)\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_V8JS_HEAP_SPACE_PHYSICAL_SIZE = 'v8js.heap.space.physical_size' as const;\n\n/**\n * Total heap memory size pre-allocated.\n * \n * @note The value can be retrieved from value `space_size` of [`v8.getHeapSpaceStatistics()`](https://nodejs.org/api/v8.html#v8getheapspacestatistics)\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_V8JS_MEMORY_HEAP_LIMIT = 'v8js.memory.heap.limit' as const;\n\n/**\n * Heap Memory size allocated.\n * \n * @note The value can be retrieved from value `space_used_size` of [`v8.getHeapSpaceStatistics()`](https://nodejs.org/api/v8.html#v8getheapspacestatistics)\n * \n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_V8JS_MEMORY_HEAP_USED = 'v8js.memory.heap.used' as const;\n\n"]}