// common/user/limit/incrementBusinessPlanCount.js
import User from '../../../../models/User.js';
import {
    ensureSubscription,
    FREE_TIER_PLAN_NAME_BACKEND,
    PRO_PLAN_NAME_BACKEND,
    getLimit,
} from './planConfig.js';

export const incrementBusinessPlanCount = async (req, res) => {
    try {
        let user = await User.findById(req.user.id);
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }
        user = ensureSubscription(user);

        const { subscription } = user;
        const { planName } = subscription;
        const now = new Date();

        if (planName === FREE_TIER_PLAN_NAME_BACKEND) {
            const limit = getLimit(planName, 'businessPlan');
            if (subscription.freeTierBusinessPlanCount >= limit) {
                return res.status(403).json({ message: 'Starter plan business plan limit reached. Please upgrade.' });
            }
            subscription.freeTierBusinessPlanCount++;
        } else if (planName === PRO_PLAN_NAME_BACKEND) {
            const limit = getLimit(planName, 'businessPlan');
            let lastReset = subscription.businessPlanMonthlyReset || new Date(0);

            // Check if a month has passed
            const resetDate = new Date(lastReset);
            resetDate.setMonth(resetDate.getMonth() + 1);

            if (now >= resetDate) {
                // If a month has passed, reset the count and the date
                subscription.proTierBusinessPlanCount = 1;
                subscription.businessPlanMonthlyReset = now;
            } else {
                // If still within the month, check the limit
                if (subscription.proTierBusinessPlanCount >= limit) {
                    return res.status(403).json({ message: `Pro plan limit of ${limit} business plans per month reached.` });
                }
                subscription.proTierBusinessPlanCount++;
            }
        } else {
            return res.status(400).json({ message: "User plan does not support this feature." });
        }

        await user.save();
        res.json({
            message: "Count successfully incremented.",
            subscription: user.subscription,
        });

    } catch (error) {
        console.error('Increment Business Plan Count Error:', error);
        res.status(500).json({ message: 'Server error while incrementing business plan count.' });
    }
};