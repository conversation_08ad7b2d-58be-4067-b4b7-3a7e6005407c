"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NodeCrypto = void 0;
const node_crypto_1 = require("node:crypto");
class NodeCrypto {
    randomUUID() {
        return (0, node_crypto_1.randomUUID)();
    }
    computeHmac(payload, secret) {
        return __awaiter(this, void 0, void 0, function* () {
            const hmac = (0, node_crypto_1.createHmac)('sha256', secret);
            hmac.update(payload);
            return yield new Promise((resolve) => {
                resolve(hmac.digest('hex'));
            });
        });
    }
}
exports.NodeCrypto = NodeCrypto;
