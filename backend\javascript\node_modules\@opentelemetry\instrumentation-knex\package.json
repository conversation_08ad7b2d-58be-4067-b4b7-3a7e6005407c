{"name": "@opentelemetry/instrumentation-knex", "version": "0.44.1", "description": "OpenTelemetry instrumentation for `knex` database SQL query builder", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"test": "nyc mocha 'test/**/*.ts'", "tdd": "yarn test -- --watch-extensions ts --watch", "clean": "rimraf build/*", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "prewatch": "npm run precompile", "version:update": "node ../../../scripts/version-update.js", "lint:readme": "node ../../../scripts/lint-readme", "compile": "tsc -p .", "prepublishOnly": "npm run compile", "watch": "tsc -w"}, "keywords": ["instrumentation", "knex", "nodejs", "opentelemetry", "tracing"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "devDependencies": {"@opentelemetry/api": "^1.3.0", "@opentelemetry/context-async-hooks": "^1.8.0", "@opentelemetry/sdk-trace-base": "^1.8.0", "@opentelemetry/sdk-trace-node": "^1.8.0", "@types/mocha": "7.0.2", "@types/node": "18.18.14", "better-sqlite3": "11.0.0", "knex": "3.1.0", "nyc": "15.1.0", "rimraf": "5.0.10", "sqlite3": "5.1.7", "typescript": "4.4.4"}, "dependencies": {"@opentelemetry/instrumentation": "^0.57.1", "@opentelemetry/semantic-conventions": "^1.27.0"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/opentelemetry-instrumentation-knex#readme", "gitHead": "1eb77007669bae87fe5664d68ba6533b95275d52"}