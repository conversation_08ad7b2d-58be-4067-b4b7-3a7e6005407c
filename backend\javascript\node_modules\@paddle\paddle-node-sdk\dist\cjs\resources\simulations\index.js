"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimulationsResource = void 0;
const index_js_1 = require("../../entities/index.js");
const index_js_2 = require("../../internal/base/index.js");
__exportStar(require("./operations/index.js"), exports);
const SimulationPaths = {
    list: '/simulations',
    create: '/simulations',
    get: '/simulations/{simulation_id}',
    update: '/simulations/{simulation_id}',
};
class SimulationsResource extends index_js_2.BaseResource {
    list(queryParams) {
        const queryParameters = new index_js_2.QueryParameters(queryParams);
        return new index_js_1.SimulationCollection(this.client, SimulationPaths.list + queryParameters.toQueryString());
    }
    create(createSimulationParameters) {
        return __awaiter(this, void 0, void 0, function* () {
            const response = yield this.client.post(SimulationPaths.create, createSimulationParameters);
            const data = this.handleResponse(response);
            return new index_js_1.Simulation(data);
        });
    }
    get(simulationId) {
        return __awaiter(this, void 0, void 0, function* () {
            const urlWithPathParams = new index_js_2.PathParameters(SimulationPaths.get, {
                simulation_id: simulationId,
            }).deriveUrl();
            const response = yield this.client.get(urlWithPathParams);
            const data = this.handleResponse(response);
            return new index_js_1.Simulation(data);
        });
    }
    update(simulationId, updateSimulation) {
        return __awaiter(this, void 0, void 0, function* () {
            const urlWithPathParams = new index_js_2.PathParameters(SimulationPaths.update, {
                simulation_id: simulationId,
            }).deriveUrl();
            const response = yield this.client.patch(urlWithPathParams, updateSimulation);
            const data = this.handleResponse(response);
            return new index_js_1.Simulation(data);
        });
    }
}
exports.SimulationsResource = SimulationsResource;
