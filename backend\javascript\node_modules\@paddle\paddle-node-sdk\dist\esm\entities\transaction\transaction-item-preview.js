import { Price } from '../price/index.js';
import { Proration } from './proration.js';
export class TransactionItemPreview {
    constructor(transactionItem) {
        var _a;
        this.price = transactionItem.price ? new Price(transactionItem.price) : null;
        this.quantity = transactionItem.quantity;
        this.includeInTotals = (_a = transactionItem.include_in_totals) !== null && _a !== void 0 ? _a : null;
        this.proration = transactionItem.proration ? new Proration(transactionItem.proration) : null;
    }
}
