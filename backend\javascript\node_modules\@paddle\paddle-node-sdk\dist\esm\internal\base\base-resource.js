import { ApiError } from '../errors/generic.js';
export class BaseResource {
    constructor(client) {
        this.client = client;
    }
    handleError(error) {
        if (error.error) {
            throw new ApiError(error.error);
        }
    }
    handleResponse(response) {
        const entityResponse = response;
        const error = response;
        this.handleError(error);
        return entityResponse.data;
    }
}
