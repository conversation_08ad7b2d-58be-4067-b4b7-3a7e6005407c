{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": "", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors, Aspecto\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { Span } from '@opentelemetry/api';\nimport { InstrumentationConfig } from '@opentelemetry/instrumentation';\n\nexport interface KafkajsMessage {\n  key?: Buffer | string | null;\n  value: Buffer | string | null;\n  partition?: number;\n  headers?: Record<string, Buffer | string | (Buffer | string)[] | undefined>;\n  timestamp?: string;\n}\n\nexport interface MessageInfo<T = KafkajsMessage> {\n  topic: string;\n  message: T;\n}\n\nexport interface KafkaProducerCustomAttributeFunction<T = KafkajsMessage> {\n  (span: Span, info: MessageInfo<T>): void;\n}\n\nexport interface KafkaConsumerCustomAttributeFunction<T = KafkajsMessage> {\n  (span: Span, info: MessageInfo<T>): void;\n}\n\nexport interface KafkaJsInstrumentationConfig extends InstrumentationConfig {\n  /** hook for adding custom attributes before producer message is sent */\n  producerHook?: KafkaProducerCustomAttributeFunction;\n\n  /** hook for adding custom attributes before consumer message is processed */\n  consumerHook?: KafkaConsumerCustomAttributeFunction;\n}\n"]}