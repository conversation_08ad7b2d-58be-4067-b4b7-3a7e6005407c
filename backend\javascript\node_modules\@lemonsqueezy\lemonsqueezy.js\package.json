{"name": "@lemonsqueezy/lemonsqueezy.js", "description": "The official Lemon Squeezy JavaScript SDK.", "version": "4.0.0", "author": {"name": "<PERSON>ezy", "email": "<EMAIL>", "url": "https://lemonsqueezy.com"}, "license": "MIT", "type": "module", "exports": {".": {"require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "sideEffects": false, "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "homepage": "https://lemonsqueezy.com", "repository": {"type": "git", "url": "https://github.com/lmsqueezy/lemonsqueezy.js.git"}, "publishConfig": {"access": "public"}, "bugs": {"url": "https://github.com/lmsqueezy/lemonsqueezy.js/issues"}, "keywords": ["api", "sdk", "lemonsqueezy", "javascript", "typescript"], "files": ["dist", "README.md"], "scripts": {"dev": "bun run --watch examples/index.ts", "build": "tsup", "coverage": "bun test --coverage", "export-size": "export-size .", "format": "prettier --write .", "lint:fix": "eslint 'src/**/*.{js,jsx,ts,tsx}' --fix", "lint": "eslint 'src/**/*.{js,jsx,ts,tsx}'", "test": "bun test", "typecheck": "tsc --noEmit", "version": "changeset version", "version:dev": "changeset version --snapshop --no-git-tag --tag dev", "release": "pnpm build && changeset publish", "release:dev": "pnpm build && changeset publish --snapshot --no-git-tag --tag dev"}, "devDependencies": {"@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.27.1", "@commitlint/cli": "^18.6.0", "@commitlint/config-conventional": "^19.2.2", "@types/bun": "latest", "@typescript-eslint/eslint-plugin": "^7.9.0", "@typescript-eslint/parser": "^7.9.0", "eslint": "^8.56.0", "export-size": "^0.7.0", "lint-staged": "^15.2.2", "simple-git-hooks": "^2.11.1", "prettier": "^3.2.5", "tsup": "^8.0.2", "typescript": "^5.4.5"}, "engines": {"node": ">=20"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint", "prettier --list-different"], "!(*.css|*.js|*.jsx|*.ts|*.tsx)": ["prettier --list-different"]}, "simple-git-hooks": {"pre-commit": "bunx lint-staged", "commit-msg": "bun run commitlint --edit $1"}}