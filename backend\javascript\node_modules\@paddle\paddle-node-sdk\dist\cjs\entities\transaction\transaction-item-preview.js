"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionItemPreview = void 0;
const index_js_1 = require("../price/index.js");
const proration_js_1 = require("./proration.js");
class TransactionItemPreview {
    constructor(transactionItem) {
        var _a;
        this.price = transactionItem.price ? new index_js_1.Price(transactionItem.price) : null;
        this.quantity = transactionItem.quantity;
        this.includeInTotals = (_a = transactionItem.include_in_totals) !== null && _a !== void 0 ? _a : null;
        this.proration = transactionItem.proration ? new proration_js_1.Proration(transactionItem.proration) : null;
    }
}
exports.TransactionItemPreview = TransactionItemPreview;
