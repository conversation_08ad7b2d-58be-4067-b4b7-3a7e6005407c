{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA0D;AAEnD,MAAM,OAAO,GAAG,CACrB,IAAU,EACV,GAA6C,EAC7C,EAAE;IACF,IAAI,GAAG,EAAE;QACP,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,SAAS,CAAC;YACb,IAAI,EAAE,oBAAc,CAAC,KAAK;YAC1B,OAAO,EAAE,GAAG,CAAC,OAAO;SACrB,CAAC,CAAC;KACJ;IACD,IAAI,CAAC,GAAG,EAAE,CAAC;AACb,CAAC,CAAC;AAZW,QAAA,OAAO,WAYlB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Span, SpanStatusCode } from '@opentelemetry/api';\n\nexport const endSpan = (\n  span: Span,\n  err: NodeJS.ErrnoException | null | undefined\n) => {\n  if (err) {\n    span.recordException(err);\n    span.setStatus({\n      code: SpanStatusCode.ERROR,\n      message: err.message,\n    });\n  }\n  span.end();\n};\n"]}