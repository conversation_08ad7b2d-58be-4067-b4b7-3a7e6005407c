// node_gemini_summarizer/controllers/auth/auth.helpers.js
import jwt from 'jsonwebtoken';
import { JWT_SECRET, JWT_EXPIRY_DURATION } from './auth.config.js'; // Ensure these are correctly defined in auth.config.js

export const generateOTP = () => {
  return Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit OTP
};

export const generateAuthToken = (user) => {
  const userId = user.id || user._id.toString();

  const payload = {
    id: userId,
  };
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRY_DURATION });
};

export const formatUserResponse = (user) => {
    const userObject = user.toObject ? user.toObject() : user; // Handle Mongoose doc or plain object
    return {
        id: userObject._id.toString(), // Ensure it's a string
        email: userObject.email,
        name: userObject.name,
        isVerified: userObject.isVerified,
        role: userObject.role, // <<< --- ADD THIS LINE ---
        subscription: userObject.subscription,
        // Do NOT include password or other sensitive fields
    };
};