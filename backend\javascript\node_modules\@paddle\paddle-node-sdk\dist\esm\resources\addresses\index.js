var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { BaseResource, PathParameters, QueryParameters } from '../../internal/base/index.js';
import { Address, AddressCollection } from '../../entities/index.js';
const AddressPaths = {
    list: '/customers/{customer_id}/addresses',
    create: '/customers/{customer_id}/addresses',
    get: '/customers/{customer_id}/addresses/{address_id}',
    update: '/customers/{customer_id}/addresses/{address_id}',
};
export * from './operations/index.js';
export class AddressesResource extends BaseResource {
    list(customerId, queryParams) {
        const queryParameters = new QueryParameters(queryParams);
        const urlWithPathParams = new PathParameters(AddressPaths.list, {
            customer_id: customerId,
        }).deriveUrl();
        return new AddressCollection(this.client, urlWithPathParams + queryParameters.toQueryString());
    }
    create(customerId, createAddressParameters) {
        return __awaiter(this, void 0, void 0, function* () {
            const urlWithPathParams = new PathParameters(AddressPaths.create, {
                customer_id: customerId,
            }).deriveUrl();
            const response = yield this.client.post(urlWithPathParams, createAddressParameters);
            const data = this.handleResponse(response);
            return new Address(data);
        });
    }
    get(customerId, addressId) {
        return __awaiter(this, void 0, void 0, function* () {
            const urlWithPathParams = new PathParameters(AddressPaths.get, {
                customer_id: customerId,
                address_id: addressId,
            }).deriveUrl();
            const response = yield this.client.get(urlWithPathParams);
            const data = this.handleResponse(response);
            return new Address(data);
        });
    }
    update(customerId, addressId, updateAddress) {
        return __awaiter(this, void 0, void 0, function* () {
            const urlWithPathParams = new PathParameters(AddressPaths.update, {
                customer_id: customerId,
                address_id: addressId,
            }).deriveUrl();
            const response = yield this.client.patch(urlWithPathParams, updateAddress);
            const data = this.handleResponse(response);
            return new Address(data);
        });
    }
    archive(customerId, addressId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.update(customerId, addressId, { status: 'archived' });
        });
    }
}
