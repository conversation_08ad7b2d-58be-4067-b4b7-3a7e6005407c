export class SubscriptionCreationOptions {
    constructor(options) {
        var _a, _b, _c;
        this.customerSimulatedAs = (_a = options === null || options === void 0 ? void 0 : options.customer_simulated_as) !== null && _a !== void 0 ? _a : null;
        this.businessSimulatedAs = (_b = options === null || options === void 0 ? void 0 : options.business_simulated_as) !== null && _b !== void 0 ? _b : null;
        this.discountSimulatedAs = (_c = options === null || options === void 0 ? void 0 : options.discount_simulated_as) !== null && _c !== void 0 ? _c : null;
    }
}
