{"version": 3, "file": "sql-common.test.js", "sourceRoot": "", "sources": ["../../test/sql-common.test.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;AAEH,iCAAiC;AACjC,4CAM4B;AAC5B,wCAAsD;AAEtD,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;IACtC,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;QACxC,MAAM,WAAW,GAAgB;YAC/B,OAAO,EAAE,kCAAkC;YAC3C,MAAM,EAAE,kBAAkB;YAC1B,UAAU,EAAE,gBAAU,CAAC,OAAO;SAC/B,CAAC;QAEF,MAAM,KAAK,GAAG,oBAAoB,CAAC;QACnC,MAAM,CAAC,WAAW,CAChB,IAAA,8BAAsB,EAAC,WAAK,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE,KAAK,CAAC,EACjE,8FAA8F,CAC/F,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;QAC/D,MAAM,IAAI,GAAG,WAAK,CAAC,eAAe,CAAC;YACjC,OAAO,EAAE,kCAAkC;YAC3C,MAAM,EAAE,kBAAkB;YAC1B,UAAU,EAAE,gBAAU,CAAC,OAAO;SAC/B,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,uCAAuC,CAAC;QAC7D,MAAM,CAAC,WAAW,CAChB,IAAA,8BAAsB,EAAC,IAAI,EAAE,YAAY,CAAC,EAC1C,YAAY,CACb,CAAC;QAEF,MAAM,aAAa,GAAG,oCAAoC,CAAC;QAC3D,MAAM,CAAC,WAAW,CAChB,IAAA,8BAAsB,EAAC,IAAI,EAAE,aAAa,CAAC,EAC3C,aAAa,CACd,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;QAClD,MAAM,WAAW,GAAgB;YAC/B,OAAO,EAAE,kCAAkC;YAC3C,MAAM,EAAE,kBAAkB;YAC1B,UAAU,EAAE,gBAAU,CAAC,OAAO;SAC/B,CAAC;QAEF,MAAM,CAAC,WAAW,CAChB,IAAA,8BAAsB,EAAC,WAAK,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE,EAAE,CAAC,EAC9D,EAAE,CACH,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;QAC3D,MAAM,KAAK,GAAG,oBAAoB,CAAC;QACnC,MAAM,CAAC,WAAW,CAChB,IAAA,8BAAsB,EACpB,WAAK,CAAC,eAAe,CAAC,0BAAoB,CAAC,EAC3C,KAAK,CACN,EACD,KAAK,CACN,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;QACzC,MAAM,WAAW,GAAgB;YAC/B,OAAO,EAAE,kCAAkC;YAC3C,MAAM,EAAE,kBAAkB;YAC1B,UAAU,EAAE,gBAAU,CAAC,OAAO;YAC9B,UAAU,EAAE,IAAA,sBAAgB,EAAC,iBAAiB,CAAC;SAChD,CAAC;QAEF,MAAM,KAAK,GAAG,oBAAoB,CAAC;QACnC,MAAM,CAAC,WAAW,CAChB,IAAA,8BAAsB,EAAC,WAAK,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE,KAAK,CAAC,EACjE,iIAAiI,CAClI,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;QAC9C,MAAM,WAAW,GAAgB;YAC/B,OAAO,EAAE,kCAAkC;YAC3C,MAAM,EAAE,kBAAkB;YAC1B,UAAU,EAAE,gBAAU,CAAC,OAAO;YAC9B,UAAU,EAAE,IAAA,sBAAgB,EAAC,yCAAyC,CAAC;SACxE,CAAC;QAEF,MAAM,KAAK,GAAG,oBAAoB,CAAC;QACnC,MAAM,CAAC,WAAW,CAChB,IAAA,8BAAsB,EAAC,WAAK,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE,KAAK,CAAC,EACjE,+KAA+K,CAChL,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as assert from 'assert';\nimport {\n  trace,\n  SpanContext,\n  TraceFlags,\n  INVALID_SPAN_CONTEXT,\n  createTraceState,\n} from '@opentelemetry/api';\nimport { addSqlCommenterComment } from '../src/index';\n\ndescribe('addSqlCommenterComment', () => {\n  it('adds comment to a simple query', () => {\n    const spanContext: SpanContext = {\n      traceId: 'd4cda95b652f4a1592b449d5929fda1b',\n      spanId: '6e0c63257de34c92',\n      traceFlags: TraceFlags.SAMPLED,\n    };\n\n    const query = 'SELECT * from FOO;';\n    assert.strictEqual(\n      addSqlCommenterComment(trace.wrapSpanContext(spanContext), query),\n      \"SELECT * from FOO; /*traceparent='00-d4cda95b652f4a1592b449d5929fda1b-6e0c63257de34c92-01'*/\"\n    );\n  });\n\n  it('does not add a comment if query already has a comment', () => {\n    const span = trace.wrapSpanContext({\n      traceId: 'd4cda95b652f4a1592b449d5929fda1b',\n      spanId: '6e0c63257de34c92',\n      traceFlags: TraceFlags.SAMPLED,\n    });\n\n    const blockComment = 'SELECT * from FOO; /* Test comment */';\n    assert.strictEqual(\n      addSqlCommenterComment(span, blockComment),\n      blockComment\n    );\n\n    const dashedComment = 'SELECT * from FOO; -- Test comment';\n    assert.strictEqual(\n      addSqlCommenterComment(span, dashedComment),\n      dashedComment\n    );\n  });\n\n  it('does not add a comment to an empty query', () => {\n    const spanContext: SpanContext = {\n      traceId: 'd4cda95b652f4a1592b449d5929fda1b',\n      spanId: '6e0c63257de34c92',\n      traceFlags: TraceFlags.SAMPLED,\n    };\n\n    assert.strictEqual(\n      addSqlCommenterComment(trace.wrapSpanContext(spanContext), ''),\n      ''\n    );\n  });\n\n  it('does not add a comment if span context is invalid', () => {\n    const query = 'SELECT * from FOO;';\n    assert.strictEqual(\n      addSqlCommenterComment(\n        trace.wrapSpanContext(INVALID_SPAN_CONTEXT),\n        query\n      ),\n      query\n    );\n  });\n\n  it('correctly also sets trace state', () => {\n    const spanContext: SpanContext = {\n      traceId: 'd4cda95b652f4a1592b449d5929fda1b',\n      spanId: '6e0c63257de34c92',\n      traceFlags: TraceFlags.SAMPLED,\n      traceState: createTraceState('foo=bar,baz=qux'),\n    };\n\n    const query = 'SELECT * from FOO;';\n    assert.strictEqual(\n      addSqlCommenterComment(trace.wrapSpanContext(spanContext), query),\n      \"SELECT * from FOO; /*traceparent='00-d4cda95b652f4a1592b449d5929fda1b-6e0c63257de34c92-01',tracestate='foo%3Dbar%2Cbaz%3Dqux'*/\"\n    );\n  });\n\n  it('escapes special characters in values', () => {\n    const spanContext: SpanContext = {\n      traceId: 'd4cda95b652f4a1592b449d5929fda1b',\n      spanId: '6e0c63257de34c92',\n      traceFlags: TraceFlags.SAMPLED,\n      traceState: createTraceState(\"foo='bar,baz='qux!()*',hack='DROP TABLE\"),\n    };\n\n    const query = 'SELECT * from FOO;';\n    assert.strictEqual(\n      addSqlCommenterComment(trace.wrapSpanContext(spanContext), query),\n      \"SELECT * from FOO; /*traceparent='00-d4cda95b652f4a1592b449d5929fda1b-6e0c63257de34c92-01',tracestate='foo%3D%27bar%2Cbaz%3D%27qux%21%28%29%2A%27%2Chack%3D%27DROP%20TABLE'*/\"\n    );\n  });\n});\n"]}