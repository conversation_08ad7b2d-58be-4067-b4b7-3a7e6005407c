/**
 * Placeholder normalize function to replace the node variant in browser runtimes,
 * this should never be called and will perform a no-op and warn if it is called regardless.
 *
 * This is a workaround to fix https://github.com/open-telemetry/opentelemetry-js/issues/4373 until the instrumentation
 * package can be made node-only.
 *
 * @param path input path
 * @return unmodified path
 * @internal
 */
export declare function normalize(path: string): string;
//# sourceMappingURL=noop-normalize.d.ts.map