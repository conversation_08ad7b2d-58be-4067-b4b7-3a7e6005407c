// File Path: /backend/javascript/config/businessPlanUploadConfig.js
import multer from 'multer';
import fs from 'node:fs/promises';
import { UPLOAD_DIR } from './appConfig.js';
import path from 'path';

const businessPlanStorage = multer.diskStorage({
    destination: async (req, file, cb) => {
        try {
            const businessPlanDir = path.join(UPLOAD_DIR, 'business-plans');
            await fs.mkdir(businessPlanDir, { recursive: true });
            cb(null, businessPlanDir);
        } catch (error) {
            console.error("Multer: Error creating business plan upload directory:", error);
            cb(error);
        }
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const safeOriginalName = file.originalname.replace(/[^a-zA-Z0-9_.-]/g, '_');
        cb(null, uniqueSuffix + '-' + safeOriginalName);
    }
});

const businessPlanUpload = multer({
    storage: businessPlanStorage,
    limits: { fileSize: 10 * 1024 * 1024 }, // 10MB
    fileFilter: (req, file, cb) => {
        const allowedMimeTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ];
        
        if (allowedMimeTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error("File type not allowed. Only PDF, DOC, and DOCX files are accepted."), false);
        }
    }
});

export default businessPlanUpload;
