// models/AdminSettings.js
import mongoose from 'mongoose';

/**
 * Schema to track admin registration settings and system configuration
 */
const AdminSettingsSchema = new mongoose.Schema({
  // Track if initial admin has been registered
  hasInitialAdmin: {
    type: Boolean,
    default: false,
  },
  // Email of the first admin (system administrator)
  systemAdminEmail: {
    type: String,
    default: null,
  },
  // Date when first admin was registered
  initialAdminRegisteredAt: {
    type: Date,
    default: null,
  },
  // System settings
  allowNewAdminRegistration: {
    type: Boolean,
    default: true, // Initially allow admin registration
  },
  // Other system-wide settings can be added here
  systemName: {
    type: String,
    default: 'DOSKY Admin System',
  },
  maintenanceMode: {
    type: Boolean,
    default: false,
  },
}, { 
  timestamps: true,
  // Ensure only one document exists
  collection: 'admin_settings'
});

// Static method to get or create the singleton settings document
AdminSettingsSchema.statics.getSettings = async function() {
  let settings = await this.findOne();
  if (!settings) {
    settings = await this.create({});
  }
  return settings;
};

// Static method to check if admin registration is allowed
AdminSettingsSchema.statics.canRegisterAdmin = async function() {
  const settings = await this.getSettings();
  return !settings.hasInitialAdmin || settings.allowNewAdminRegistration;
};

// Static method to mark initial admin as registered
AdminSettingsSchema.statics.markInitialAdminRegistered = async function(adminEmail) {
  const settings = await this.getSettings();
  if (!settings.hasInitialAdmin) {
    settings.hasInitialAdmin = true;
    settings.systemAdminEmail = adminEmail;
    settings.initialAdminRegisteredAt = new Date();
    settings.allowNewAdminRegistration = false; // Disable further admin registration by default
    await settings.save();
  }
  return settings;
};

const AdminSettings = mongoose.model('AdminSettings', AdminSettingsSchema);

export default AdminSettings;
