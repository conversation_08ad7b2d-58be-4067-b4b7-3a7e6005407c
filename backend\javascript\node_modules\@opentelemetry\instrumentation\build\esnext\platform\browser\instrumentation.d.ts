import { InstrumentationAbstract } from '../../instrumentation';
import * as types from '../../types';
import { InstrumentationConfig } from '../../types';
/**
 * Base abstract class for instrumenting web plugins
 */
export declare abstract class InstrumentationBase<ConfigType extends InstrumentationConfig = InstrumentationConfig> extends InstrumentationAbstract<ConfigType> implements types.Instrumentation<ConfigType> {
    constructor(instrumentationName: string, instrumentationVersion: string, config: ConfigType);
}
//# sourceMappingURL=instrumentation.d.ts.map