// node_gemini_summarizer/controllers/auth/auth.login.js
import User from '../../../models/User.js';
import { generateAuthToken, formatUserResponse } from './auth.helpers.js';
// bcrypt is used via user.comparePassword() in the User model

export const loginUser = async (req, res) => {
  const { email, password } = req.body;

  if (!email || !password) {
    return res.status(400).json({ error: 'Please provide email and password.' });
  }

  try {
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials. User not found.' });
    }

    const isMatch = await user.comparePassword(password); // Assumes comparePassword method exists on User model
    if (!isMatch) {
      return res.status(401).json({ error: 'Invalid credentials. Password incorrect.' });
    }

    if (!user.isVerified) {
      return res.status(403).json({
          error: 'Email not verified. Please verify your email before logging in.',
          emailNotVerified: true,
          userEmail: user.email
      });
    }

    const token = generateAuthToken(user);

    res.json({
        token,
        user: formatUserResponse(user)
    });

  } catch (error) {
    console.error('Login error:', error.message, error.stack);
    res.status(500).json({ error: 'Server error during login.' });
  }
};