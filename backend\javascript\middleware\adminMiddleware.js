// middleware/adminMiddleware.js
import jwt from 'jsonwebtoken';
import User from '../models/User.js';

/**
 * Middleware to protect admin-only routes
 * This middleware should be used after the protect middleware (authMiddleware.js)
 * to ensure the user is authenticated and has admin privileges
 */
export const requireAdmin = async (req, res, next) => {
    try {
        // Check if user is authenticated (should be set by protect middleware)
        if (!req.user) {
            return res.status(401).json({ 
                error: 'Authentication required. Please log in first.' 
            });
        }

        // Check if user has admin privileges
        if (!req.user.isAdmin) {
            return res.status(403).json({ 
                error: 'Access denied. Admin privileges required.' 
            });
        }

        // User is authenticated and has admin privileges
        next();
    } catch (error) {
        console.error('Admin middleware error:', error);
        return res.status(500).json({ 
            error: 'Server error during admin authorization check.' 
        });
    }
};

/**
 * Combined middleware that checks both authentication and admin status
 * Can be used as a single middleware instead of chaining protect + requireAdmin
 */
export const protectAdmin = async (req, res, next) => {
    let token;

    try {
        // Check for token in Authorization header
        if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
            token = req.headers.authorization.split(' ')[1];
        }

        if (!token) {
            return res.status(401).json({ 
                error: 'Not authorized, no token provided.' 
            });
        }

        // Verify the token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);

        if (!decoded.id) {
            return res.status(401).json({ 
                error: 'Not authorized, invalid token format.' 
            });
        }

        // Fetch the user from the database
        const user = await User.findById(decoded.id).select('-password');

        if (!user) {
            return res.status(401).json({ 
                error: 'Not authorized, user not found.' 
            });
        }

        // Check if user has admin privileges
        if (!user.isAdmin) {
            return res.status(403).json({ 
                error: 'Access denied. Admin privileges required.' 
            });
        }

        // Attach user to request object
        req.user = user;
        next();
    } catch (error) {
        console.error('Admin protection error:', error);
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({ 
                error: 'Not authorized, invalid token.' 
            });
        }
        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({ 
                error: 'Not authorized, token expired.' 
            });
        }
        return res.status(500).json({ 
            error: 'Server error during admin authentication.' 
        });
    }
};
