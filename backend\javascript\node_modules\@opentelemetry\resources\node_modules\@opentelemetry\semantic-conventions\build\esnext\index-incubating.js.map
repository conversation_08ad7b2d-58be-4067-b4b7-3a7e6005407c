{"version": 3, "file": "index-incubating.js", "sourceRoot": "", "sources": ["../../src/index-incubating.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,wEAAwE;AACxE,wDAAwD;AACxD,cAAc,qBAAqB,CAAC;AACpC,cAAc,kBAAkB,CAAC;AACjC,cAAc,2BAA2B,CAAC;AAC1C,cAAc,wBAAwB,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// Incubating export also contains stable constants in order to maintain\n// backward compatibility between minor version releases\nexport * from './stable_attributes';\nexport * from './stable_metrics';\nexport * from './experimental_attributes';\nexport * from './experimental_metrics';\n"]}