"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubscriptionScheduledChangeNotification = void 0;
class SubscriptionScheduledChangeNotification {
    constructor(subscriptionScheduledChange) {
        this.action = subscriptionScheduledChange.action;
        this.effectiveAt = subscriptionScheduledChange.effective_at;
        this.resumeAt = subscriptionScheduledChange.resume_at ? subscriptionScheduledChange.resume_at : null;
    }
}
exports.SubscriptionScheduledChangeNotification = SubscriptionScheduledChangeNotification;
