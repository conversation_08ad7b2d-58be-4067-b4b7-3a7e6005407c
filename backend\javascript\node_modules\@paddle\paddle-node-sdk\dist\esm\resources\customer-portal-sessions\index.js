var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { CustomerPortalSession } from '../../entities/index.js';
import { BaseResource, PathParameters } from '../../internal/base/index.js';
const CustomerPortalSessionPaths = {
    create: '/customers/{customer_id}/portal-sessions',
};
export class CustomerPortalSessionsResource extends BaseResource {
    create(customerId, subscriptionIds) {
        return __awaiter(this, void 0, void 0, function* () {
            const urlWithPathParams = new PathParameters(CustomerPortalSessionPaths.create, {
                customer_id: customerId,
            }).deriveUrl();
            const response = yield this.client.post(urlWithPathParams, {
                subscriptionIds,
            });
            const data = this.handleResponse(response);
            return new CustomerPortalSession(data);
        });
    }
}
