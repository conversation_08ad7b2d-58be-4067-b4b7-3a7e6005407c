// src/common/Navbar/NavigationBar.jsx
import React from 'react';
// --- MODIFICATION: Imported the LogOut icon and FiShield for admin dashboard ---
import { Sparkles, Menu, X, User as UserIcon, LogIn, LogOut } from 'lucide-react';
import { FiShield } from 'react-icons/fi';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { AnimatePresence } from 'framer-motion';

const NavigationBar = ({ isMenuOpen, setIsMenuOpen, onGetStartedClick }) => {
  const navigate = useNavigate();
  const { isAuthenticated, currentUser, logout } = useAuth();

  const handleLogout = () => {
    logout();
    setIsMenuOpen(false);
  };

  const handleMobileAuthClick = () => {
    setIsMenuOpen(false);
    if (onGetStartedClick) {
      onGetStartedClick();
    }
  };

  // A small component for the plan badge to keep the JSX clean
  const PlanBadge = ({ planName }) => {
    if (!planName) return null;
    return (
      <span className="bg-purple-500/20 text-purple-300 text-xs font-semibold px-2.5 py-1 rounded-full border border-purple-500/30">
        {planName} Plan
      </span>
    );
  };

  return (
    <nav className="sticky top-0 z-50 backdrop-blur-md bg-black/30 border-b border-white/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2" onClick={() => setIsMenuOpen(false)}>
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
              <Sparkles className="w-6 h-6 text-white" />
            </div>
            <span className="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              DOSKY
            </span>
          </Link>

          {/* --- MODIFICATION: Desktop Navigation for authenticated users is redesigned --- */}
          <div className="hidden md:flex items-center space-x-6 lg:space-x-8">
            <Link to="/pricing" className="text-gray-300 hover:text-white transition-colors">Pricing</Link>

            {/* Admin Dashboard Link - Only show for admin users */}
            {isAuthenticated && currentUser?.isAdmin && (
              <Link
                to="/admin/dashboard"
                className="text-gray-300 hover:text-white transition-colors flex items-center"
              >
                <FiShield className="w-4 h-4 mr-1" />
                Dashboard
              </Link>
            )}

            {isAuthenticated && currentUser ? (
              // NEW: Inline display for user info, plan, and logout. Replaces the dropdown.
              <div className="flex items-center space-x-4">
                <Link
                  to="/account"
                  className="flex items-center text-gray-300 hover:text-white transition-colors"
                  title="Go to your account"
                >
                  <UserIcon className="w-5 h-5 mr-2" />
                  <span className="font-medium">{currentUser.name || currentUser.email.split('@')[0]}</span>
                </Link>

                <PlanBadge planName={currentUser.subscription?.planName} />

                {/* Vertical separator */}
                <div className="h-4 w-px bg-slate-700"></div>

                <button
                  onClick={handleLogout}
                  className="flex items-center text-sm font-medium text-red-400 hover:text-red-300 transition-colors"
                  title="Logout"
                >
                  <LogOut className="w-4 h-4 mr-1.5" />
                  Logout
                </button>
              </div>

            ) : (
              // Unauthenticated user: Button to open the modal
              <button
                onClick={onGetStartedClick}
                className="flex items-center bg-gradient-to-r from-purple-500 to-pink-500 px-5 py-2 rounded-full hover:shadow-lg hover:shadow-purple-500/25 transition-all duration-300 text-sm font-medium"
              >
                <LogIn className="w-4 h-4 mr-2" />
                Login / Sign Up
              </button>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden text-white p-2"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label="Toggle menu"
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      {/* Mobile Menu (remains unchanged, provides clear actions for mobile users) */}
      <AnimatePresence>
        {isMenuOpen && (
          <div className="md:hidden absolute top-full left-0 right-0 bg-black/95 backdrop-blur-lg border-b border-white/10 shadow-xl">
            <div className="px-5 py-6 space-y-3">
              <Link to="/#features" className="block py-2 text-gray-300 hover:text-white transition-colors" onClick={() => setIsMenuOpen(false)}>Features</Link>
              <Link to="/pricing" className="block py-2 text-gray-300 hover:text-white transition-colors" onClick={() => setIsMenuOpen(false)}>Pricing</Link>
              <hr className="border-slate-700 my-2"/>
              {isAuthenticated && currentUser ? (
                <>
                  {currentUser.isAdmin && (
                    <Link
                      to="/admin/dashboard"
                      className="block py-2 text-gray-300 hover:text-white transition-colors flex items-center"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <FiShield className="w-4 h-4 mr-2" />
                      Admin Dashboard
                    </Link>
                  )}
                  <Link to="/account" className="block py-2 text-gray-300 hover:text-white transition-colors" onClick={() => setIsMenuOpen(false)}>My Account</Link>
                  <button onClick={handleLogout} className="w-full text-left block py-2 text-red-400 hover:text-red-300 transition-colors">
                    Logout
                  </button>
                </>
              ) : (
                <button
                  onClick={handleMobileAuthClick}
                  className="w-full text-center bg-gradient-to-r from-purple-500 to-pink-500 px-6 py-2.5 rounded-full hover:shadow-lg hover:shadow-purple-500/25 transition-all duration-300 text-sm font-medium"
                >
                  Login / Sign Up
                </button>
              )}
            </div>
          </div>
        )}
      </AnimatePresence>
    </nav>
  );
};

export default React.memo(NavigationBar);