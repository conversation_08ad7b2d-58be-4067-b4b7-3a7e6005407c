"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubscriptionResumeDetails = void 0;
const subscription_resume_entities_js_1 = require("./subscription-resume-entities.js");
const subscription_resume_options_js_1 = require("./subscription-resume-options.js");
class SubscriptionResumeDetails {
    constructor(config) {
        this.entities = config.entities ? new subscription_resume_entities_js_1.SubscriptionResumeEntities(config.entities) : null;
        this.options = config.options ? new subscription_resume_options_js_1.SubscriptionResumeOptions(config.options) : null;
    }
}
exports.SubscriptionResumeDetails = SubscriptionResumeDetails;
