{"version": 3, "file": "AnyValue.js", "sourceRoot": "", "sources": ["../../../src/types/AnyValue.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type AnyValueScalar = string | number | boolean;\n\nexport type AnyValueArray = Array<AnyValue>;\n\n/**\n * AnyValueMap is a map from string to AnyValue (attribute value or a nested map)\n */\nexport interface AnyValueMap {\n  [attributeKey: string]: AnyValue;\n}\n\n/**\n * AnyValue can be one of the following:\n * - a scalar value\n * - a byte array\n * - array of any value\n * - map from string to any value\n * - empty value\n */\nexport type AnyValue =\n  | AnyValueScalar\n  | Uint8Array\n  | AnyValueArray\n  | AnyValueMap\n  | null\n  | undefined;\n"]}