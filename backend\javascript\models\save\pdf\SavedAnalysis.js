// node_gemini_summarizer/models/SavedAnalysis.js
import mongoose from 'mongoose';

const SavedAnalysisSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  fileName: { type: String, required: true },
  summary: { type: String, required: true },
  language: { type: String, default: 'English' },
  isDeepDive: { type: Boolean, default: false },
  pdfType: { type: String, default: 'auto' },
  savedAt: { type: Date, default: Date.now },
}, {
  timestamps: true, // Adds createdAt and updatedAt
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

SavedAnalysisSchema.virtual('id').get(function(){
    return this._id.toHexString();
});

// FIX: Check if the model already exists before compiling it
export default mongoose.models.SavedAnalysis || mongoose.model('SavedAnalysis', SavedAnalysisSchema);