// models/SavedChat.js
import mongoose from 'mongoose';

const MessageSchema = new mongoose.Schema({
  id: { type: String, required: true }, // This is the frontend-generated ID
  text: { type: String, required: true },
  sender: { type: String, enum: ['user', 'ai', 'system'], required: true },
  isLoading: { type: Boolean, default: false },
  fullResponse: { type: String },
  // Add any other message properties you store
}, { _id: false }); // No separate _id for subdocuments unless needed for specific reasons

const SavedChatSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User', // Assuming you have a User model
    required: true,
  },
  sessionId: { // This is the sessionId from the chat page URL params
    type: String,
    required: true,
  },
  pdfName: {
    type: String,
    required: true,
  },
  messages: [MessageSchema],
  firstMessageTimestamp: { // Optional: Timestamp of the first message for sorting/display
    type: Date,
  },
  savedAt: { // Timestamp when this specific save action occurred or last updated
    type: Date,
    default: Date.now,
  }
}, {
  timestamps: true // Adds createdAt and updatedAt automatically by Mongoose
});

// Compound index to efficiently query by userId and sessionId, and ensure uniqueness
// if you want one saved record per user per chat session.
SavedChatSchema.index({ userId: 1, sessionId: 1 }, { unique: true });

// Virtual for 'id' to return '_id' as 'id'
SavedChatSchema.virtual('id').get(function() {
  return this._id.toHexString();
});

SavedChatSchema.set('toJSON', {
  virtuals: true,
  transform: (doc, ret) => { // Ensure _id is removed if virtual 'id' is present
    delete ret._id;
    delete ret.__v;
  }
});

export default mongoose.model('SavedChat', SavedChatSchema);