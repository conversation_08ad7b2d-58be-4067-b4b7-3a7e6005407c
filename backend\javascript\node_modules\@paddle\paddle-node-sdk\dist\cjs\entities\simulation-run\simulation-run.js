"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimulationRun = void 0;
const index_js_1 = require("../index.js");
class SimulationRun {
    constructor(simulationRunResponse) {
        var _a, _b;
        this.id = simulationRunResponse.id;
        this.status = simulationRunResponse.status;
        this.createdAt = simulationRunResponse.created_at;
        this.updatedAt = simulationRunResponse.updated_at;
        this.type = simulationRunResponse.type;
        this.events = (_b = (_a = simulationRunResponse.events) === null || _a === void 0 ? void 0 : _a.map((event) => new index_js_1.SimulationRunEvent(event))) !== null && _b !== void 0 ? _b : [];
    }
}
exports.SimulationRun = SimulationRun;
