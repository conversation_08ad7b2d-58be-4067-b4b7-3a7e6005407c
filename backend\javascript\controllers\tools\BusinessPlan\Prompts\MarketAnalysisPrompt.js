/**
 * Builds the "Detailed Market Analysis" part of the main prompt.
 * This function generates the competitive landscape, SWOT, and GTM strategy sections.
 * @param {object} params - The parameters object.
 * @param {string} params.idea - The user's core business idea.
 * @param {Array} marketData - The fetched market research data.
 * @returns {string} The generated prompt part.
 */
export const buildMarketAnalysisPromptPart = ({ idea }, marketData) => {
    const appNameInstruction = "[The App]";
    const marketDataString = marketData && marketData.length > 0
        ? JSON.stringify(marketData.map(d => ({ title: d.title, snippet: d.snippet })), null, 2)
        : "No specific market reports were found. Rely on your general industry knowledge.";

    // Instructions to ensure correct table formatting, especially for multiple items and RTL languages.
    const languageInstruction = `
~LANG_INSTRUCTION~ IMPORTANT: When generating table content, separate each cell with a comma (,) and ensure proper cell boundaries. Each table row should have exactly the same number of cells as the header row. Do not concatenate multiple values into a single cell.
`;

    const tableFormatInstruction = `
~TABLE_FORMAT~ Each table row must follow this exact format:
~T_[TAG]_R~ Cell1,Cell2,Cell3,Cell4,Cell5
Make sure each cell is separated by a single comma, and array-like content should be formatted as: ['item1', 'item2', 'item3']
`;

    const rtlInstruction = `
~RTL_FORMAT~ For Arabic content: Ensure each table cell is properly separated. When listing multiple items in a cell, use the format: ['العنصر الأول', 'العنصر الثاني', 'العنصر الثالث']
`;

    return `
${languageInstruction}
${tableFormatInstruction}
${rtlInstruction}
### PART 2: DETAILED MARKET ANALYSIS ###
**Supporting Market Data (for your context, may be empty):**
${marketDataString}

~H~ Market Viability & Competitive Landscape
~S~ 1. Quantitative Market Sizing
~P~ [Provide a quantitative estimation of the market size for "${idea}" with specific numbers (e.g., '$800 Million by 2027'). MUST include a source citation (e.g., 'according to Statista...'). This is mandatory.]
~G~ Tip: Use this market size data in your investor pitch deck to demonstrate ROI.
~S~ 2. User Behavioral Trends
~P~ [Analyze a key user behavior relevant to "${idea}", supported by a specific, sourced statistic (e.g., 'A Nielsen survey found that...'). This source citation is mandatory.]
~I~ [State the most important user behavior this app must excel at.]
~S~ 3. Competitive Landscape & Matrix
~P~ The following are 3 key competitors in this space. I have generated plausible, brandable English names for them.
~C~ [Competitor A]: [Description]
~C~ [Competitor B]: [Description]
~C~ [Competitor C]: [Description]
~T_INTRO~ The following matrix compares key features:
~T_H~ Feature,${appNameInstruction},Competitor A,Competitor B,Competitor C
~T_R~ [Relevant feature 1],true,false,true,false
~T_R~ [Relevant feature 2],true,true,true,false
~T_R~ [Relevant feature 3],true,true,false,false
~S~ 4. SWOT Analysis
~P~ A detailed SWOT analysis for the project:
~L~ Strengths: [List key strengths]
~L~ Weaknesses: [List key weaknesses]
~L~ Opportunities: [List key opportunities]
~L~ Threats: [List key threats]
~S~ 5. Go-to-Market (GTM) Strategy
~P~ [Propose a high-level GTM strategy with a target launch region and key channels.]
~I~ [State the single most important first step in this GTM strategy.]
    `;
};