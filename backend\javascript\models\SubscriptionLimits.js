// models/SubscriptionLimits.js
import mongoose from 'mongoose';

/**
 * Schema for storing subscription plan limits and configurations
 * This allows dynamic configuration of plan limits through the admin dashboard
 */
const SubscriptionLimitsSchema = new mongoose.Schema({
  // Plan identifier
  planName: {
    type: String,
    required: true,
    enum: ['Starter', 'Pro', 'Enterprise'],
    unique: true
  },
  
  // Display information
  displayName: {
    type: String,
    required: true
  },
  
  description: {
    type: String,
    default: ''
  },
  
  // Pricing information
  price: {
    monthly: {
      type: Number,
      default: 0
    },
    yearly: {
      type: Number,
      default: 0
    }
  },
  
  // Feature limits
  limits: {
    // PDF-related limits
    pdfUploads: {
      monthly: {
        type: Number,
        default: 0
      },
      total: {
        type: Number,
        default: -1 // -1 means unlimited
      }
    },
    
    // Business tools limits
    businessPlans: {
      monthly: {
        type: Number,
        default: 0
      },
      total: {
        type: Number,
        default: -1
      }
    },
    
    investorPitches: {
      monthly: {
        type: Number,
        default: 0
      },
      total: {
        type: Number,
        default: -1
      }
    },
    
    businessQA: {
      daily: {
        type: Number,
        default: 0
      },
      monthly: {
        type: Number,
        default: 0
      }
    },
    
    // Chat and messaging limits
    chatMessages: {
      daily: {
        type: Number,
        default: 0
      },
      monthly: {
        type: Number,
        default: 0
      }
    },
    
    // AI features limits
    aiCharacters: {
      total: {
        type: Number,
        default: 0
      }
    },
    
    // Storage limits (in MB)
    storage: {
      total: {
        type: Number,
        default: 100 // 100MB default
      }
    },
    
    // Advanced features
    advancedAnalytics: {
      type: Boolean,
      default: false
    },
    
    prioritySupport: {
      type: Boolean,
      default: false
    },
    
    customBranding: {
      type: Boolean,
      default: false
    }
  },
  
  // Plan status
  isActive: {
    type: Boolean,
    default: true
  },
  
  // Plan ordering for display
  sortOrder: {
    type: Number,
    default: 0
  }
}, { 
  timestamps: true,
  collection: 'subscription_limits'
});

// Static method to get default limits for a plan
SubscriptionLimitsSchema.statics.getDefaultLimits = function(planName) {
  const defaults = {
    'Starter': {
      displayName: 'Starter Plan',
      description: 'Perfect for individuals getting started',
      price: { monthly: 0, yearly: 0 },
      limits: {
        pdfUploads: { monthly: 5, total: -1 },
        businessPlans: { monthly: 3, total: -1 },
        investorPitches: { monthly: 3, total: -1 },
        businessQA: { daily: 5, monthly: 50 },
        chatMessages: { daily: 20, monthly: 200 },
        aiCharacters: { total: 2 },
        storage: { total: 100 },
        advancedAnalytics: false,
        prioritySupport: false,
        customBranding: false
      },
      sortOrder: 1
    },
    'Pro': {
      displayName: 'Pro Plan',
      description: 'For professionals and growing businesses',
      price: { monthly: 19.99, yearly: 199.99 },
      limits: {
        pdfUploads: { monthly: 25, total: -1 },
        businessPlans: { monthly: 15, total: -1 },
        investorPitches: { monthly: 15, total: -1 },
        businessQA: { daily: 25, monthly: 300 },
        chatMessages: { daily: 100, monthly: 1000 },
        aiCharacters: { total: 10 },
        storage: { total: 1000 },
        advancedAnalytics: true,
        prioritySupport: true,
        customBranding: false
      },
      sortOrder: 2
    },
    'Enterprise': {
      displayName: 'Enterprise Plan',
      description: 'For large organizations with custom needs',
      price: { monthly: 99.99, yearly: 999.99 },
      limits: {
        pdfUploads: { monthly: -1, total: -1 },
        businessPlans: { monthly: -1, total: -1 },
        investorPitches: { monthly: -1, total: -1 },
        businessQA: { daily: -1, monthly: -1 },
        chatMessages: { daily: -1, monthly: -1 },
        aiCharacters: { total: -1 },
        storage: { total: 10000 },
        advancedAnalytics: true,
        prioritySupport: true,
        customBranding: true
      },
      sortOrder: 3
    }
  };
  
  return defaults[planName] || defaults['Starter'];
};

// Static method to initialize default plans
SubscriptionLimitsSchema.statics.initializeDefaultPlans = async function() {
  const plans = ['Starter', 'Pro', 'Enterprise'];
  
  for (const planName of plans) {
    const existingPlan = await this.findOne({ planName });
    if (!existingPlan) {
      const defaultData = this.getDefaultLimits(planName);
      await this.create({
        planName,
        ...defaultData
      });
    }
  }
};

// Static method to get limits for a specific plan
SubscriptionLimitsSchema.statics.getLimitsForPlan = async function(planName) {
  const plan = await this.findOne({ planName, isActive: true });
  if (!plan) {
    // Return default limits if plan not found
    const defaultData = this.getDefaultLimits(planName);
    return defaultData.limits;
  }
  return plan.limits;
};

const SubscriptionLimits = mongoose.model('SubscriptionLimits', SubscriptionLimitsSchema);

export default SubscriptionLimits;
