// src/components/admin/SystemSettings.jsx
import React, { useState, useEffect } from 'react';
import { 
  FiSettings, 
  FiSave, 
  FiLoader,
  FiToggleLeft,
  FiToggleRight,
  FiAlertTriangle,
  FiCheckCircle
} from 'react-icons/fi';

const SystemSettings = () => {
  const [settings, setSettings] = useState({
    allowNewAdminRegistration: false,
    maintenanceMode: false,
    systemName: 'DOSKY Admin System'
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const API_BASE_URL = import.meta.env.VITE_NODE_BACKEND_URL || 'http://localhost:3001';

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('authToken');
      
      const response = await fetch(`${API_BASE_URL}/api/admin/analytics`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch settings');
      }

      const data = await response.json();
      setSettings({
        allowNewAdminRegistration: data.systemInfo.allowNewAdminRegistration,
        maintenanceMode: data.systemInfo.maintenanceMode,
        systemName: data.systemInfo.systemName || 'DOSKY Admin System'
      });
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      setSaving(true);
      setError('');
      setSuccess('');
      
      const token = localStorage.getItem('authToken');
      
      const response = await fetch(`${API_BASE_URL}/api/admin/settings`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      if (!response.ok) {
        throw new Error('Failed to save settings');
      }

      setSuccess('Settings saved successfully!');
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  const handleToggle = (key) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const handleInputChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  if (loading) {
    return (
      <div className="p-6 flex items-center justify-center">
        <FiLoader className="w-8 h-8 text-purple-500 animate-spin" />
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <FiSettings className="w-6 h-6 text-purple-500 mr-3" />
          <h2 className="text-xl font-bold text-white">System Settings</h2>
        </div>
        <button
          onClick={saveSettings}
          disabled={saving}
          className="flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 text-white rounded-lg transition-colors"
        >
          {saving ? (
            <FiLoader className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <FiSave className="w-4 h-4 mr-2" />
          )}
          {saving ? 'Saving...' : 'Save Settings'}
        </button>
      </div>

      {/* Status Messages */}
      {error && (
        <div className="mb-4 p-3 bg-red-900/50 border border-red-700 rounded-lg text-red-300 flex items-center">
          <FiAlertTriangle className="w-4 h-4 mr-2" />
          {error}
        </div>
      )}

      {success && (
        <div className="mb-4 p-3 bg-green-900/50 border border-green-700 rounded-lg text-green-300 flex items-center">
          <FiCheckCircle className="w-4 h-4 mr-2" />
          {success}
        </div>
      )}

      {/* Settings Sections */}
      <div className="space-y-6">
        {/* General Settings */}
        <div className="bg-slate-700 rounded-lg p-6 border border-slate-600">
          <h3 className="text-lg font-semibold text-white mb-4">General Settings</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                System Name
              </label>
              <input
                type="text"
                value={settings.systemName}
                onChange={(e) => handleInputChange('systemName', e.target.value)}
                className="w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="Enter system name"
              />
              <p className="text-xs text-slate-400 mt-1">
                This name will be displayed in the admin dashboard header.
              </p>
            </div>
          </div>
        </div>

        {/* Admin Settings */}
        <div className="bg-slate-700 rounded-lg p-6 border border-slate-600">
          <h3 className="text-lg font-semibold text-white mb-4">Admin Settings</h3>
          
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h4 className="text-white font-medium">Allow New Admin Registration</h4>
                <p className="text-sm text-slate-400 mt-1">
                  Enable or disable the ability for new users to register as administrators.
                </p>
              </div>
              <button
                onClick={() => handleToggle('allowNewAdminRegistration')}
                className={`ml-4 p-1 rounded-full transition-colors ${
                  settings.allowNewAdminRegistration 
                    ? 'text-green-400 hover:text-green-300' 
                    : 'text-slate-400 hover:text-slate-300'
                }`}
              >
                {settings.allowNewAdminRegistration ? (
                  <FiToggleRight className="w-8 h-8" />
                ) : (
                  <FiToggleLeft className="w-8 h-8" />
                )}
              </button>
            </div>

            {settings.allowNewAdminRegistration && (
              <div className="p-4 bg-yellow-900/20 border border-yellow-700 rounded-lg">
                <div className="flex items-center">
                  <FiAlertTriangle className="w-4 h-4 text-yellow-400 mr-2" />
                  <span className="text-sm text-yellow-300 font-medium">Security Warning</span>
                </div>
                <p className="text-sm text-yellow-200 mt-1">
                  Enabling this setting allows new users to register with admin privileges. 
                  This should only be enabled temporarily when you need to add new administrators.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* System Maintenance */}
        <div className="bg-slate-700 rounded-lg p-6 border border-slate-600">
          <h3 className="text-lg font-semibold text-white mb-4">System Maintenance</h3>
          
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h4 className="text-white font-medium">Maintenance Mode</h4>
                <p className="text-sm text-slate-400 mt-1">
                  Enable maintenance mode to temporarily disable user access to the system.
                </p>
              </div>
              <button
                onClick={() => handleToggle('maintenanceMode')}
                className={`ml-4 p-1 rounded-full transition-colors ${
                  settings.maintenanceMode 
                    ? 'text-yellow-400 hover:text-yellow-300' 
                    : 'text-slate-400 hover:text-slate-300'
                }`}
              >
                {settings.maintenanceMode ? (
                  <FiToggleRight className="w-8 h-8" />
                ) : (
                  <FiToggleLeft className="w-8 h-8" />
                )}
              </button>
            </div>

            {settings.maintenanceMode && (
              <div className="p-4 bg-yellow-900/20 border border-yellow-700 rounded-lg">
                <div className="flex items-center">
                  <FiAlertTriangle className="w-4 h-4 text-yellow-400 mr-2" />
                  <span className="text-sm text-yellow-300 font-medium">Maintenance Mode Active</span>
                </div>
                <p className="text-sm text-yellow-200 mt-1">
                  The system is currently in maintenance mode. Regular users will see a maintenance message 
                  and won't be able to access the application. Administrators can still access the system.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* System Information */}
        <div className="bg-slate-700 rounded-lg p-6 border border-slate-600">
          <h3 className="text-lg font-semibold text-white mb-4">System Information</h3>
          
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="text-sm font-medium text-slate-300 mb-2">Application Version</h4>
              <p className="text-white">v1.0.0</p>
            </div>
            
            <div>
              <h4 className="text-sm font-medium text-slate-300 mb-2">Environment</h4>
              <p className="text-white">Development</p>
            </div>
            
            <div>
              <h4 className="text-sm font-medium text-slate-300 mb-2">Database Status</h4>
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-900/50 text-green-300 border border-green-700">
                Connected
              </span>
            </div>
            
            <div>
              <h4 className="text-sm font-medium text-slate-300 mb-2">Last Updated</h4>
              <p className="text-white">{new Date().toLocaleDateString()}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SystemSettings;
