import { PaymentMethodDetailsNotification } from './payment-method-details-notification.js';
export class TransactionPaymentAttemptNotification {
    constructor(transactionPaymentAttempt) {
        var _a;
        this.paymentAttemptId = transactionPaymentAttempt.payment_attempt_id;
        this.paymentMethodId = (_a = transactionPaymentAttempt.payment_method_id) !== null && _a !== void 0 ? _a : null;
        this.storedPaymentMethodId = transactionPaymentAttempt.stored_payment_method_id;
        this.amount = transactionPaymentAttempt.amount;
        this.status = transactionPaymentAttempt.status;
        this.errorCode = transactionPaymentAttempt.error_code ? transactionPaymentAttempt.error_code : null;
        this.methodDetails = transactionPaymentAttempt.method_details
            ? new PaymentMethodDetailsNotification(transactionPaymentAttempt.method_details)
            : null;
        this.createdAt = transactionPaymentAttempt.created_at;
        this.capturedAt = transactionPaymentAttempt.captured_at ? transactionPaymentAttempt.captured_at : null;
    }
}
