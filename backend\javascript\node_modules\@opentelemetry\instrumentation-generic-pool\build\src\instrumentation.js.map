{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,0CAA0C;AAC1C,oEAKwC;AAIxC,kBAAkB;AAClB,uCAA0D;AAE1D,MAAM,WAAW,GAAG,cAAc,CAAC;AAEnC,MAAa,0BAA2B,SAAQ,qCAAmB;IAIjE,YAAY,SAAgC,EAAE;QAC5C,KAAK,CAAC,sBAAY,EAAE,yBAAe,EAAE,MAAM,CAAC,CAAC;QAJ/C,2BAA2B;QACnB,gBAAW,GAAG,KAAK,CAAC;IAI5B,CAAC;IAED,IAAI;QACF,OAAO;YACL,IAAI,qDAAmC,CACrC,WAAW,EACX,CAAC,YAAY,CAAC,EACd,aAAa,CAAC,EAAE;gBACd,MAAM,IAAI,GAAQ,aAAa,CAAC,IAAI,CAAC;gBACrC,IAAI,IAAA,2BAAS,EAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;oBACrC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;iBACzC;gBACD,IAAI,CAAC,KAAK,CACR,IAAI,CAAC,SAAS,EACd,SAAS,EACT,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAChC,CAAC;gBACF,OAAO,aAAa,CAAC;YACvB,CAAC,EACD,aAAa,CAAC,EAAE;gBACd,MAAM,IAAI,GAAQ,aAAa,CAAC,IAAI,CAAC;gBACrC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBACxC,OAAO,aAAa,CAAC;YACvB,CAAC,CACF;YACD,IAAI,qDAAmC,CACrC,WAAW,EACX,CAAC,YAAY,CAAC,EACd,aAAa,CAAC,EAAE;gBACd,MAAM,IAAI,GAAQ,aAAa,CAAC,IAAI,CAAC;gBACrC,IAAI,IAAA,2BAAS,EAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;oBACrC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;iBACzC;gBACD,IAAI,CAAC,KAAK,CACR,IAAI,CAAC,SAAS,EACd,SAAS,EACT,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,IAAI,CAAC,CAC7C,CAAC;gBACF,OAAO,aAAa,CAAC;YACvB,CAAC,EACD,aAAa,CAAC,EAAE;gBACd,MAAM,IAAI,GAAQ,aAAa,CAAC,IAAI,CAAC;gBACrC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBACxC,OAAO,aAAa,CAAC;YACvB,CAAC,CACF;YACD,IAAI,qDAAmC,CACrC,WAAW,EACX,CAAC,cAAc,CAAC,EAChB,aAAa,CAAC,EAAE;gBACd,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;gBACzB,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,IAAI,CAAC,EAAE;oBACjC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;iBACrC;gBACD,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBAChE,OAAO,aAAa,CAAC;YACvB,CAAC,EACD,aAAa,CAAC,EAAE;gBACd,oEAAoE;gBACpE,uDAAuD;gBACvD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,OAAO,aAAa,CAAC;YACvB,CAAC,CACF;SACF,CAAC;IACJ,CAAC;IAEO,eAAe,CAAC,QAA8C;QACpE,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,SAAS,eAAe,CAE7B,GAAG,IAAW;YAEd,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACpC,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,SAAS,CAC3C,sBAAsB,EACtB,EAAE,EACF,MAAM,CACP,CAAC;YAEF,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE;gBAC5D,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,IAAI,CACtC,KAAK,CAAC,EAAE;oBACN,IAAI,CAAC,GAAG,EAAE,CAAC;oBACX,OAAO,KAAK,CAAC;gBACf,CAAC,EACD,GAAG,CAAC,EAAE;oBACJ,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;oBAC1B,IAAI,CAAC,GAAG,EAAE,CAAC;oBACX,MAAM,GAAG,CAAC;gBACZ,CAAC,CACF,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;IACJ,CAAC;IAEO,YAAY,CAAC,QAAa;QAChC,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,SAAS,YAAY;YAC1B,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAC7C,eAAe,CAAC,KAAK,CACnB,IAAI,EACJ,SAAS,EACT,eAAe,CAAC,4BAA4B,CAAC,IAAI,CAAC,eAAe,CAAC,CACnE,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;IACJ,CAAC;IAEO,4BAA4B,CAAC,QAAa;QAChD,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,SAAS,eAAe,CAE7B,EAAY,EACZ,QAAgB;YAEhB,0BAA0B;YAC1B,IAAI,eAAe,CAAC,WAAW,EAAE;gBAC/B,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;aAC1C;YACD,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACpC,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,SAAS,CAC3C,sBAAsB,EACtB,EAAE,EACF,MAAM,CACP,CAAC;YAEF,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE;gBAC5D,QAAQ,CAAC,IAAI,CACX,IAAI,EACJ,CAAC,GAAY,EAAE,MAAe,EAAE,EAAE;oBAChC,IAAI,CAAC,GAAG,EAAE,CAAC;oBACX,gDAAgD;oBAChD,4CAA4C;oBAC5C,IAAI,EAAE,EAAE;wBACN,OAAO,EAAE,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;qBACxB;gBACH,CAAC,EACD,QAAQ,CACT,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;IACJ,CAAC;CACF;AArJD,gEAqJC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as api from '@opentelemetry/api';\nimport {\n  InstrumentationBase,\n  InstrumentationConfig,\n  InstrumentationNodeModuleDefinition,\n  isWrapped,\n} from '@opentelemetry/instrumentation';\n\nimport type * as genericPool from 'generic-pool';\n\n/** @knipignore */\nimport { PACKAGE_NAME, PACKAGE_VERSION } from './version';\n\nconst MODULE_NAME = 'generic-pool';\n\nexport class GenericPoolInstrumentation extends InstrumentationBase {\n  // only used for v2 - v2.3)\n  private _isDisabled = false;\n\n  constructor(config: InstrumentationConfig = {}) {\n    super(PACKAGE_NAME, PACKAGE_VERSION, config);\n  }\n\n  init() {\n    return [\n      new InstrumentationNodeModuleDefinition(\n        MODULE_NAME,\n        ['>=3.0.0 <4'],\n        moduleExports => {\n          const Pool: any = moduleExports.Pool;\n          if (isWrapped(Pool.prototype.acquire)) {\n            this._unwrap(Pool.prototype, 'acquire');\n          }\n          this._wrap(\n            Pool.prototype,\n            'acquire',\n            this._acquirePatcher.bind(this)\n          );\n          return moduleExports;\n        },\n        moduleExports => {\n          const Pool: any = moduleExports.Pool;\n          this._unwrap(Pool.prototype, 'acquire');\n          return moduleExports;\n        }\n      ),\n      new InstrumentationNodeModuleDefinition(\n        MODULE_NAME,\n        ['>=2.4.0 <3'],\n        moduleExports => {\n          const Pool: any = moduleExports.Pool;\n          if (isWrapped(Pool.prototype.acquire)) {\n            this._unwrap(Pool.prototype, 'acquire');\n          }\n          this._wrap(\n            Pool.prototype,\n            'acquire',\n            this._acquireWithCallbacksPatcher.bind(this)\n          );\n          return moduleExports;\n        },\n        moduleExports => {\n          const Pool: any = moduleExports.Pool;\n          this._unwrap(Pool.prototype, 'acquire');\n          return moduleExports;\n        }\n      ),\n      new InstrumentationNodeModuleDefinition(\n        MODULE_NAME,\n        ['>=2.0.0 <2.4'],\n        moduleExports => {\n          this._isDisabled = false;\n          if (isWrapped(moduleExports.Pool)) {\n            this._unwrap(moduleExports, 'Pool');\n          }\n          this._wrap(moduleExports, 'Pool', this._poolWrapper.bind(this));\n          return moduleExports;\n        },\n        moduleExports => {\n          // since the object is created on the fly every time, we need to use\n          // a boolean switch here to disable the instrumentation\n          this._isDisabled = true;\n          return moduleExports;\n        }\n      ),\n    ];\n  }\n\n  private _acquirePatcher(original: genericPool.Pool<unknown>['acquire']) {\n    const instrumentation = this;\n    return function wrapped_acquire(\n      this: genericPool.Pool<unknown>,\n      ...args: any[]\n    ) {\n      const parent = api.context.active();\n      const span = instrumentation.tracer.startSpan(\n        'generic-pool.acquire',\n        {},\n        parent\n      );\n\n      return api.context.with(api.trace.setSpan(parent, span), () => {\n        return original.call(this, ...args).then(\n          value => {\n            span.end();\n            return value;\n          },\n          err => {\n            span.recordException(err);\n            span.end();\n            throw err;\n          }\n        );\n      });\n    };\n  }\n\n  private _poolWrapper(original: any) {\n    const instrumentation = this;\n    return function wrapped_pool(this: any) {\n      const pool = original.apply(this, arguments);\n      instrumentation._wrap(\n        pool,\n        'acquire',\n        instrumentation._acquireWithCallbacksPatcher.bind(instrumentation)\n      );\n      return pool;\n    };\n  }\n\n  private _acquireWithCallbacksPatcher(original: any) {\n    const instrumentation = this;\n    return function wrapped_acquire(\n      this: genericPool.Pool<unknown>,\n      cb: Function,\n      priority: number\n    ) {\n      // only used for v2 - v2.3\n      if (instrumentation._isDisabled) {\n        return original.call(this, cb, priority);\n      }\n      const parent = api.context.active();\n      const span = instrumentation.tracer.startSpan(\n        'generic-pool.acquire',\n        {},\n        parent\n      );\n\n      return api.context.with(api.trace.setSpan(parent, span), () => {\n        original.call(\n          this,\n          (err: unknown, client: unknown) => {\n            span.end();\n            // Not checking whether cb is a function because\n            // the original code doesn't do that either.\n            if (cb) {\n              return cb(err, client);\n            }\n          },\n          priority\n        );\n      });\n    };\n  }\n}\n"]}