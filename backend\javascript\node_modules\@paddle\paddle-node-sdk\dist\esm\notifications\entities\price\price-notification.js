import { ImportMetaNotification, MoneyNotification, TimePeriodNotification, UnitPriceOverrideNotification, } from '../shared/index.js';
import { PriceQuantityNotification } from './price-quantity-notification.js';
export class PriceNotification {
    constructor(price) {
        var _a, _b, _c, _d;
        this.id = price.id;
        this.productId = price.product_id;
        this.description = price.description;
        this.type = price.type ? price.type : null;
        this.name = price.name ? price.name : null;
        this.billingCycle = price.billing_cycle ? new TimePeriodNotification(price.billing_cycle) : null;
        this.trialPeriod = price.trial_period ? new TimePeriodNotification(price.trial_period) : null;
        this.taxMode = price.tax_mode;
        this.unitPrice = new MoneyNotification(price.unit_price);
        this.unitPriceOverrides =
            (_b = (_a = price.unit_price_overrides) === null || _a === void 0 ? void 0 : _a.map((unit_price_override) => new UnitPriceOverrideNotification(unit_price_override))) !== null && _b !== void 0 ? _b : [];
        this.quantity = new PriceQuantityNotification(price.quantity);
        this.status = price.status;
        this.createdAt = (_c = price.created_at) !== null && _c !== void 0 ? _c : null;
        this.updatedAt = (_d = price.updated_at) !== null && _d !== void 0 ? _d : null;
        this.customData = price.custom_data ? price.custom_data : null;
        this.importMeta = price.import_meta ? new ImportMetaNotification(price.import_meta) : null;
    }
}
