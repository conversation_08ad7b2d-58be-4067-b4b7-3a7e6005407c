{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { InstrumentationConfig } from '@opentelemetry/instrumentation';\nimport type * as api from '@opentelemetry/api';\n\nexport interface GraphQLInstrumentationExecutionResponseHook {\n  (span: api.Span, data: any): void;\n}\n\nexport interface GraphQLInstrumentationConfig extends InstrumentationConfig {\n  /**\n   * When set to true it will not remove attributes values from schema source.\n   * By default all values that can be sensitive are removed and replaced\n   * with \"*\"\n   *\n   * @default false\n   */\n  allowValues?: boolean;\n\n  /**\n   * The maximum depth of fields/resolvers to instrument.\n   * When set to 0 it will not instrument fields and resolvers\n   *\n   * @default undefined\n   */\n  depth?: number;\n\n  /**\n   * Do not create spans for resolvers.\n   *\n   * @default false\n   */\n  ignoreResolveSpans?: boolean;\n\n  /**\n   * Don't create spans for the execution of the default resolver on object properties.\n   *\n   * When a resolver function is not defined on the schema for a field, graphql will\n   * use the default resolver which just looks for a property with that name on the object.\n   * If the property is not a function, it's not very interesting to trace.\n   * This option can reduce noise and number of spans created.\n   *\n   * @default false\n   */\n  ignoreTrivialResolveSpans?: boolean;\n\n  /**\n   * Whether to merge list items into a single element.\n   *\n   * @example `users.*.name` instead of `users.0.name`, `users.1.name`\n   *\n   * @default false\n   */\n  mergeItems?: boolean;\n\n  /**\n   * Hook that allows adding custom span attributes based on the data\n   * returned from \"execute\" GraphQL action.\n   *\n   * @param data - A GraphQL `ExecutionResult` object. For the exact type definitions, see the following:\n   *  - {@linkcode https://github.com/graphql/graphql-js/blob/v14.7.0/src/execution/execute.js#L115 graphql@14}\n   *  - {@linkcode https://github.com/graphql/graphql-js/blob/15.x.x/src/execution/execute.d.ts#L31 graphql@15}\n   *  - {@linkcode https://github.com/graphql/graphql-js/blob/16.x.x/src/execution/execute.ts#L127 graphql@16}\n   *\n   * @default undefined\n   */\n  responseHook?: GraphQLInstrumentationExecutionResponseHook;\n}\n\n// Utility type to make specific properties required\ntype RequireSpecificKeys<T, K extends keyof T> = T & { [P in K]-?: T[P] };\n\n// Merged and parsed config of default instrumentation config and GraphQL\nexport type GraphQLInstrumentationParsedConfig = RequireSpecificKeys<\n  GraphQLInstrumentationConfig,\n  'mergeItems' | 'depth' | 'allowValues' | 'ignoreResolveSpans'\n>;\n"]}