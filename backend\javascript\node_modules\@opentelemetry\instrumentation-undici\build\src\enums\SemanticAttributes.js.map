{"version": 3, "file": "SemanticAttributes.js", "sourceRoot": "", "sources": ["../../../src/enums/SemanticAttributes.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,iHAAiH;AACpG,QAAA,kBAAkB,GAAG;IAChC;;OAEG;IACH,qBAAqB,EAAE,uBAAuB;IAE9C;;;;;;;;;;;;;;;;;;MAkBE;IACF,UAAU,EAAE,YAAY;IAExB;;OAEG;IACH,sBAAsB,EAAE,wBAAwB;IAEhD;;;;;;;;;;;;;;;;;MAiBE;IACF,mBAAmB,EAAE,qBAAqB;IAE1C;;OAEG;IACH,4BAA4B,EAAE,8BAA8B;IAE5D;;;;OAIG;IACH,yBAAyB,EAAE,2BAA2B;IAEtD;;OAEG;IACH,uBAAuB,EAAE,yBAAyB;IAElD;;OAEG;IACH,yBAAyB,EAAE,2BAA2B;IAEtD;;;;;MAKE;IACF,UAAU,EAAE,YAAY;IAExB;;OAEG;IACH,oBAAoB,EAAE,sBAAsB;IAE5C;;OAEG;IACH,iBAAiB,EAAE,mBAAmB;IAEtC;;;;OAIG;IACH,qBAAqB,EAAE,uBAAuB;IAE9C;;;;OAIG;IACH,wBAAwB,EAAE,0BAA0B;IAEpD;;;;OAIG;IACH,cAAc,EAAE,gBAAgB;IAEhC;;;;OAIG;IACH,WAAW,EAAE,aAAa;IAE1B;;;;;;MAME;IACF,QAAQ,EAAE,UAAU;IAEpB;;OAEG;IACH,QAAQ,EAAE,UAAU;IAEpB;;;;OAIG;IACH,SAAS,EAAE,WAAW;IAEtB;;OAEG;IACH,UAAU,EAAE,YAAY;IAExB;;OAEG;IACH,mBAAmB,EAAE,qBAAqB;CAC3C,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates//templates/SemanticAttributes.ts.j2\nexport const SemanticAttributes = {\n  /**\n   * State of the HTTP connection in the HTTP connection pool.\n   */\n  HTTP_CONNECTION_STATE: 'http.connection.state',\n\n  /**\n  * Describes a class of error the operation ended with.\n  *\n  * Note: The `error.type` SHOULD be predictable and SHOULD have low cardinality.\nInstrumentations SHOULD document the list of errors they report.\n\nThe cardinality of `error.type` within one instrumentation library SHOULD be low.\nTelemetry consumers that aggregate data from multiple instrumentation libraries and applications\nshould be prepared for `error.type` to have high cardinality at query time when no\nadditional filters are applied.\n\nIf the operation has completed successfully, instrumentations SHOULD NOT set `error.type`.\n\nIf a specific domain defines its own set of error identifiers (such as HTTP or gRPC status codes),\nit&#39;s RECOMMENDED to:\n\n* Use a domain-specific attribute\n* Set `error.type` to capture all errors, regardless of whether they are defined within the domain-specific set or not.\n  */\n  ERROR_TYPE: 'error.type',\n\n  /**\n   * The size of the request payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n   */\n  HTTP_REQUEST_BODY_SIZE: 'http.request.body.size',\n\n  /**\n  * HTTP request method.\n  *\n  * Note: HTTP request method value SHOULD be &#34;known&#34; to the instrumentation.\nBy default, this convention defines &#34;known&#34; methods as the ones listed in [RFC9110](https://www.rfc-editor.org/rfc/rfc9110.html#name-methods)\nand the PATCH method defined in [RFC5789](https://www.rfc-editor.org/rfc/rfc5789.html).\n\nIf the HTTP request method is not known to instrumentation, it MUST set the `http.request.method` attribute to `_OTHER`.\n\nIf the HTTP instrumentation could end up converting valid HTTP request methods to `_OTHER`, then it MUST provide a way to override\nthe list of known HTTP methods. If this override is done via environment variable, then the environment variable MUST be named\nOTEL_INSTRUMENTATION_HTTP_KNOWN_METHODS and support a comma-separated list of case-sensitive known HTTP methods\n(this list MUST be a full override of the default known method, it is not a list of known methods in addition to the defaults).\n\nHTTP method names are case-sensitive and `http.request.method` attribute value MUST match a known HTTP method name exactly.\nInstrumentations for specific web frameworks that consider HTTP methods to be case insensitive, SHOULD populate a canonical equivalent.\nTracing instrumentations that do so, MUST also set `http.request.method_original` to the original value.\n  */\n  HTTP_REQUEST_METHOD: 'http.request.method',\n\n  /**\n   * Original HTTP method sent by the client in the request line.\n   */\n  HTTP_REQUEST_METHOD_ORIGINAL: 'http.request.method_original',\n\n  /**\n   * The ordinal number of request resending attempt (for any reason, including redirects).\n   *\n   * Note: The resend count SHOULD be updated each time an HTTP request gets resent by the client, regardless of what was the cause of the resending (e.g. redirection, authorization failure, 503 Server Unavailable, network issues, or any other).\n   */\n  HTTP_REQUEST_RESEND_COUNT: 'http.request.resend_count',\n\n  /**\n   * The size of the response payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n   */\n  HTTP_RESPONSE_BODY_SIZE: 'http.response.body.size',\n\n  /**\n   * [HTTP response status code](https://tools.ietf.org/html/rfc7231#section-6).\n   */\n  HTTP_RESPONSE_STATUS_CODE: 'http.response.status_code',\n\n  /**\n  * The matched route, that is, the path template in the format used by the respective server framework.\n  *\n  * Note: MUST NOT be populated when this is not supported by the HTTP server framework as the route attribute should have low-cardinality and the URI path can NOT substitute it.\nSHOULD include the [application root](/docs/http/http-spans.md#http-server-definitions) if there is one.\n  */\n  HTTP_ROUTE: 'http.route',\n\n  /**\n   * Peer address of the network connection - IP address or Unix domain socket name.\n   */\n  NETWORK_PEER_ADDRESS: 'network.peer.address',\n\n  /**\n   * Peer port number of the network connection.\n   */\n  NETWORK_PEER_PORT: 'network.peer.port',\n\n  /**\n   * [OSI application layer](https://osi-model.com/application-layer/) or non-OSI equivalent.\n   *\n   * Note: The value SHOULD be normalized to lowercase.\n   */\n  NETWORK_PROTOCOL_NAME: 'network.protocol.name',\n\n  /**\n   * Version of the protocol specified in `network.protocol.name`.\n   *\n   * Note: `network.protocol.version` refers to the version of the protocol used and might be different from the protocol client&#39;s version. If the HTTP client has a version of `0.27.2`, but sends HTTP version `1.1`, this attribute should be set to `1.1`.\n   */\n  NETWORK_PROTOCOL_VERSION: 'network.protocol.version',\n\n  /**\n   * Server domain name if available without reverse DNS lookup; otherwise, IP address or Unix domain socket name.\n   *\n   * Note: When observed from the client side, and when communicating through an intermediary, `server.address` SHOULD represent the server address behind any intermediaries, for example proxies, if it&#39;s available.\n   */\n  SERVER_ADDRESS: 'server.address',\n\n  /**\n   * Server port number.\n   *\n   * Note: When observed from the client side, and when communicating through an intermediary, `server.port` SHOULD represent the server port behind any intermediaries, for example proxies, if it&#39;s available.\n   */\n  SERVER_PORT: 'server.port',\n\n  /**\n  * Absolute URL describing a network resource according to [RFC3986](https://www.rfc-editor.org/rfc/rfc3986).\n  *\n  * Note: For network calls, URL usually has `scheme://host[:port][path][?query][#fragment]` format, where the fragment is not transmitted over HTTP, but if it is known, it SHOULD be included nevertheless.\n`url.full` MUST NOT contain credentials passed via URL in form of `https://username:<EMAIL>/`. In such case username and password SHOULD be redacted and attribute&#39;s value SHOULD be `https://REDACTED:<EMAIL>/`.\n`url.full` SHOULD capture the absolute URL when it is available (or can be reconstructed) and SHOULD NOT be validated or modified except for sanitizing purposes.\n  */\n  URL_FULL: 'url.full',\n\n  /**\n   * The [URI path](https://www.rfc-editor.org/rfc/rfc3986#section-3.3) component.\n   */\n  URL_PATH: 'url.path',\n\n  /**\n   * The [URI query](https://www.rfc-editor.org/rfc/rfc3986#section-3.4) component.\n   *\n   * Note: Sensitive content provided in query string SHOULD be scrubbed when instrumentations can identify it.\n   */\n  URL_QUERY: 'url.query',\n\n  /**\n   * The [URI scheme](https://www.rfc-editor.org/rfc/rfc3986#section-3.1) component identifying the used protocol.\n   */\n  URL_SCHEME: 'url.scheme',\n\n  /**\n   * Value of the [HTTP User-Agent](https://www.rfc-editor.org/rfc/rfc9110.html#field.user-agent) header sent by the client.\n   */\n  USER_AGENT_ORIGINAL: 'user_agent.original',\n};\n"]}