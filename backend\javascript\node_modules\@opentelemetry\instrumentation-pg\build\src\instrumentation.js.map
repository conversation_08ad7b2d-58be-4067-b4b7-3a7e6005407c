{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,oEAMwC;AACxC,4CAW4B;AAG5B,qDAO0B;AAE1B,iCAAiC;AACjC,0DAAmE;AACnE,kBAAkB;AAClB,uCAA0D;AAC1D,iDAA8C;AAC9C,8CAI6B;AAC7B,8EAM6C;AAC7C,uCAMmB;AAEnB,SAAS,oBAAoB,CAAC,MAAW;IACvC,OAAO,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,QAAQ;QAC5C,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM;QACvB,CAAC,CAAC,MAAM,CAAC,CAAC,WAAW;AACzB,CAAC;AAED,MAAa,iBAAkB,SAAQ,qCAA4C;IAejF,YAAY,SAAkC,EAAE;QAC9C,KAAK,CAAC,sBAAY,EAAE,yBAAe,EAAE,MAAM,CAAC,CAAC;QAZ/C,iEAAiE;QACjE,wEAAwE;QACxE,uEAAuE;QACvE,sFAAsF;QACtF,yBAAyB;QACjB,wBAAmB,GAAiC;YAC1D,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,CAAC;YACP,OAAO,EAAE,CAAC;SACX,CAAC;IAIF,CAAC;IAEQ,wBAAwB;QAC/B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAClD,6CAAmC,EACnC;YACE,WAAW,EAAE,yCAAyC;YACtD,IAAI,EAAE,GAAG;YACT,SAAS,EAAE,eAAS,CAAC,MAAM;YAC3B,MAAM,EAAE;gBACN,wBAAwB,EAAE;oBACxB,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;iBAC7C;aACF;SACF,CACF,CAAC;QAEF,IAAI,CAAC,mBAAmB,GAAG;YACzB,IAAI,EAAE,CAAC;YACP,OAAO,EAAE,CAAC;YACV,IAAI,EAAE,CAAC;SACR,CAAC;QACF,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CACrD,2CAAiC,EACjC;YACE,WAAW,EACT,yFAAyF;YAC3F,IAAI,EAAE,cAAc;SACrB,CACF,CAAC;QACF,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAC9D,sDAA4C,EAC5C;YACE,WAAW,EACT,gEAAgE;YAClE,IAAI,EAAE,cAAc;SACrB,CACF,CAAC;IACJ,CAAC;IAES,IAAI;QACZ,MAAM,qBAAqB,GAAG,CAAC,YAAY,CAAC,CAAC;QAE7C,MAAM,oBAAoB,GAAG,IAAI,+CAA6B,CAC5D,yBAAyB,EACzB,qBAAqB,EACrB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAC9B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CACjC,CAAC;QAEF,MAAM,cAAc,GAAG,IAAI,+CAA6B,CACtD,kBAAkB,EAClB,qBAAqB,EACrB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAC9B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CACjC,CAAC;QAEF,MAAM,QAAQ,GAAG,IAAI,qDAAmC,CACtD,IAAI,EACJ,qBAAqB,EACrB,CAAC,MAAW,EAAE,EAAE;YACd,MAAM,aAAa,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC;YAEnD,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YAC1C,OAAO,MAAM,CAAC;QAChB,CAAC,EACD,CAAC,MAAW,EAAE,EAAE;YACd,MAAM,aAAa,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC;YAEnD,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YAC5C,OAAO,MAAM,CAAC;QAChB,CAAC,EACD,CAAC,cAAc,EAAE,oBAAoB,CAAC,CACvC,CAAC;QAEF,MAAM,YAAY,GAAG,IAAI,qDAAmC,CAC1D,SAAS,EACT,CAAC,YAAY,CAAC,EACd,CAAC,aAAiC,EAAE,EAAE;YACpC,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;gBAC9C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;aAClD;YACD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,SAAS,EACvB,SAAS,EACT,IAAI,CAAC,oBAAoB,EAAS,CACnC,CAAC;YACF,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,CAAC,aAAiC,EAAE,EAAE;YACpC,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;gBAC9C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;aAClD;QACH,CAAC,CACF,CAAC;QAEF,OAAO,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;IAClC,CAAC;IAEO,cAAc,CAAC,MAAW;QAChC,IAAI,CAAC,MAAM,EAAE;YACX,OAAO;SACR;QAED,MAAM,aAAa,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC;QAEnD,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;YAC5C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;SAChD;QAED,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;YAC9C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;SAClD;QAED,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,SAAS,EACvB,OAAO,EACP,IAAI,CAAC,oBAAoB,EAAS,CACnC,CAAC;QAEF,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,SAAS,EACvB,SAAS,EACT,IAAI,CAAC,sBAAsB,EAAS,CACrC,CAAC;QAEF,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,gBAAgB,CAAC,MAAW;QAClC,MAAM,aAAa,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC;QAEnD,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;YAC5C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;SAChD;QAED,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;YAC9C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;SAClD;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,sBAAsB;QAC5B,MAAM,MAAM,GAAG,IAAI,CAAC;QACpB,OAAO,CAAC,QAAyB,EAAE,EAAE;YACnC,OAAO,SAAS,OAAO,CAAuB,QAAmB;gBAC/D,IAAI,KAAK,CAAC,yBAAyB,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,EAAE;oBACvD,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;iBACtC;gBAED,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,qBAAS,CAAC,OAAO,EAAE;oBACtD,IAAI,EAAE,cAAQ,CAAC,MAAM;oBACrB,UAAU,EAAE,KAAK,CAAC,mCAAmC,CAAC,IAAI,CAAC;iBAC5D,CAAC,CAAC;gBAEH,IAAI,QAAQ,EAAE;oBACZ,MAAM,UAAU,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,CAAC;oBACnD,QAAQ,GAAG,KAAK,CAAC,0BAA0B,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;oBAC5D,IAAI,UAAU,EAAE;wBACd,QAAQ,GAAG,aAAO,CAAC,IAAI,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;qBACrD;iBACF;gBAED,MAAM,aAAa,GAAY,aAAO,CAAC,IAAI,CACzC,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EACrC,GAAG,EAAE;oBACH,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBACvC,CAAC,CACF,CAAC;gBAEF,OAAO,mBAAmB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YAClD,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,uBAAuB,CAAC,UAAsB,EAAE,SAAiB;QACvE,MAAM,iBAAiB,GAAe,EAAE,CAAC;QACzC,MAAM,UAAU,GAAG;YACjB,yCAAkB;YAClB,2BAAiB;YACjB,sCAAe;YACf,uCAAgB;YAChB,0CAAmB;YACnB,gCAAsB;SACvB,CAAC;QAEF,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACvB,IAAI,GAAG,IAAI,UAAU,EAAE;gBACrB,iBAAiB,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;aAC1C;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,eAAe,GACnB,IAAA,2BAAoB,EAAC,IAAA,qBAAc,EAAC,SAAS,EAAE,IAAA,aAAM,GAAE,CAAC,CAAC,GAAG,IAAI,CAAC;QACnE,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,eAAe,EAAE,iBAAiB,CAAC,CAAC;IACrE,CAAC;IAEO,oBAAoB;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC;QACpB,OAAO,CAAC,QAA+C,EAAE,EAAE;YACzD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;YACvD,OAAO,SAAS,KAAK,CAAyB,GAAG,IAAe;gBAC9D,IAAI,KAAK,CAAC,yBAAyB,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,EAAE;oBACvD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAa,CAAC,CAAC;iBAC5C;gBACD,MAAM,SAAS,GAAG,IAAA,aAAM,GAAE,CAAC;gBAE3B,gEAAgE;gBAChE,sEAAsE;gBACtE,uEAAuE;gBACvE,oEAAoE;gBACpE,wEAAwE;gBACxE,wEAAwE;gBACxE,kEAAkE;gBAClE,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACrB,MAAM,gBAAgB,GAAG,OAAO,IAAI,KAAK,QAAQ,CAAC;gBAClD,MAAM,6BAA6B,GACjC,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;gBAErC,yEAAyE;gBACzE,yEAAyE;gBACzE,kDAAkD;gBAClD,MAAM,WAAW,GAAG,gBAAgB;oBAClC,CAAC,CAAC;wBACE,IAAI,EAAE,IAAc;wBACpB,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;qBACrD;oBACH,CAAC,CAAC,6BAA6B;wBAC/B,CAAC,CAAE,IAA6B;wBAChC,CAAC,CAAC,SAAS,CAAC;gBAEd,MAAM,UAAU,GAAe;oBAC7B,CAAC,yCAAkB,CAAC,EAAE,gDAAyB;oBAC/C,CAAC,2BAAiB,CAAC,EAAE,IAAI,CAAC,QAAQ;oBAClC,CAAC,uCAAgB,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI;oBAClD,CAAC,0CAAmB,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI;iBACtD,CAAC;gBAEF,IAAI,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,IAAI,EAAE;oBACrB,UAAU,CAAC,gCAAsB,CAAC;wBAChC,KAAK,CAAC,4BAA4B,CAAC,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,IAAI,CAAC,CAAC;iBACzD;gBAED,MAAM,cAAc,GAAG,GAAG,EAAE;oBAC1B,MAAM,CAAC,uBAAuB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;gBACxD,CAAC,CAAC;gBAEF,MAAM,qBAAqB,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;gBAEjD,MAAM,IAAI,GAAG,KAAK,CAAC,iBAAiB,CAAC,IAAI,CACvC,IAAI,EACJ,MAAM,CAAC,MAAM,EACb,qBAAqB,EACrB,WAAW,CACZ,CAAC;gBAEF,sEAAsE;gBACtE,+DAA+D;gBAC/D,IAAI,qBAAqB,CAAC,+BAA+B,EAAE;oBACzD,IAAI,gBAAgB,EAAE;wBACpB,IAAI,CAAC,CAAC,CAAC,GAAG,IAAA,mCAAsB,EAAC,IAAI,EAAE,IAAI,CAAC,CAAC;qBAC9C;yBAAM,IAAI,6BAA6B,IAAI,CAAC,CAAC,MAAM,IAAI,IAAI,CAAC,EAAE;wBAC7D,yEAAyE;wBACzE,8EAA8E;wBAC9E,8DAA8D;wBAC9D,IAAI,CAAC,CAAC,CAAC,mCACF,IAAI,KACP,IAAI,EAAE,IAAA,mCAAsB,EAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,GAC9C,CAAC;qBACH;iBACF;gBAED,iDAAiD;gBACjD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;oBACnB,MAAM,UAAU,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,CAAC;oBACnD,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;wBAC/C,gCAAgC;wBAChC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,aAAa,CACzC,qBAAqB,EACrB,IAAI,EACJ,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAqB,EAAE,qBAAqB;wBAChE,UAAU,EACV,cAAc,CACf,CAAC;wBAEF,6CAA6C;wBAC7C,IAAI,UAAU,EAAE;4BACd,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,aAAO,CAAC,IAAI,CAClC,aAAO,CAAC,MAAM,EAAE,EAChB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CACtB,CAAC;yBACH;qBACF;yBAAM,IAAI,OAAO,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,QAAQ,CAAA,KAAK,UAAU,EAAE;wBACtD,6BAA6B;wBAC7B,IAAI,QAAQ,GAAG,KAAK,CAAC,aAAa,CAChC,MAAM,CAAC,SAAS,EAAE,EAClB,IAAI,EACJ,WAAW,CAAC,QAA4B,EAAE,qBAAqB;wBAC/D,UAAU,EACV,cAAc,CACf,CAAC;wBAEF,8CAA8C;wBAC9C,IAAI,UAAU,EAAE;4BACd,QAAQ,GAAG,aAAO,CAAC,IAAI,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;yBACrD;wBAEA,IAAI,CAAC,CAAC,CAAqC,CAAC,QAAQ,GAAG,QAAQ,CAAC;qBAClE;iBACF;gBAED,MAAM,EAAE,WAAW,EAAE,GAAG,qBAAqB,CAAC;gBAC9C,IAAI,OAAO,WAAW,KAAK,UAAU,IAAI,WAAW,EAAE;oBACpD,IAAA,wCAAsB,EACpB,GAAG,EAAE;wBACH,kEAAkE;wBAClE,uCAAuC;wBACvC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,oBAAoB,CAAC;wBACjE,MAAM,UAAU,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;wBAElD,WAAW,CAAC,IAAI,EAAE;4BAChB,UAAU;4BACV,KAAK,EAAE;gCACL,IAAI,EAAE,WAAW,CAAC,IAAI;gCACtB,yDAAyD;gCACzD,6DAA6D;gCAC7D,8DAA8D;gCAC9D,yDAAyD;gCACzD,4DAA4D;gCAC5D,2DAA2D;gCAC3D,8DAA8D;gCAC9D,0DAA0D;gCAC1D,2DAA2D;gCAC3D,0DAA0D;gCAC1D,6DAA6D;gCAC7D,4DAA4D;gCAC5D,6DAA6D;gCAC7D,8DAA8D;gCAC9D,kEAAkE;gCAClE,MAAM,EAAE,WAAW,CAAC,MAAmB;gCACvC,IAAI,EAAE,WAAW,CAAC,IAA0B;6BAC7C;yBACF,CAAC,CAAC;oBACL,CAAC,EACD,GAAG,CAAC,EAAE;wBACJ,IAAI,GAAG,EAAE;4BACP,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC;yBACrD;oBACH,CAAC,EACD,IAAI,CACL,CAAC;iBACH;gBAED,IAAI,MAAe,CAAC;gBACpB,IAAI;oBACF,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAa,CAAC,CAAC;iBAC9C;gBAAC,OAAO,CAAU,EAAE;oBACnB,IAAI,CAAC,SAAS,CAAC;wBACb,IAAI,EAAE,oBAAc,CAAC,KAAK;wBAC1B,OAAO,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;qBAClC,CAAC,CAAC;oBACH,IAAI,CAAC,GAAG,EAAE,CAAC;oBACX,MAAM,CAAC,CAAC;iBACT;gBAED,+CAA+C;gBAC/C,IAAI,MAAM,YAAY,OAAO,EAAE;oBAC7B,OAAO,MAAM;yBACV,IAAI,CAAC,CAAC,MAAe,EAAE,EAAE;wBACxB,yFAAyF;wBACzF,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;4BAC3B,KAAK,CAAC,qBAAqB,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;4BAC9D,cAAc,EAAE,CAAC;4BACjB,IAAI,CAAC,GAAG,EAAE,CAAC;4BACX,OAAO,CAAC,MAAM,CAAC,CAAC;wBAClB,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC;yBACD,KAAK,CAAC,CAAC,KAAY,EAAE,EAAE;wBACtB,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;4BAC/B,IAAI,CAAC,SAAS,CAAC;gCACb,IAAI,EAAE,oBAAc,CAAC,KAAK;gCAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;6BACvB,CAAC,CAAC;4BACH,cAAc,EAAE,CAAC;4BACjB,IAAI,CAAC,GAAG,EAAE,CAAC;4BACX,MAAM,CAAC,KAAK,CAAC,CAAC;wBAChB,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC;iBACN;gBAED,oBAAoB;gBACpB,OAAO,MAAM,CAAC,CAAC,OAAO;YACxB,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,6BAA6B,CAAC,MAAsB;QAC1D,IAAI,MAAM,CAAC,oCAAmB,CAAC;YAAE,OAAO;QACxC,MAAM,QAAQ,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEnD,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;YACxB,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC,aAAa,CAC5C,QAAQ,EACR,MAAM,EACN,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,0BAA0B,EAC/B,IAAI,CAAC,mBAAmB,CACzB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;YACxB,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC,aAAa,CAC5C,QAAQ,EACR,MAAM,EACN,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,0BAA0B,EAC/B,IAAI,CAAC,mBAAmB,CACzB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;YACvB,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC,aAAa,CAC5C,QAAQ,EACR,MAAM,EACN,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,0BAA0B,EAC/B,IAAI,CAAC,mBAAmB,CACzB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,EAAE,CAAC,SAAgB,EAAE,GAAG,EAAE;YAC/B,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC,aAAa,CAC5C,QAAQ,EACR,MAAM,EACN,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,0BAA0B,EAC/B,IAAI,CAAC,mBAAmB,CACzB,CAAC;QACJ,CAAC,CAAC,CAAC;QACH,MAAM,CAAC,oCAAmB,CAAC,GAAG,IAAI,CAAC;IACrC,CAAC;IAEO,oBAAoB;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC;QACpB,OAAO,CAAC,eAAqD,EAAE,EAAE;YAC/D,OAAO,SAAS,OAAO,CAAuB,QAAyB;gBACrE,IAAI,KAAK,CAAC,yBAAyB,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,EAAE;oBACvD,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,QAAe,CAAC,CAAC;iBACpD;gBAED,aAAa;gBACb,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,qBAAS,CAAC,YAAY,EAAE;oBAC3D,IAAI,EAAE,cAAQ,CAAC,MAAM;oBACrB,UAAU,EAAE,KAAK,CAAC,6BAA6B,CAAC,IAAI,CAAC,OAAO,CAAC;iBAC9D,CAAC,CAAC;gBAEH,MAAM,CAAC,6BAA6B,CAAC,IAAI,CAAC,CAAC;gBAE3C,IAAI,QAAQ,EAAE;oBACZ,MAAM,UAAU,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,CAAC;oBACnD,QAAQ,GAAG,KAAK,CAAC,mBAAmB,CAClC,IAAI,EACJ,QAAQ,CACS,CAAC;oBACpB,6CAA6C;oBAC7C,IAAI,UAAU,EAAE;wBACd,QAAQ,GAAG,aAAO,CAAC,IAAI,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;qBACrD;iBACF;gBAED,MAAM,aAAa,GAAY,aAAO,CAAC,IAAI,CACzC,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EACrC,GAAG,EAAE;oBACH,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,QAAe,CAAC,CAAC;gBACrD,CAAC,CACF,CAAC;gBAEF,OAAO,mBAAmB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YAClD,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;CACF;AAnfD,8CAmfC;AAED,SAAS,mBAAmB,CAAC,IAAU,EAAE,aAAsB;IAC7D,IAAI,CAAC,CAAC,aAAa,YAAY,OAAO,CAAC,EAAE;QACvC,OAAO,aAAa,CAAC;KACtB;IAED,MAAM,oBAAoB,GAAG,aAAiC,CAAC;IAC/D,OAAO,aAAO,CAAC,IAAI,CACjB,aAAO,CAAC,MAAM,EAAE,EAChB,oBAAoB;SACjB,IAAI,CAAC,MAAM,CAAC,EAAE;QACb,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,KAAc,EAAE,EAAE;QACxB,IAAI,CAAC,SAAS,CAAC;YACb,IAAI,EAAE,oBAAc,CAAC,KAAK;YAC1B,OAAO,EAAE,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC;SACtC,CAAC,CAAC;QACH,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC,CAAC,CACL,CAAC;AACJ,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  isWrapped,\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  safeExecuteInTheMiddle,\n  InstrumentationNodeModuleFile,\n} from '@opentelemetry/instrumentation';\nimport {\n  context,\n  trace,\n  Span,\n  SpanStatusCode,\n  SpanKind,\n  Histogram,\n  ValueType,\n  Attributes,\n  HrTime,\n  UpDownCounter,\n} from '@opentelemetry/api';\nimport type * as pgTypes from 'pg';\nimport type * as pgPoolTypes from 'pg-pool';\nimport {\n  PgClientConnect,\n  PgClientExtended,\n  PostgresCallback,\n  PgPoolExtended,\n  PgPoolCallback,\n  EVENT_LISTENERS_SET,\n} from './internal-types';\nimport { PgInstrumentationConfig } from './types';\nimport * as utils from './utils';\nimport { addSqlCommenterComment } from '@opentelemetry/sql-common';\n/** @knipignore */\nimport { PACKAGE_NAME, PACKAGE_VERSION } from './version';\nimport { SpanNames } from './enums/SpanNames';\nimport {\n  hrTime,\n  hrTimeDuration,\n  hrTimeToMilliseconds,\n} from '@opentelemetry/core';\nimport {\n  DBSYSTEMVALUES_POSTGRESQL,\n  SEMATTRS_DB_SYSTEM,\n  ATTR_ERROR_TYPE,\n  ATTR_SERVER_PORT,\n  ATTR_SERVER_ADDRESS,\n} from '@opentelemetry/semantic-conventions';\nimport {\n  METRIC_DB_CLIENT_CONNECTION_COUNT,\n  METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS,\n  METRIC_DB_CLIENT_OPERATION_DURATION,\n  ATTR_DB_NAMESPACE,\n  ATTR_DB_OPERATION_NAME,\n} from './semconv';\n\nfunction extractModuleExports(module: any) {\n  return module[Symbol.toStringTag] === 'Module'\n    ? module.default // ESM\n    : module; // CommonJS\n}\n\nexport class PgInstrumentation extends InstrumentationBase<PgInstrumentationConfig> {\n  private _operationDuration!: Histogram;\n  private _connectionsCount!: UpDownCounter;\n  private _connectionPendingRequests!: UpDownCounter;\n  // Pool events connect, acquire, release and remove can be called\n  // multiple times without changing the values of total, idle and waiting\n  // connections. The _connectionsCounter is used to keep track of latest\n  // values and only update the metrics _connectionsCount and _connectionPendingRequests\n  // when the value change.\n  private _connectionsCounter: utils.poolConnectionsCounter = {\n    used: 0,\n    idle: 0,\n    pending: 0,\n  };\n\n  constructor(config: PgInstrumentationConfig = {}) {\n    super(PACKAGE_NAME, PACKAGE_VERSION, config);\n  }\n\n  override _updateMetricInstruments() {\n    this._operationDuration = this.meter.createHistogram(\n      METRIC_DB_CLIENT_OPERATION_DURATION,\n      {\n        description: 'Duration of database client operations.',\n        unit: 's',\n        valueType: ValueType.DOUBLE,\n        advice: {\n          explicitBucketBoundaries: [\n            0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5, 10,\n          ],\n        },\n      }\n    );\n\n    this._connectionsCounter = {\n      idle: 0,\n      pending: 0,\n      used: 0,\n    };\n    this._connectionsCount = this.meter.createUpDownCounter(\n      METRIC_DB_CLIENT_CONNECTION_COUNT,\n      {\n        description:\n          'The number of connections that are currently in state described by the state attribute.',\n        unit: '{connection}',\n      }\n    );\n    this._connectionPendingRequests = this.meter.createUpDownCounter(\n      METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS,\n      {\n        description:\n          'The number of current pending requests for an open connection.',\n        unit: '{connection}',\n      }\n    );\n  }\n\n  protected init() {\n    const SUPPORTED_PG_VERSIONS = ['>=8.0.3 <9'];\n\n    const modulePgNativeClient = new InstrumentationNodeModuleFile(\n      'pg/lib/native/client.js',\n      SUPPORTED_PG_VERSIONS,\n      this._patchPgClient.bind(this),\n      this._unpatchPgClient.bind(this)\n    );\n\n    const modulePgClient = new InstrumentationNodeModuleFile(\n      'pg/lib/client.js',\n      SUPPORTED_PG_VERSIONS,\n      this._patchPgClient.bind(this),\n      this._unpatchPgClient.bind(this)\n    );\n\n    const modulePG = new InstrumentationNodeModuleDefinition(\n      'pg',\n      SUPPORTED_PG_VERSIONS,\n      (module: any) => {\n        const moduleExports = extractModuleExports(module);\n\n        this._patchPgClient(moduleExports.Client);\n        return module;\n      },\n      (module: any) => {\n        const moduleExports = extractModuleExports(module);\n\n        this._unpatchPgClient(moduleExports.Client);\n        return module;\n      },\n      [modulePgClient, modulePgNativeClient]\n    );\n\n    const modulePGPool = new InstrumentationNodeModuleDefinition(\n      'pg-pool',\n      ['>=2.0.0 <4'],\n      (moduleExports: typeof pgPoolTypes) => {\n        if (isWrapped(moduleExports.prototype.connect)) {\n          this._unwrap(moduleExports.prototype, 'connect');\n        }\n        this._wrap(\n          moduleExports.prototype,\n          'connect',\n          this._getPoolConnectPatch() as any\n        );\n        return moduleExports;\n      },\n      (moduleExports: typeof pgPoolTypes) => {\n        if (isWrapped(moduleExports.prototype.connect)) {\n          this._unwrap(moduleExports.prototype, 'connect');\n        }\n      }\n    );\n\n    return [modulePG, modulePGPool];\n  }\n\n  private _patchPgClient(module: any) {\n    if (!module) {\n      return;\n    }\n\n    const moduleExports = extractModuleExports(module);\n\n    if (isWrapped(moduleExports.prototype.query)) {\n      this._unwrap(moduleExports.prototype, 'query');\n    }\n\n    if (isWrapped(moduleExports.prototype.connect)) {\n      this._unwrap(moduleExports.prototype, 'connect');\n    }\n\n    this._wrap(\n      moduleExports.prototype,\n      'query',\n      this._getClientQueryPatch() as any\n    );\n\n    this._wrap(\n      moduleExports.prototype,\n      'connect',\n      this._getClientConnectPatch() as any\n    );\n\n    return module;\n  }\n\n  private _unpatchPgClient(module: any) {\n    const moduleExports = extractModuleExports(module);\n\n    if (isWrapped(moduleExports.prototype.query)) {\n      this._unwrap(moduleExports.prototype, 'query');\n    }\n\n    if (isWrapped(moduleExports.prototype.connect)) {\n      this._unwrap(moduleExports.prototype, 'connect');\n    }\n\n    return module;\n  }\n\n  private _getClientConnectPatch() {\n    const plugin = this;\n    return (original: PgClientConnect) => {\n      return function connect(this: pgTypes.Client, callback?: Function) {\n        if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n          return original.call(this, callback);\n        }\n\n        const span = plugin.tracer.startSpan(SpanNames.CONNECT, {\n          kind: SpanKind.CLIENT,\n          attributes: utils.getSemanticAttributesFromConnection(this),\n        });\n\n        if (callback) {\n          const parentSpan = trace.getSpan(context.active());\n          callback = utils.patchClientConnectCallback(span, callback);\n          if (parentSpan) {\n            callback = context.bind(context.active(), callback);\n          }\n        }\n\n        const connectResult: unknown = context.with(\n          trace.setSpan(context.active(), span),\n          () => {\n            return original.call(this, callback);\n          }\n        );\n\n        return handleConnectResult(span, connectResult);\n      };\n    };\n  }\n\n  private recordOperationDuration(attributes: Attributes, startTime: HrTime) {\n    const metricsAttributes: Attributes = {};\n    const keysToCopy = [\n      SEMATTRS_DB_SYSTEM,\n      ATTR_DB_NAMESPACE,\n      ATTR_ERROR_TYPE,\n      ATTR_SERVER_PORT,\n      ATTR_SERVER_ADDRESS,\n      ATTR_DB_OPERATION_NAME,\n    ];\n\n    keysToCopy.forEach(key => {\n      if (key in attributes) {\n        metricsAttributes[key] = attributes[key];\n      }\n    });\n\n    const durationSeconds =\n      hrTimeToMilliseconds(hrTimeDuration(startTime, hrTime())) / 1000;\n    this._operationDuration.record(durationSeconds, metricsAttributes);\n  }\n\n  private _getClientQueryPatch() {\n    const plugin = this;\n    return (original: typeof pgTypes.Client.prototype.query) => {\n      this._diag.debug('Patching pg.Client.prototype.query');\n      return function query(this: PgClientExtended, ...args: unknown[]) {\n        if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n          return original.apply(this, args as never);\n        }\n        const startTime = hrTime();\n\n        // client.query(text, cb?), client.query(text, values, cb?), and\n        // client.query(configObj, cb?) are all valid signatures. We construct\n        // a queryConfig obj from all (valid) signatures to build the span in a\n        // unified way. We verify that we at least have query text, and code\n        // defensively when dealing with `queryConfig` after that (to handle all\n        // the other invalid cases, like a non-array for values being provided).\n        // The type casts here reflect only what we've actually validated.\n        const arg0 = args[0];\n        const firstArgIsString = typeof arg0 === 'string';\n        const firstArgIsQueryObjectWithText =\n          utils.isObjectWithTextString(arg0);\n\n        // TODO: remove the `as ...` casts below when the TS version is upgraded.\n        // Newer TS versions will use the result of firstArgIsQueryObjectWithText\n        // to properly narrow arg0, but TS 4.3.5 does not.\n        const queryConfig = firstArgIsString\n          ? {\n              text: arg0 as string,\n              values: Array.isArray(args[1]) ? args[1] : undefined,\n            }\n          : firstArgIsQueryObjectWithText\n          ? (arg0 as utils.ObjectWithText)\n          : undefined;\n\n        const attributes: Attributes = {\n          [SEMATTRS_DB_SYSTEM]: DBSYSTEMVALUES_POSTGRESQL,\n          [ATTR_DB_NAMESPACE]: this.database,\n          [ATTR_SERVER_PORT]: this.connectionParameters.port,\n          [ATTR_SERVER_ADDRESS]: this.connectionParameters.host,\n        };\n\n        if (queryConfig?.text) {\n          attributes[ATTR_DB_OPERATION_NAME] =\n            utils.parseNormalizedOperationName(queryConfig?.text);\n        }\n\n        const recordDuration = () => {\n          plugin.recordOperationDuration(attributes, startTime);\n        };\n\n        const instrumentationConfig = plugin.getConfig();\n\n        const span = utils.handleConfigQuery.call(\n          this,\n          plugin.tracer,\n          instrumentationConfig,\n          queryConfig\n        );\n\n        // Modify query text w/ a tracing comment before invoking original for\n        // tracing, but only if args[0] has one of our expected shapes.\n        if (instrumentationConfig.addSqlCommenterCommentToQueries) {\n          if (firstArgIsString) {\n            args[0] = addSqlCommenterComment(span, arg0);\n          } else if (firstArgIsQueryObjectWithText && !('name' in arg0)) {\n            // In the case of a query object, we need to ensure there's no name field\n            // as this indicates a prepared query, where the comment would remain the same\n            // for every invocation and contain an outdated trace context.\n            args[0] = {\n              ...arg0,\n              text: addSqlCommenterComment(span, arg0.text),\n            };\n          }\n        }\n\n        // Bind callback (if any) to parent span (if any)\n        if (args.length > 0) {\n          const parentSpan = trace.getSpan(context.active());\n          if (typeof args[args.length - 1] === 'function') {\n            // Patch ParameterQuery callback\n            args[args.length - 1] = utils.patchCallback(\n              instrumentationConfig,\n              span,\n              args[args.length - 1] as PostgresCallback, // nb: not type safe.\n              attributes,\n              recordDuration\n            );\n\n            // If a parent span exists, bind the callback\n            if (parentSpan) {\n              args[args.length - 1] = context.bind(\n                context.active(),\n                args[args.length - 1]\n              );\n            }\n          } else if (typeof queryConfig?.callback === 'function') {\n            // Patch ConfigQuery callback\n            let callback = utils.patchCallback(\n              plugin.getConfig(),\n              span,\n              queryConfig.callback as PostgresCallback, // nb: not type safe.\n              attributes,\n              recordDuration\n            );\n\n            // If a parent span existed, bind the callback\n            if (parentSpan) {\n              callback = context.bind(context.active(), callback);\n            }\n\n            (args[0] as { callback?: PostgresCallback }).callback = callback;\n          }\n        }\n\n        const { requestHook } = instrumentationConfig;\n        if (typeof requestHook === 'function' && queryConfig) {\n          safeExecuteInTheMiddle(\n            () => {\n              // pick keys to expose explicitly, so we're not leaking pg package\n              // internals that are subject to change\n              const { database, host, port, user } = this.connectionParameters;\n              const connection = { database, host, port, user };\n\n              requestHook(span, {\n                connection,\n                query: {\n                  text: queryConfig.text,\n                  // nb: if `client.query` is called with illegal arguments\n                  // (e.g., if `queryConfig.values` is passed explicitly, but a\n                  // non-array is given), then the type casts will be wrong. But\n                  // we leave it up to the queryHook to handle that, and we\n                  // catch and swallow any errors it throws. The other options\n                  // are all worse. E.g., we could leave `queryConfig.values`\n                  // and `queryConfig.name` as `unknown`, but then the hook body\n                  // would be forced to validate (or cast) them before using\n                  // them, which seems incredibly cumbersome given that these\n                  // casts will be correct 99.9% of the time -- and pg.query\n                  // will immediately throw during development in the other .1%\n                  // of cases. Alternatively, we could simply skip calling the\n                  // hook when `values` or `name` don't have the expected type,\n                  // but that would add unnecessary validation overhead to every\n                  // hook invocation and possibly be even more confusing/unexpected.\n                  values: queryConfig.values as unknown[],\n                  name: queryConfig.name as string | undefined,\n                },\n              });\n            },\n            err => {\n              if (err) {\n                plugin._diag.error('Error running query hook', err);\n              }\n            },\n            true\n          );\n        }\n\n        let result: unknown;\n        try {\n          result = original.apply(this, args as never);\n        } catch (e: unknown) {\n          span.setStatus({\n            code: SpanStatusCode.ERROR,\n            message: utils.getErrorMessage(e),\n          });\n          span.end();\n          throw e;\n        }\n\n        // Bind promise to parent span and end the span\n        if (result instanceof Promise) {\n          return result\n            .then((result: unknown) => {\n              // Return a pass-along promise which ends the span and then goes to user's orig resolvers\n              return new Promise(resolve => {\n                utils.handleExecutionResult(plugin.getConfig(), span, result);\n                recordDuration();\n                span.end();\n                resolve(result);\n              });\n            })\n            .catch((error: Error) => {\n              return new Promise((_, reject) => {\n                span.setStatus({\n                  code: SpanStatusCode.ERROR,\n                  message: error.message,\n                });\n                recordDuration();\n                span.end();\n                reject(error);\n              });\n            });\n        }\n\n        // else returns void\n        return result; // void\n      };\n    };\n  }\n\n  private _setPoolConnectEventListeners(pgPool: PgPoolExtended) {\n    if (pgPool[EVENT_LISTENERS_SET]) return;\n    const poolName = utils.getPoolName(pgPool.options);\n\n    pgPool.on('connect', () => {\n      this._connectionsCounter = utils.updateCounter(\n        poolName,\n        pgPool,\n        this._connectionsCount,\n        this._connectionPendingRequests,\n        this._connectionsCounter\n      );\n    });\n\n    pgPool.on('acquire', () => {\n      this._connectionsCounter = utils.updateCounter(\n        poolName,\n        pgPool,\n        this._connectionsCount,\n        this._connectionPendingRequests,\n        this._connectionsCounter\n      );\n    });\n\n    pgPool.on('remove', () => {\n      this._connectionsCounter = utils.updateCounter(\n        poolName,\n        pgPool,\n        this._connectionsCount,\n        this._connectionPendingRequests,\n        this._connectionsCounter\n      );\n    });\n\n    pgPool.on('release' as any, () => {\n      this._connectionsCounter = utils.updateCounter(\n        poolName,\n        pgPool,\n        this._connectionsCount,\n        this._connectionPendingRequests,\n        this._connectionsCounter\n      );\n    });\n    pgPool[EVENT_LISTENERS_SET] = true;\n  }\n\n  private _getPoolConnectPatch() {\n    const plugin = this;\n    return (originalConnect: typeof pgPoolTypes.prototype.connect) => {\n      return function connect(this: PgPoolExtended, callback?: PgPoolCallback) {\n        if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n          return originalConnect.call(this, callback as any);\n        }\n\n        // setup span\n        const span = plugin.tracer.startSpan(SpanNames.POOL_CONNECT, {\n          kind: SpanKind.CLIENT,\n          attributes: utils.getSemanticAttributesFromPool(this.options),\n        });\n\n        plugin._setPoolConnectEventListeners(this);\n\n        if (callback) {\n          const parentSpan = trace.getSpan(context.active());\n          callback = utils.patchCallbackPGPool(\n            span,\n            callback\n          ) as PgPoolCallback;\n          // If a parent span exists, bind the callback\n          if (parentSpan) {\n            callback = context.bind(context.active(), callback);\n          }\n        }\n\n        const connectResult: unknown = context.with(\n          trace.setSpan(context.active(), span),\n          () => {\n            return originalConnect.call(this, callback as any);\n          }\n        );\n\n        return handleConnectResult(span, connectResult);\n      };\n    };\n  }\n}\n\nfunction handleConnectResult(span: Span, connectResult: unknown) {\n  if (!(connectResult instanceof Promise)) {\n    return connectResult;\n  }\n\n  const connectResultPromise = connectResult as Promise<unknown>;\n  return context.bind(\n    context.active(),\n    connectResultPromise\n      .then(result => {\n        span.end();\n        return result;\n      })\n      .catch((error: unknown) => {\n        span.setStatus({\n          code: SpanStatusCode.ERROR,\n          message: utils.getErrorMessage(error),\n        });\n        span.end();\n        return Promise.reject(error);\n      })\n  );\n}\n"]}