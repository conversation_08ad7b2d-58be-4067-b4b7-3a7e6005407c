/**
 * Builds the "Business Plan Overview" part of the main prompt.
 * This function generates the high-level summary of the business plan.
 * @param {object} params - The parameters object.
 * @param {string} params.name - The user's provided business name.
 * @param {string} params.period - The user's selected business plan period (e.g., "3 Months").
 * @returns {string} The generated prompt part.
 */
export const buildOverviewPromptPart = ({ name, period }) => {
    const businessName = name || 'the business idea';
    return `
### PART 1: BUSINESS PLAN OVERVIEW ###
~H~ Business Plan Overview for ${businessName}
~S~ Core Concept
~P~ [Summarize the user's idea and the problem it solves in one paragraph based on the vision provided.]
~S~ Strategic Goals for ${period}
~L~ [Generate a high-level goal for the specified period.]
~L~ [Generate a second high-level goal for the specified period.]
~L~ [Generate a third high-level goal for the specified period.]
~S~ Target Market & Monetization
~P~ [Briefly describe the target audience and how the revenue strategy applies to them based on the vision provided.]
    `;
};