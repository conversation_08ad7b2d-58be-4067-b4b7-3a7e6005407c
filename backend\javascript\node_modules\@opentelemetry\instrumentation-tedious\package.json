{"name": "@opentelemetry/instrumentation-tedious", "version": "0.18.1", "description": "OpenTelemetry instrumentation for `tedious` database client for Microsoft SQL Server", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"clean": "rimraf build/*", "compile": "tsc -p .", "lint:fix": "eslint . --ext .ts --fix", "lint": "eslint . --ext .ts", "prewatch": "npm run precompile", "prepublishOnly": "npm run compile", "tdd": "npm run test -- --watch-extensions ts --watch", "test": "nyc mocha 'test/**/*.test.ts'", "test-all-versions": "tav", "version:update": "node ../../../scripts/version-update.js"}, "keywords": ["instrumentation", "microsoft", "mssql", "nodejs", "opentelemetry", "profiling", "sql server", "tds", "tedious", "tracing"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "devDependencies": {"@opentelemetry/api": "^1.3.0", "@opentelemetry/context-async-hooks": "^1.8.0", "@opentelemetry/contrib-test-utils": "^0.45.1", "@opentelemetry/sdk-trace-base": "^1.8.0", "@types/mocha": "7.0.2", "@types/node": "18.18.14", "nyc": "15.1.0", "rimraf": "5.0.10", "semver": "7.7.1", "tedious": "17.0.0", "test-all-versions": "6.1.0", "typescript": "4.4.4"}, "dependencies": {"@opentelemetry/instrumentation": "^0.57.1", "@opentelemetry/semantic-conventions": "^1.27.0", "@types/tedious": "^4.0.14"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/instrumentation-tedious#readme", "gitHead": "1eb77007669bae87fe5664d68ba6533b95275d52"}