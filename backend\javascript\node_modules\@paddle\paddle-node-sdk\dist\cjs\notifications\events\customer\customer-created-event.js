"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerCreatedEvent = void 0;
const event_js_1 = require("../../../entities/events/event.js");
const index_js_1 = require("../../helpers/index.js");
const index_js_2 = require("../../entities/index.js");
class CustomerCreatedEvent extends event_js_1.Event {
    constructor(response) {
        super(response);
        this.eventType = index_js_1.EventName.CustomerCreated;
        this.data = new index_js_2.CustomerNotification(response.data);
    }
}
exports.CustomerCreatedEvent = CustomerCreatedEvent;
