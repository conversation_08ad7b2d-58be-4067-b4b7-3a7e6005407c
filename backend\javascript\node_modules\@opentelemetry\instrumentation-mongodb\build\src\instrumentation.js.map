{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,4CAM4B;AAC5B,oEAMwC;AACxC,8EAU6C;AAE7C,qDAU0B;AAE1B,kBAAkB;AAClB,uCAA0D;AAG1D,MAAM,cAAc,GAAiC;IACnD,iBAAiB,EAAE,IAAI;CACxB,CAAC;AAEF,uDAAuD;AACvD,MAAa,sBAAuB,SAAQ,qCAAiD;IAI3F,YAAY,SAAuC,EAAE;QACnD,KAAK,CAAC,sBAAY,EAAE,yBAAe,kCAAO,cAAc,GAAK,MAAM,EAAG,CAAC;IACzE,CAAC;IAEQ,SAAS,CAAC,SAAuC,EAAE;QAC1D,KAAK,CAAC,SAAS,iCAAM,cAAc,GAAK,MAAM,EAAG,CAAC;IACpD,CAAC;IAEQ,wBAAwB;QAC/B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CACrD,6BAA6B,EAC7B;YACE,WAAW,EACT,yFAAyF;YAC3F,IAAI,EAAE,cAAc;SACrB,CACF,CAAC;IACJ,CAAC;IAED,IAAI;QACF,MAAM,EACJ,iBAAiB,EAAE,iBAAiB,EACpC,mBAAmB,EAAE,mBAAmB,GACzC,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAEnC,MAAM,EAAE,cAAc,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACzE,MAAM,EACJ,yBAAyB,EACzB,wBAAwB,EACxB,mBAAmB,GACpB,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QACnC,MAAM,EAAE,qBAAqB,EAAE,uBAAuB,EAAE,GACtD,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACrC,MAAM,EAAE,eAAe,EAAE,iBAAiB,EAAE,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE5E,OAAO;YACL,IAAI,qDAAmC,CACrC,SAAS,EACT,CAAC,YAAY,CAAC,EACd,SAAS,EACT,SAAS,EACT;gBACE,IAAI,+CAA6B,CAC/B,wCAAwC,EACxC,CAAC,YAAY,CAAC,EACd,iBAAiB,EACjB,mBAAmB,CACpB;aACF,CACF;YACD,IAAI,qDAAmC,CACrC,SAAS,EACT,CAAC,YAAY,CAAC,EACd,SAAS,EACT,SAAS,EACT;gBACE,IAAI,+CAA6B,CAC/B,gCAAgC,EAChC,CAAC,cAAc,CAAC,EAChB,yBAAyB,EACzB,mBAAmB,CACpB;gBACD,IAAI,+CAA6B,CAC/B,gCAAgC,EAChC,CAAC,YAAY,CAAC,EACd,wBAAwB,EACxB,mBAAmB,CACpB;gBACD,IAAI,+CAA6B,CAC/B,qCAAqC,EACrC,CAAC,cAAc,CAAC,EAChB,qBAAqB,EACrB,uBAAuB,CACxB;gBACD,IAAI,+CAA6B,CAC/B,6BAA6B,EAC7B,CAAC,YAAY,CAAC,EACd,cAAc,EACd,gBAAgB,CACjB;gBACD,IAAI,+CAA6B,CAC/B,yBAAyB,EACzB,CAAC,YAAY,CAAC,EACd,eAAe,EACf,iBAAiB,CAClB;aACF,CACF;SACF,CAAC;IACJ,CAAC;IAEO,uBAAuB;QAC7B,OAAO;YACL,iBAAiB,EAAE,CAAC,aAAgB,EAAE,EAAE;gBACtC,yBAAyB;gBACzB,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,MAAM,CAAC,EAAE;oBACnC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;iBACvC;gBACD,IAAI,CAAC,KAAK,CACR,aAAa,EACb,QAAQ,EACR,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CACpC,CAAC;gBACF,yBAAyB;gBACzB,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,MAAM,CAAC,EAAE;oBACnC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;iBACvC;gBACD,IAAI,CAAC,KAAK,CACR,aAAa,EACb,QAAQ,EACR,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CACpC,CAAC;gBACF,yBAAyB;gBACzB,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,MAAM,CAAC,EAAE;oBACnC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;iBACvC;gBACD,IAAI,CAAC,KAAK,CACR,aAAa,EACb,QAAQ,EACR,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CACpC,CAAC;gBACF,sBAAsB;gBACtB,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,EAAE;oBACpC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;iBACxC;gBACD,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;gBAChE,cAAc;gBACd,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,KAAK,CAAC,EAAE;oBAClC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;iBACtC;gBACD,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;gBAC3D,qCAAqC;gBACrC,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,EAAE;oBACpC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;iBACxC;gBACD,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,SAAS,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;gBAC/D,OAAO,aAAa,CAAC;YACvB,CAAC;YACD,mBAAmB,EAAE,CAAC,aAAiB,EAAE,EAAE;gBACzC,IAAI,aAAa,KAAK,SAAS;oBAAE,OAAO;gBACxC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;gBACtC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;gBACtC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;gBACtC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;gBACvC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;gBACrC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;YACzC,CAAC;SACF,CAAC;IACJ,CAAC;IAEO,qBAAqB;QAC3B,OAAO;YACL,eAAe,EAAE,CAAC,aAAkB,EAAE,EAAE;gBACtC,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,EAAE;oBACpC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;iBACxC;gBACD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,iBAAiB,CAAC,SAAS,EACzC,SAAS,EACT,IAAI,CAAC,oBAAoB,EAAE,CAC5B,CAAC;gBAEF,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,EAAE;oBACpC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;iBACxC;gBACD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,iBAAiB,CAAC,SAAS,EACzC,SAAS,EACT,IAAI,CAAC,oBAAoB,EAAE,CAC5B,CAAC;gBACF,OAAO,aAAa,CAAC;YACvB,CAAC;YACD,iBAAiB,EAAE,CAAC,aAAiB,EAAE,EAAE;gBACvC,IAAI,aAAa,KAAK,SAAS;oBAAE,OAAO;gBACxC,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,EAAE;oBACpC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;iBACxC;gBACD,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,EAAE;oBACpC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;iBACxC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;IAEO,oBAAoB;QAC1B,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,CAAC,QAA8B,EAAE,EAAE;YACxC,OAAO,SAAS,YAAY;gBAC1B,MAAM,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;gBACpD,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpC,MAAM,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAEnD,IAAI,sBAAsB,KAAK,qBAAqB,EAAE;oBACpD,4DAA4D;oBAC5D,eAAe,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE;wBACvC,KAAK,EAAE,MAAM;wBACb,WAAW,EAAE,eAAe,CAAC,SAAS;qBACvC,CAAC,CAAC;iBACJ;qBAAM,IAAI,sBAAsB,GAAG,CAAC,KAAK,qBAAqB,EAAE;oBAC/D,wEAAwE;oBACxE,eAAe,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;wBACxC,KAAK,EAAE,MAAM;wBACb,WAAW,EAAE,eAAe,CAAC,SAAS;qBACvC,CAAC,CAAC;oBACH,eAAe,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE;wBACvC,KAAK,EAAE,MAAM;wBACb,WAAW,EAAE,eAAe,CAAC,SAAS;qBACvC,CAAC,CAAC;iBACJ;gBACD,OAAO,OAAO,CAAC;YACjB,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,oBAAoB;QAC1B,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,CAAC,QAA8B,EAAE,EAAE;YACxC,OAAO,SAAS,YAAY,CAAY,OAAsB;gBAC5D,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBAEhD,eAAe,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;oBACxC,KAAK,EAAE,MAAM;oBACb,WAAW,EAAE,eAAe,CAAC,SAAS;iBACvC,CAAC,CAAC;gBACH,eAAe,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE;oBACvC,KAAK,EAAE,MAAM;oBACb,WAAW,EAAE,eAAe,CAAC,SAAS;iBACvC,CAAC,CAAC;gBACH,OAAO,UAAU,CAAC;YACpB,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,2BAA2B;QACjC,OAAO;YACL,qBAAqB,EAAE,CAAC,aAAkB,EAAE,EAAE;gBAC5C,MAAM,aAAa,GAAG,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC;gBAE7D,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;oBACrC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;iBACzC;gBAED,IAAI,CAAC,KAAK,CACR,aAAa,EACb,UAAU,EACV,IAAI,CAAC,4BAA4B,EAAE,CACpC,CAAC;gBACF,OAAO,aAAa,CAAC;YACvB,CAAC;YACD,uBAAuB,EAAE,CAAC,aAAmB,EAAE,EAAE;gBAC/C,IAAI,aAAa,KAAK,SAAS;oBAAE,OAAO;gBAExC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,cAAc,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;YACnE,CAAC;SACF,CAAC;IACJ,CAAC;IAEO,oBAAoB;QAC1B,OAAO;YACL,cAAc,EAAE,CAAC,aAAkB,EAAE,EAAE;gBACrC,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,EAAE;oBACpC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;iBACxC;gBAED,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,SAAS,EAAE,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC;gBAClE,OAAO,aAAa,CAAC;YACvB,CAAC;YACD,gBAAgB,EAAE,CAAC,aAAiB,EAAE,EAAE;gBACtC,IAAI,aAAa,KAAK,SAAS;oBAAE,OAAO;gBAExC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;YACzC,CAAC;SACF,CAAC;IACJ,CAAC;IAED,0CAA0C;IAC1C,qDAAqD;IAC7C,4BAA4B;QAClC,OAAO,CAAC,QAAsC,EAAE,EAAE;YAChD,OAAO,SAAS,eAAe,CAAgB,QAAa;gBAC1D,MAAM,eAAe,GAAG,aAAO,CAAC,IAAI,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;gBACjE,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;YAC9C,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,oBAAoB;QAC1B,MAAM,eAAe,GAAG,IAAI,CAAC;QAE7B,OAAO,CACL,QAAoE,EACpE,EAAE;YACF,OAAO,SAAS,cAAc,CAE5B,OAAY,EACZ,QAAa;gBAEb,iFAAiF;gBACjF,sBAAsB;gBACtB,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;oBACzB,MAAM,MAAM,GAAI,QAAwC,CAAC,IAAI,CAC3D,IAAI,EACJ,OAAO,CACR,CAAC;oBACF,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE;wBAC/C,MAAM,CAAC,IAAI,CACT,GAAG,EAAE,CAAC,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC;wBAC1C,6CAA6C;wBAC7C,GAAG,EAAE,CAAC,SAAS,CAChB,CAAC;qBACH;oBACD,OAAO,MAAM,CAAC;iBACf;gBAED,4DAA4D;gBAC5D,MAAM,eAAe,GAAG,UAAU,GAAQ,EAAE,IAAS;oBACnD,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE;wBAChB,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;wBACpB,OAAO;qBACR;oBACD,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;oBACrC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;gBACtB,CAAC,CAAC;gBAEF,OAAQ,QAAyC,CAAC,IAAI,CACpD,IAAI,EACJ,OAAO,EACP,eAAe,CAChB,CAAC;YACJ,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED,6DAA6D;IACrD,uBAAuB;QAC7B,OAAO;YACL,yBAAyB,EAAE,CAAC,aAAkB,EAAE,EAAE;gBAChD,yBAAyB;gBACzB,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;oBACzD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;iBAC7D;gBAED,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,UAAU,CAAC,SAAS,EAClC,SAAS,EACT,IAAI,CAAC,0BAA0B,EAAE,CAClC,CAAC;gBACF,OAAO,aAAa,CAAC;YACvB,CAAC;YACD,wBAAwB,EAAE,CAAC,aAAkB,EAAE,EAAE;gBAC/C,yBAAyB;gBACzB,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;oBACzD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;iBAC7D;gBAED,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,UAAU,CAAC,SAAS,EAClC,SAAS,EACT,IAAI,CAAC,yBAAyB,EAAE,CACjC,CAAC;gBACF,OAAO,aAAa,CAAC;YACvB,CAAC;YACD,mBAAmB,EAAE,CAAC,aAAmB,EAAE,EAAE;gBAC3C,IAAI,aAAa,KAAK,SAAS;oBAAE,OAAO;gBACxC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YAC9D,CAAC;SACF,CAAC;IACJ,CAAC;IAED,0CAA0C;IAClC,oBAAoB,CAAC,aAA6C;QACxE,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,CAAC,QAAoD,EAAE,EAAE;YAC9D,OAAO,SAAS,oBAAoB,CAElC,MAA6B,EAC7B,EAAU,EACV,GAAc,EACd,OAA2B,EAC3B,QAAmB;gBAEnB,MAAM,WAAW,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBACpD,MAAM,mBAAmB,GACvB,eAAe,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;gBAEzD,MAAM,aAAa,GACjB,OAAO,OAAO,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;gBACrD,IACE,mBAAmB;oBACnB,OAAO,aAAa,KAAK,UAAU;oBACnC,OAAO,GAAG,KAAK,QAAQ,EACvB;oBACA,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;wBACjC,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;qBACtD;yBAAM;wBACL,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;qBAChE;iBACF;gBAED,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,SAAS,CAC3C,WAAW,aAAa,EAAE,EAC1B;oBACE,IAAI,EAAE,cAAQ,CAAC,MAAM;iBACtB,CACF,CAAC;gBAEF,eAAe,CAAC,qBAAqB,CACnC,IAAI,EACJ,EAAE,EACF,MAAM;gBACN,8DAA8D;gBAC9D,GAAG,CAAC,CAAC,CAAQ,EACb,aAAa,CACd,CAAC;gBACF,MAAM,eAAe,GAAG,eAAe,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;gBACvE,yEAAyE;gBACzE,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;oBACjC,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,eAAe,CAAC,CAAC;iBAC9D;qBAAM;oBACL,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC;iBACvE;YACH,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED,0CAA0C;IAClC,kBAAkB;QACxB,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,CAAC,QAAyC,EAAE,EAAE;YACnD,OAAO,SAAS,oBAAoB,CAElC,MAA6B,EAC7B,EAAU,EACV,GAAyB,EACzB,OAA2B,EAC3B,QAAmB;gBAEnB,MAAM,WAAW,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBACpD,MAAM,mBAAmB,GACvB,eAAe,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;gBAEzD,MAAM,aAAa,GACjB,OAAO,OAAO,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;gBAErD,IACE,mBAAmB;oBACnB,OAAO,aAAa,KAAK,UAAU;oBACnC,OAAO,GAAG,KAAK,QAAQ,EACvB;oBACA,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;wBACjC,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;qBACtD;yBAAM;wBACL,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;qBAChE;iBACF;gBAED,MAAM,WAAW,GAAG,sBAAsB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;gBAChE,MAAM,IAAI,GACR,WAAW,KAAK,mCAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC;gBACvE,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,IAAI,EAAE,EAAE;oBAC/D,IAAI,EAAE,cAAQ,CAAC,MAAM;iBACtB,CAAC,CAAC;gBACH,MAAM,SAAS,GACb,WAAW,KAAK,mCAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC;gBACvE,eAAe,CAAC,qBAAqB,CAAC,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;gBACxE,MAAM,eAAe,GAAG,eAAe,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;gBACvE,yEAAyE;gBACzE,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;oBACjC,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,eAAe,CAAC,CAAC;iBAC9D;qBAAM;oBACL,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC;iBACvE;YACH,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED,0CAA0C;IAClC,0BAA0B;QAChC,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,CAAC,QAAyC,EAAE,EAAE;YACnD,OAAO,SAAS,sBAAsB,CAEpC,EAAoB,EACpB,GAAQ,EACR,OAA4B,EAC5B,QAAa;gBAEb,MAAM,WAAW,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBACpD,MAAM,mBAAmB,GACvB,eAAe,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;gBACzD,MAAM,aAAa,GAAG,QAAQ,CAAC;gBAC/B,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBAExC,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,KAAK,EAAE;oBACxD,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;iBACxD;gBAED,IAAI,IAAI,GAAG,SAAS,CAAC;gBACrB,IAAI,CAAC,mBAAmB,EAAE;oBACxB,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,WAAW,EAAE,EAAE;wBAChE,IAAI,EAAE,cAAQ,CAAC,MAAM;qBACtB,CAAC,CAAC;oBACH,eAAe,CAAC,qBAAqB,CACnC,IAAI,EACJ,IAAI,EACJ,EAAE,EACF,GAAG,EACH,WAAW,CACZ,CAAC;iBACH;gBACD,MAAM,eAAe,GAAG,eAAe,CAAC,SAAS,CAC/C,IAAI,EACJ,aAAa,EACb,IAAI,CAAC,EAAE,EACP,WAAW,CACZ,CAAC;gBAEF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC;YAChE,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,yBAAyB;QAC/B,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,CAAC,QAAwC,EAAE,EAAE;YAClD,OAAO,SAAS,sBAAsB,CAEpC,GAAG,IAAgD;gBAEnD,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC;gBACvB,MAAM,WAAW,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBACpD,MAAM,mBAAmB,GACvB,eAAe,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;gBAEzD,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,MAAM,aAAa,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC;gBAEtC,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,KAAK,EAAE;oBACxD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;iBACnC;gBAED,IAAI,IAAI,GAAG,SAAS,CAAC;gBACrB,IAAI,CAAC,mBAAmB,EAAE;oBACxB,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,WAAW,EAAE,EAAE;wBAChE,IAAI,EAAE,cAAQ,CAAC,MAAM;qBACtB,CAAC,CAAC;oBACH,eAAe,CAAC,qBAAqB,CACnC,IAAI,EACJ,IAAI,EACJ,EAAE,EACF,GAAG,EACH,WAAW,CACZ,CAAC;iBACH;gBAED,MAAM,eAAe,GAAG,eAAe,CAAC,SAAS,CAC/C,IAAI,EACJ,aAAa,EACb,IAAI,CAAC,EAAE,EACP,WAAW,CACZ,CAAC;gBAEF,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAC1C,MAAM,CAAC,IAAI,CACT,CAAC,GAAQ,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC,EACxC,CAAC,GAAQ,EAAE,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC,CACnC,CAAC;gBAEF,OAAO,MAAM,CAAC;YAChB,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED,uCAAuC;IAC/B,eAAe;QACrB,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,CAAC,QAAuC,EAAE,EAAE;YACjD,OAAO,SAAS,oBAAoB,CAElC,MAA6B,EAC7B,EAAU,EACV,GAAyB,EACzB,WAAwB,EACxB,OAA2B,EAC3B,QAAmB;gBAEnB,MAAM,WAAW,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBACpD,MAAM,mBAAmB,GACvB,eAAe,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;gBACzD,MAAM,aAAa,GACjB,OAAO,OAAO,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;gBAErD,IACE,mBAAmB;oBACnB,OAAO,aAAa,KAAK,UAAU;oBACnC,OAAO,GAAG,KAAK,QAAQ,EACvB;oBACA,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;wBACjC,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;qBACnE;yBAAM;wBACL,OAAO,QAAQ,CAAC,IAAI,CAClB,IAAI,EACJ,MAAM,EACN,EAAE,EACF,GAAG,EACH,WAAW,EACX,OAAO,EACP,QAAQ,CACT,CAAC;qBACH;iBACF;gBAED,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE;oBAC5D,IAAI,EAAE,cAAQ,CAAC,MAAM;iBACtB,CAAC,CAAC;gBACH,eAAe,CAAC,qBAAqB,CAAC,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;gBACrE,MAAM,eAAe,GAAG,eAAe,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;gBACvE,yEAAyE;gBACzE,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;oBACjC,OAAO,QAAQ,CAAC,IAAI,CAClB,IAAI,EACJ,MAAM,EACN,EAAE,EACF,GAAG,EACH,WAAW,EACX,eAAe,CAChB,CAAC;iBACH;qBAAM;oBACL,OAAO,QAAQ,CAAC,IAAI,CAClB,IAAI,EACJ,MAAM,EACN,EAAE,EACF,GAAG,EACH,WAAW,EACX,OAAO,EACP,eAAe,CAChB,CAAC;iBACH;YACH,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED,uCAAuC;IAC/B,iBAAiB;QACvB,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,CAAC,QAAyC,EAAE,EAAE;YACnD,OAAO,SAAS,oBAAoB,CAElC,MAA6B,EAC7B,EAAU,EACV,WAAwB,EACxB,SAAiB,EACjB,OAA2B,EAC3B,QAAmB;gBAEnB,MAAM,WAAW,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBACpD,MAAM,mBAAmB,GACvB,eAAe,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;gBAEzD,MAAM,aAAa,GACjB,OAAO,OAAO,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;gBAErD,IAAI,mBAAmB,IAAI,OAAO,aAAa,KAAK,UAAU,EAAE;oBAC9D,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;wBACjC,OAAO,QAAQ,CAAC,IAAI,CAClB,IAAI,EACJ,MAAM,EACN,EAAE,EACF,WAAW,EACX,SAAS,EACT,OAAO,CACR,CAAC;qBACH;yBAAM;wBACL,OAAO,QAAQ,CAAC,IAAI,CAClB,IAAI,EACJ,MAAM,EACN,EAAE,EACF,WAAW,EACX,SAAS,EACT,OAAO,EACP,QAAQ,CACT,CAAC;qBACH;iBACF;gBAED,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE;oBAC/D,IAAI,EAAE,cAAQ,CAAC,MAAM;iBACtB,CAAC,CAAC;gBACH,eAAe,CAAC,qBAAqB,CACnC,IAAI,EACJ,EAAE,EACF,MAAM,EACN,WAAW,CAAC,GAAG,EACf,SAAS,CACV,CAAC;gBACF,MAAM,eAAe,GAAG,eAAe,CAAC,SAAS,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;gBACvE,yEAAyE;gBACzE,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;oBACjC,OAAO,QAAQ,CAAC,IAAI,CAClB,IAAI,EACJ,MAAM,EACN,EAAE,EACF,WAAW,EACX,SAAS,EACT,eAAe,CAChB,CAAC;iBACH;qBAAM;oBACL,OAAO,QAAQ,CAAC,IAAI,CAClB,IAAI,EACJ,MAAM,EACN,EAAE,EACF,WAAW,EACX,SAAS,EACT,OAAO,EACP,eAAe,CAChB,CAAC;iBACH;YACH,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED;;;OAGG;IACK,MAAM,CAAC,eAAe,CAC5B,OAA6B;QAE7B,IAAI,OAAO,CAAC,aAAa,KAAK,SAAS,EAAE;YACvC,OAAO,mCAAkB,CAAC,cAAc,CAAC;SAC1C;aAAM,IAAI,OAAO,CAAC,aAAa,KAAK,SAAS,EAAE;YAC9C,OAAO,mCAAkB,CAAC,eAAe,CAAC;SAC3C;aAAM,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS,EAAE;YACzC,OAAO,mCAAkB,CAAC,SAAS,CAAC;SACrC;aAAM,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YACtC,OAAO,mCAAkB,CAAC,KAAK,CAAC;SACjC;aAAM,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE;YAC1C,OAAO,mCAAkB,CAAC,SAAS,CAAC;SACrC;aAAM;YACL,OAAO,mCAAkB,CAAC,OAAO,CAAC;SACnC;IACH,CAAC;IAED;;;;;;OAMG;IACK,qBAAqB,CAC3B,IAAU,EACV,aAAkB,EAClB,EAAoB,EACpB,OAAa,EACb,SAAkB;QAElB,IAAI,IAAI,EAAE,IAAwB,CAAC;QACnC,IAAI,aAAa,EAAE;YACjB,MAAM,SAAS,GACb,OAAO,aAAa,CAAC,OAAO,KAAK,QAAQ;gBACvC,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;gBAClC,CAAC,CAAC,EAAE,CAAC;YACT,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC1B,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;gBACpB,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;aACrB;SACF;QACD,uFAAuF;QACvF,IAAI,UAAmC,CAAC;QACxC,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,KAAI,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;YAC9C,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;SACnC;aAAM,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,EAAE;YAC3B,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC;SAC9B;aAAM;YACL,UAAU,GAAG,OAAO,CAAC;SACtB;QAED,IAAI,CAAC,qBAAqB,CACxB,IAAI,EACJ,EAAE,CAAC,EAAE,EACL,EAAE,CAAC,UAAU,EACb,IAAI,EACJ,IAAI,EACJ,UAAU,EACV,SAAS,CACV,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACK,qBAAqB,CAC3B,IAAU,EACV,EAAU,EACV,QAA+B,EAC/B,OAA8B,EAC9B,SAA8B;;QAE9B,wDAAwD;QACxD,IAAI,IAAwB,CAAC;QAC7B,IAAI,IAAwB,CAAC;QAC7B,IAAI,QAAQ,IAAI,QAAQ,CAAC,CAAC,EAAE;YAC1B,IAAI,GAAG,MAAA,MAAA,QAAQ,CAAC,CAAC,CAAC,OAAO,0CAAE,IAAI,mCAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;YACnD,IAAI,GAAG,MAAA,CAAC,MAAA,MAAA,QAAQ,CAAC,CAAC,CAAC,OAAO,0CAAE,IAAI,mCAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,0CAAE,QAAQ,EAAE,CAAC;YACjE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;gBAChC,MAAM,OAAO,GAAG,MAAA,QAAQ,CAAC,WAAW,0CAAE,OAAO,CAAC;gBAC9C,IAAI,OAAO,EAAE;oBACX,MAAM,eAAe,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBAC3C,IAAI,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;oBAC1B,IAAI,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;iBAC3B;aACF;SACF;QAED,0EAA0E;QAC1E,4EAA4E;QAC5E,sEAAsE;QACtE,0DAA0D;QAC1D,MAAM,CAAC,MAAM,EAAE,YAAY,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACxD,uFAAuF;QACvF,MAAM,UAAU,GAAG,MAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,mCAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,CAAC,mCAAI,OAAO,CAAC;QAE3D,IAAI,CAAC,qBAAqB,CACxB,IAAI,EACJ,MAAM,EACN,YAAY,EACZ,IAAI,EACJ,IAAI,EACJ,UAAU,EACV,SAAS,CACV,CAAC;IACJ,CAAC;IAEO,qBAAqB,CAC3B,IAAU,EACV,MAAe,EACf,YAAqB,EACrB,IAAyB,EACzB,IAAyB,EACzB,UAAgB,EAChB,SAA8B;QAE9B,kCAAkC;QAClC,IAAI,CAAC,aAAa,CAAC;YACjB,CAAC,yCAAkB,CAAC,EAAE,6CAAsB;YAC5C,CAAC,uCAAgB,CAAC,EAAE,MAAM;YAC1B,CAAC,qDAA8B,CAAC,EAAE,YAAY;YAC9C,CAAC,4CAAqB,CAAC,EAAE,SAAS;YAClC,CAAC,oDAA6B,CAAC,EAAE,aAAa,IAAI,IAAI,IAAI,IAAI,MAAM,EAAE;SACvE,CAAC,CAAC;QAEH,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,IAAI,CAAC,YAAY,CAAC,6CAAsB,EAAE,IAAI,CAAC,CAAC;YAChD,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YACtC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;gBACtB,IAAI,CAAC,YAAY,CAAC,6CAAsB,EAAE,UAAU,CAAC,CAAC;aACvD;SACF;QACD,IAAI,CAAC,UAAU;YAAE,OAAO;QAExB,MAAM,EAAE,qBAAqB,EAAE,2BAA2B,EAAE,GAC1D,IAAI,CAAC,SAAS,EAAE,CAAC;QACnB,MAAM,qBAAqB,GACzB,OAAO,2BAA2B,KAAK,UAAU;YAC/C,CAAC,CAAC,2BAA2B;YAC7B,CAAC,CAAC,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEpD,IAAA,wCAAsB,EACpB,GAAG,EAAE;YACH,MAAM,KAAK,GAAG,qBAAqB,CAAC,UAAU,CAAC,CAAC;YAChD,IAAI,CAAC,YAAY,CAAC,4CAAqB,EAAE,KAAK,CAAC,CAAC;QAClD,CAAC,EACD,GAAG,CAAC,EAAE;YACJ,IAAI,GAAG,EAAE;gBACP,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,0CAA0C,EAAE,GAAG,CAAC,CAAC;aACnE;QACH,CAAC,EACD,IAAI,CACL,CAAC;IACJ,CAAC;IAEO,6BAA6B,CAAC,UAAmC;QACvE,MAAM,EAAE,yBAAyB,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QACvD,MAAM,SAAS,GAAG,yBAAyB;YACzC,CAAC,CAAC,UAAU;YACZ,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QACrC,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;IAEO,eAAe,CAAC,KAAc;QACpC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACxB,OAAO,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC;SAC5D;QAED,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE;YAC/C,OAAO,MAAM,CAAC,WAAW,CACvB,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC;gBAC5C,GAAG;gBACH,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;aAC9B,CAAC,CACH,CAAC;SACH;QAED,iEAAiE;QACjE,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;;;OAIG;IACK,sBAAsB,CAAC,IAAU,EAAE,MAAqB;QAC9D,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC1C,IAAI,OAAO,YAAY,KAAK,UAAU,EAAE;YACtC,IAAA,wCAAsB,EACpB,GAAG,EAAE;gBACH,YAAY,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;YACvC,CAAC,EACD,GAAG,CAAC,EAAE;gBACJ,IAAI,GAAG,EAAE;oBACP,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC;iBACtD;YACH,CAAC,EACD,IAAI,CACL,CAAC;SACH;IACH,CAAC;IAED;;;;;OAKG;IACK,SAAS,CACf,IAAsB,EACtB,aAAuB,EACvB,YAAqB,EACrB,WAAoB;QAEpB,wEAAwE;QACxE,4CAA4C;QAC5C,MAAM,aAAa,GAAG,aAAO,CAAC,MAAM,EAAE,CAAC;QACvC,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,SAAS,UAAU,CAAW,GAAG,IAAe;YACrD,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACtB,IAAI,IAAI,EAAE;gBACR,IAAI,KAAK,YAAY,KAAK,EAAE;oBAC1B,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,SAAS,CAAC;wBACd,IAAI,EAAE,oBAAc,CAAC,KAAK;wBAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;qBACvB,CAAC,CAAC;iBACJ;qBAAM;oBACL,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAkB,CAAC;oBACxC,eAAe,CAAC,sBAAsB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;iBACtD;gBACD,IAAI,CAAC,GAAG,EAAE,CAAC;aACZ;YAED,OAAO,aAAO,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,EAAE;gBACtC,IAAI,WAAW,KAAK,aAAa,EAAE;oBACjC,eAAe,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;wBACxC,KAAK,EAAE,MAAM;wBACb,WAAW,EAAE,eAAe,CAAC,SAAS;qBACvC,CAAC,CAAC;iBACJ;gBACD,OAAO,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;IACJ,CAAC;IACO,WAAW,CAAC,OAAY;;QAC9B,MAAM,IAAI,GAAG,MAAA,OAAO,CAAC,WAAW,0CAAE,IAAI,CAAC;QACvC,MAAM,IAAI,GAAG,MAAA,OAAO,CAAC,WAAW,0CAAE,IAAI,CAAC;QACvC,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC;QAChC,MAAM,QAAQ,GAAG,aAAa,IAAI,IAAI,IAAI,IAAI,QAAQ,EAAE,CAAC;QACzD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAEO,yBAAyB,CAAC,WAA6B;QAC7D,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,iBAAiB,CAAC;QAC7D,MAAM,eAAe,GAAG,WAAW,KAAK,SAAS,CAAC;QAClD,OAAO,iBAAiB,KAAK,IAAI,IAAI,eAAe,CAAC;IACvD,CAAC;CACF;AAj+BD,wDAi+BC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  context,\n  trace,\n  Span,\n  SpanKind,\n  SpanStatusCode,\n} from '@opentelemetry/api';\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  InstrumentationNodeModuleFile,\n  isWrapped,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\nimport {\n  DBSYSTEMVALUES_MONGODB,\n  SEMATTRS_DB_CONNECTION_STRING,\n  SEMATTRS_DB_MONGODB_COLLECTION,\n  SEMATTRS_DB_NAME,\n  SEMATTRS_DB_OPERATION,\n  SEMATTRS_DB_STATEMENT,\n  SEMATTRS_DB_SYSTEM,\n  SEMATTRS_NET_PEER_NAME,\n  SEMATTRS_NET_PEER_PORT,\n} from '@opentelemetry/semantic-conventions';\nimport { MongoDBInstrumentationConfig, CommandResult } from './types';\nimport {\n  CursorState,\n  ServerSession,\n  MongodbCommandType,\n  MongoInternalCommand,\n  MongodbNamespace,\n  MongoInternalTopology,\n  WireProtocolInternal,\n  V4Connection,\n  V4ConnectionPool,\n} from './internal-types';\nimport { V4Connect, V4Session } from './internal-types';\n/** @knipignore */\nimport { PACKAGE_NAME, PACKAGE_VERSION } from './version';\nimport { UpDownCounter } from '@opentelemetry/api';\n\nconst DEFAULT_CONFIG: MongoDBInstrumentationConfig = {\n  requireParentSpan: true,\n};\n\n/** mongodb instrumentation plugin for OpenTelemetry */\nexport class MongoDBInstrumentation extends InstrumentationBase<MongoDBInstrumentationConfig> {\n  private _connectionsUsage!: UpDownCounter;\n  private _poolName!: string;\n\n  constructor(config: MongoDBInstrumentationConfig = {}) {\n    super(PACKAGE_NAME, PACKAGE_VERSION, { ...DEFAULT_CONFIG, ...config });\n  }\n\n  override setConfig(config: MongoDBInstrumentationConfig = {}) {\n    super.setConfig({ ...DEFAULT_CONFIG, ...config });\n  }\n\n  override _updateMetricInstruments() {\n    this._connectionsUsage = this.meter.createUpDownCounter(\n      'db.client.connections.usage',\n      {\n        description:\n          'The number of connections that are currently in state described by the state attribute.',\n        unit: '{connection}',\n      }\n    );\n  }\n\n  init() {\n    const {\n      v3PatchConnection: v3PatchConnection,\n      v3UnpatchConnection: v3UnpatchConnection,\n    } = this._getV3ConnectionPatches();\n\n    const { v4PatchConnect, v4UnpatchConnect } = this._getV4ConnectPatches();\n    const {\n      v4PatchConnectionCallback,\n      v4PatchConnectionPromise,\n      v4UnpatchConnection,\n    } = this._getV4ConnectionPatches();\n    const { v4PatchConnectionPool, v4UnpatchConnectionPool } =\n      this._getV4ConnectionPoolPatches();\n    const { v4PatchSessions, v4UnpatchSessions } = this._getV4SessionsPatches();\n\n    return [\n      new InstrumentationNodeModuleDefinition(\n        'mongodb',\n        ['>=3.3.0 <4'],\n        undefined,\n        undefined,\n        [\n          new InstrumentationNodeModuleFile(\n            'mongodb/lib/core/wireprotocol/index.js',\n            ['>=3.3.0 <4'],\n            v3PatchConnection,\n            v3UnpatchConnection\n          ),\n        ]\n      ),\n      new InstrumentationNodeModuleDefinition(\n        'mongodb',\n        ['>=4.0.0 <7'],\n        undefined,\n        undefined,\n        [\n          new InstrumentationNodeModuleFile(\n            'mongodb/lib/cmap/connection.js',\n            ['>=4.0.0 <6.4'],\n            v4PatchConnectionCallback,\n            v4UnpatchConnection\n          ),\n          new InstrumentationNodeModuleFile(\n            'mongodb/lib/cmap/connection.js',\n            ['>=6.4.0 <7'],\n            v4PatchConnectionPromise,\n            v4UnpatchConnection\n          ),\n          new InstrumentationNodeModuleFile(\n            'mongodb/lib/cmap/connection_pool.js',\n            ['>=4.0.0 <6.4'],\n            v4PatchConnectionPool,\n            v4UnpatchConnectionPool\n          ),\n          new InstrumentationNodeModuleFile(\n            'mongodb/lib/cmap/connect.js',\n            ['>=4.0.0 <7'],\n            v4PatchConnect,\n            v4UnpatchConnect\n          ),\n          new InstrumentationNodeModuleFile(\n            'mongodb/lib/sessions.js',\n            ['>=4.0.0 <7'],\n            v4PatchSessions,\n            v4UnpatchSessions\n          ),\n        ]\n      ),\n    ];\n  }\n\n  private _getV3ConnectionPatches<T extends WireProtocolInternal>() {\n    return {\n      v3PatchConnection: (moduleExports: T) => {\n        // patch insert operation\n        if (isWrapped(moduleExports.insert)) {\n          this._unwrap(moduleExports, 'insert');\n        }\n        this._wrap(\n          moduleExports,\n          'insert',\n          this._getV3PatchOperation('insert')\n        );\n        // patch remove operation\n        if (isWrapped(moduleExports.remove)) {\n          this._unwrap(moduleExports, 'remove');\n        }\n        this._wrap(\n          moduleExports,\n          'remove',\n          this._getV3PatchOperation('remove')\n        );\n        // patch update operation\n        if (isWrapped(moduleExports.update)) {\n          this._unwrap(moduleExports, 'update');\n        }\n        this._wrap(\n          moduleExports,\n          'update',\n          this._getV3PatchOperation('update')\n        );\n        // patch other command\n        if (isWrapped(moduleExports.command)) {\n          this._unwrap(moduleExports, 'command');\n        }\n        this._wrap(moduleExports, 'command', this._getV3PatchCommand());\n        // patch query\n        if (isWrapped(moduleExports.query)) {\n          this._unwrap(moduleExports, 'query');\n        }\n        this._wrap(moduleExports, 'query', this._getV3PatchFind());\n        // patch get more operation on cursor\n        if (isWrapped(moduleExports.getMore)) {\n          this._unwrap(moduleExports, 'getMore');\n        }\n        this._wrap(moduleExports, 'getMore', this._getV3PatchCursor());\n        return moduleExports;\n      },\n      v3UnpatchConnection: (moduleExports?: T) => {\n        if (moduleExports === undefined) return;\n        this._unwrap(moduleExports, 'insert');\n        this._unwrap(moduleExports, 'remove');\n        this._unwrap(moduleExports, 'update');\n        this._unwrap(moduleExports, 'command');\n        this._unwrap(moduleExports, 'query');\n        this._unwrap(moduleExports, 'getMore');\n      },\n    };\n  }\n\n  private _getV4SessionsPatches<T extends V4Session>() {\n    return {\n      v4PatchSessions: (moduleExports: any) => {\n        if (isWrapped(moduleExports.acquire)) {\n          this._unwrap(moduleExports, 'acquire');\n        }\n        this._wrap(\n          moduleExports.ServerSessionPool.prototype,\n          'acquire',\n          this._getV4AcquireCommand()\n        );\n\n        if (isWrapped(moduleExports.release)) {\n          this._unwrap(moduleExports, 'release');\n        }\n        this._wrap(\n          moduleExports.ServerSessionPool.prototype,\n          'release',\n          this._getV4ReleaseCommand()\n        );\n        return moduleExports;\n      },\n      v4UnpatchSessions: (moduleExports?: T) => {\n        if (moduleExports === undefined) return;\n        if (isWrapped(moduleExports.acquire)) {\n          this._unwrap(moduleExports, 'acquire');\n        }\n        if (isWrapped(moduleExports.release)) {\n          this._unwrap(moduleExports, 'release');\n        }\n      },\n    };\n  }\n\n  private _getV4AcquireCommand() {\n    const instrumentation = this;\n    return (original: V4Session['acquire']) => {\n      return function patchAcquire(this: any) {\n        const nSessionsBeforeAcquire = this.sessions.length;\n        const session = original.call(this);\n        const nSessionsAfterAcquire = this.sessions.length;\n\n        if (nSessionsBeforeAcquire === nSessionsAfterAcquire) {\n          //no session in the pool. a new session was created and used\n          instrumentation._connectionsUsage.add(1, {\n            state: 'used',\n            'pool.name': instrumentation._poolName,\n          });\n        } else if (nSessionsBeforeAcquire - 1 === nSessionsAfterAcquire) {\n          //a session was already in the pool. remove it from the pool and use it.\n          instrumentation._connectionsUsage.add(-1, {\n            state: 'idle',\n            'pool.name': instrumentation._poolName,\n          });\n          instrumentation._connectionsUsage.add(1, {\n            state: 'used',\n            'pool.name': instrumentation._poolName,\n          });\n        }\n        return session;\n      };\n    };\n  }\n\n  private _getV4ReleaseCommand() {\n    const instrumentation = this;\n    return (original: V4Session['release']) => {\n      return function patchRelease(this: any, session: ServerSession) {\n        const cmdPromise = original.call(this, session);\n\n        instrumentation._connectionsUsage.add(-1, {\n          state: 'used',\n          'pool.name': instrumentation._poolName,\n        });\n        instrumentation._connectionsUsage.add(1, {\n          state: 'idle',\n          'pool.name': instrumentation._poolName,\n        });\n        return cmdPromise;\n      };\n    };\n  }\n\n  private _getV4ConnectionPoolPatches<T extends V4ConnectionPool>() {\n    return {\n      v4PatchConnectionPool: (moduleExports: any) => {\n        const poolPrototype = moduleExports.ConnectionPool.prototype;\n\n        if (isWrapped(poolPrototype.checkOut)) {\n          this._unwrap(poolPrototype, 'checkOut');\n        }\n\n        this._wrap(\n          poolPrototype,\n          'checkOut',\n          this._getV4ConnectionPoolCheckOut()\n        );\n        return moduleExports;\n      },\n      v4UnpatchConnectionPool: (moduleExports?: any) => {\n        if (moduleExports === undefined) return;\n\n        this._unwrap(moduleExports.ConnectionPool.prototype, 'checkOut');\n      },\n    };\n  }\n\n  private _getV4ConnectPatches<T extends V4Connect>() {\n    return {\n      v4PatchConnect: (moduleExports: any) => {\n        if (isWrapped(moduleExports.connect)) {\n          this._unwrap(moduleExports, 'connect');\n        }\n\n        this._wrap(moduleExports, 'connect', this._getV4ConnectCommand());\n        return moduleExports;\n      },\n      v4UnpatchConnect: (moduleExports?: T) => {\n        if (moduleExports === undefined) return;\n\n        this._unwrap(moduleExports, 'connect');\n      },\n    };\n  }\n\n  // This patch will become unnecessary once\n  // https://jira.mongodb.org/browse/NODE-5639 is done.\n  private _getV4ConnectionPoolCheckOut() {\n    return (original: V4ConnectionPool['checkOut']) => {\n      return function patchedCheckout(this: unknown, callback: any) {\n        const patchedCallback = context.bind(context.active(), callback);\n        return original.call(this, patchedCallback);\n      };\n    };\n  }\n\n  private _getV4ConnectCommand() {\n    const instrumentation = this;\n\n    return (\n      original: V4Connect['connectCallback'] | V4Connect['connectPromise']\n    ) => {\n      return function patchedConnect(\n        this: unknown,\n        options: any,\n        callback: any\n      ) {\n        // from v6.4 `connect` method only accepts an options param and returns a promise\n        // with the connection\n        if (original.length === 1) {\n          const result = (original as V4Connect['connectPromise']).call(\n            this,\n            options\n          );\n          if (result && typeof result.then === 'function') {\n            result.then(\n              () => instrumentation.setPoolName(options),\n              // this handler is set to pass the lint rules\n              () => undefined\n            );\n          }\n          return result;\n        }\n\n        // Earlier versions expects a callback param and return void\n        const patchedCallback = function (err: any, conn: any) {\n          if (err || !conn) {\n            callback(err, conn);\n            return;\n          }\n          instrumentation.setPoolName(options);\n          callback(err, conn);\n        };\n\n        return (original as V4Connect['connectCallback']).call(\n          this,\n          options,\n          patchedCallback\n        );\n      };\n    };\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  private _getV4ConnectionPatches<T extends V4Connection>() {\n    return {\n      v4PatchConnectionCallback: (moduleExports: any) => {\n        // patch insert operation\n        if (isWrapped(moduleExports.Connection.prototype.command)) {\n          this._unwrap(moduleExports.Connection.prototype, 'command');\n        }\n\n        this._wrap(\n          moduleExports.Connection.prototype,\n          'command',\n          this._getV4PatchCommandCallback()\n        );\n        return moduleExports;\n      },\n      v4PatchConnectionPromise: (moduleExports: any) => {\n        // patch insert operation\n        if (isWrapped(moduleExports.Connection.prototype.command)) {\n          this._unwrap(moduleExports.Connection.prototype, 'command');\n        }\n\n        this._wrap(\n          moduleExports.Connection.prototype,\n          'command',\n          this._getV4PatchCommandPromise()\n        );\n        return moduleExports;\n      },\n      v4UnpatchConnection: (moduleExports?: any) => {\n        if (moduleExports === undefined) return;\n        this._unwrap(moduleExports.Connection.prototype, 'command');\n      },\n    };\n  }\n\n  /** Creates spans for common operations */\n  private _getV3PatchOperation(operationName: 'insert' | 'update' | 'remove') {\n    const instrumentation = this;\n    return (original: WireProtocolInternal[typeof operationName]) => {\n      return function patchedServerCommand(\n        this: unknown,\n        server: MongoInternalTopology,\n        ns: string,\n        ops: unknown[],\n        options: unknown | Function,\n        callback?: Function\n      ) {\n        const currentSpan = trace.getSpan(context.active());\n        const skipInstrumentation =\n          instrumentation._checkSkipInstrumentation(currentSpan);\n\n        const resultHandler =\n          typeof options === 'function' ? options : callback;\n        if (\n          skipInstrumentation ||\n          typeof resultHandler !== 'function' ||\n          typeof ops !== 'object'\n        ) {\n          if (typeof options === 'function') {\n            return original.call(this, server, ns, ops, options);\n          } else {\n            return original.call(this, server, ns, ops, options, callback);\n          }\n        }\n\n        const span = instrumentation.tracer.startSpan(\n          `mongodb.${operationName}`,\n          {\n            kind: SpanKind.CLIENT,\n          }\n        );\n\n        instrumentation._populateV3Attributes(\n          span,\n          ns,\n          server,\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          ops[0] as any,\n          operationName\n        );\n        const patchedCallback = instrumentation._patchEnd(span, resultHandler);\n        // handle when options is the callback to send the correct number of args\n        if (typeof options === 'function') {\n          return original.call(this, server, ns, ops, patchedCallback);\n        } else {\n          return original.call(this, server, ns, ops, options, patchedCallback);\n        }\n      };\n    };\n  }\n\n  /** Creates spans for command operation */\n  private _getV3PatchCommand() {\n    const instrumentation = this;\n    return (original: WireProtocolInternal['command']) => {\n      return function patchedServerCommand(\n        this: unknown,\n        server: MongoInternalTopology,\n        ns: string,\n        cmd: MongoInternalCommand,\n        options: unknown | Function,\n        callback?: Function\n      ) {\n        const currentSpan = trace.getSpan(context.active());\n        const skipInstrumentation =\n          instrumentation._checkSkipInstrumentation(currentSpan);\n\n        const resultHandler =\n          typeof options === 'function' ? options : callback;\n\n        if (\n          skipInstrumentation ||\n          typeof resultHandler !== 'function' ||\n          typeof cmd !== 'object'\n        ) {\n          if (typeof options === 'function') {\n            return original.call(this, server, ns, cmd, options);\n          } else {\n            return original.call(this, server, ns, cmd, options, callback);\n          }\n        }\n\n        const commandType = MongoDBInstrumentation._getCommandType(cmd);\n        const type =\n          commandType === MongodbCommandType.UNKNOWN ? 'command' : commandType;\n        const span = instrumentation.tracer.startSpan(`mongodb.${type}`, {\n          kind: SpanKind.CLIENT,\n        });\n        const operation =\n          commandType === MongodbCommandType.UNKNOWN ? undefined : commandType;\n        instrumentation._populateV3Attributes(span, ns, server, cmd, operation);\n        const patchedCallback = instrumentation._patchEnd(span, resultHandler);\n        // handle when options is the callback to send the correct number of args\n        if (typeof options === 'function') {\n          return original.call(this, server, ns, cmd, patchedCallback);\n        } else {\n          return original.call(this, server, ns, cmd, options, patchedCallback);\n        }\n      };\n    };\n  }\n\n  /** Creates spans for command operation */\n  private _getV4PatchCommandCallback() {\n    const instrumentation = this;\n    return (original: V4Connection['commandCallback']) => {\n      return function patchedV4ServerCommand(\n        this: any,\n        ns: MongodbNamespace,\n        cmd: any,\n        options: undefined | unknown,\n        callback: any\n      ) {\n        const currentSpan = trace.getSpan(context.active());\n        const skipInstrumentation =\n          instrumentation._checkSkipInstrumentation(currentSpan);\n        const resultHandler = callback;\n        const commandType = Object.keys(cmd)[0];\n\n        if (typeof cmd !== 'object' || cmd.ismaster || cmd.hello) {\n          return original.call(this, ns, cmd, options, callback);\n        }\n\n        let span = undefined;\n        if (!skipInstrumentation) {\n          span = instrumentation.tracer.startSpan(`mongodb.${commandType}`, {\n            kind: SpanKind.CLIENT,\n          });\n          instrumentation._populateV4Attributes(\n            span,\n            this,\n            ns,\n            cmd,\n            commandType\n          );\n        }\n        const patchedCallback = instrumentation._patchEnd(\n          span,\n          resultHandler,\n          this.id,\n          commandType\n        );\n\n        return original.call(this, ns, cmd, options, patchedCallback);\n      };\n    };\n  }\n\n  private _getV4PatchCommandPromise() {\n    const instrumentation = this;\n    return (original: V4Connection['commandPromise']) => {\n      return function patchedV4ServerCommand(\n        this: any,\n        ...args: Parameters<V4Connection['commandPromise']>\n      ) {\n        const [ns, cmd] = args;\n        const currentSpan = trace.getSpan(context.active());\n        const skipInstrumentation =\n          instrumentation._checkSkipInstrumentation(currentSpan);\n\n        const commandType = Object.keys(cmd)[0];\n        const resultHandler = () => undefined;\n\n        if (typeof cmd !== 'object' || cmd.ismaster || cmd.hello) {\n          return original.apply(this, args);\n        }\n\n        let span = undefined;\n        if (!skipInstrumentation) {\n          span = instrumentation.tracer.startSpan(`mongodb.${commandType}`, {\n            kind: SpanKind.CLIENT,\n          });\n          instrumentation._populateV4Attributes(\n            span,\n            this,\n            ns,\n            cmd,\n            commandType\n          );\n        }\n\n        const patchedCallback = instrumentation._patchEnd(\n          span,\n          resultHandler,\n          this.id,\n          commandType\n        );\n\n        const result = original.apply(this, args);\n        result.then(\n          (res: any) => patchedCallback(null, res),\n          (err: any) => patchedCallback(err)\n        );\n\n        return result;\n      };\n    };\n  }\n\n  /** Creates spans for find operation */\n  private _getV3PatchFind() {\n    const instrumentation = this;\n    return (original: WireProtocolInternal['query']) => {\n      return function patchedServerCommand(\n        this: unknown,\n        server: MongoInternalTopology,\n        ns: string,\n        cmd: MongoInternalCommand,\n        cursorState: CursorState,\n        options: unknown | Function,\n        callback?: Function\n      ) {\n        const currentSpan = trace.getSpan(context.active());\n        const skipInstrumentation =\n          instrumentation._checkSkipInstrumentation(currentSpan);\n        const resultHandler =\n          typeof options === 'function' ? options : callback;\n\n        if (\n          skipInstrumentation ||\n          typeof resultHandler !== 'function' ||\n          typeof cmd !== 'object'\n        ) {\n          if (typeof options === 'function') {\n            return original.call(this, server, ns, cmd, cursorState, options);\n          } else {\n            return original.call(\n              this,\n              server,\n              ns,\n              cmd,\n              cursorState,\n              options,\n              callback\n            );\n          }\n        }\n\n        const span = instrumentation.tracer.startSpan('mongodb.find', {\n          kind: SpanKind.CLIENT,\n        });\n        instrumentation._populateV3Attributes(span, ns, server, cmd, 'find');\n        const patchedCallback = instrumentation._patchEnd(span, resultHandler);\n        // handle when options is the callback to send the correct number of args\n        if (typeof options === 'function') {\n          return original.call(\n            this,\n            server,\n            ns,\n            cmd,\n            cursorState,\n            patchedCallback\n          );\n        } else {\n          return original.call(\n            this,\n            server,\n            ns,\n            cmd,\n            cursorState,\n            options,\n            patchedCallback\n          );\n        }\n      };\n    };\n  }\n\n  /** Creates spans for find operation */\n  private _getV3PatchCursor() {\n    const instrumentation = this;\n    return (original: WireProtocolInternal['getMore']) => {\n      return function patchedServerCommand(\n        this: unknown,\n        server: MongoInternalTopology,\n        ns: string,\n        cursorState: CursorState,\n        batchSize: number,\n        options: unknown | Function,\n        callback?: Function\n      ) {\n        const currentSpan = trace.getSpan(context.active());\n        const skipInstrumentation =\n          instrumentation._checkSkipInstrumentation(currentSpan);\n\n        const resultHandler =\n          typeof options === 'function' ? options : callback;\n\n        if (skipInstrumentation || typeof resultHandler !== 'function') {\n          if (typeof options === 'function') {\n            return original.call(\n              this,\n              server,\n              ns,\n              cursorState,\n              batchSize,\n              options\n            );\n          } else {\n            return original.call(\n              this,\n              server,\n              ns,\n              cursorState,\n              batchSize,\n              options,\n              callback\n            );\n          }\n        }\n\n        const span = instrumentation.tracer.startSpan('mongodb.getMore', {\n          kind: SpanKind.CLIENT,\n        });\n        instrumentation._populateV3Attributes(\n          span,\n          ns,\n          server,\n          cursorState.cmd,\n          'getMore'\n        );\n        const patchedCallback = instrumentation._patchEnd(span, resultHandler);\n        // handle when options is the callback to send the correct number of args\n        if (typeof options === 'function') {\n          return original.call(\n            this,\n            server,\n            ns,\n            cursorState,\n            batchSize,\n            patchedCallback\n          );\n        } else {\n          return original.call(\n            this,\n            server,\n            ns,\n            cursorState,\n            batchSize,\n            options,\n            patchedCallback\n          );\n        }\n      };\n    };\n  }\n\n  /**\n   * Get the mongodb command type from the object.\n   * @param command Internal mongodb command object\n   */\n  private static _getCommandType(\n    command: MongoInternalCommand\n  ): MongodbCommandType {\n    if (command.createIndexes !== undefined) {\n      return MongodbCommandType.CREATE_INDEXES;\n    } else if (command.findandmodify !== undefined) {\n      return MongodbCommandType.FIND_AND_MODIFY;\n    } else if (command.ismaster !== undefined) {\n      return MongodbCommandType.IS_MASTER;\n    } else if (command.count !== undefined) {\n      return MongodbCommandType.COUNT;\n    } else if (command.aggregate !== undefined) {\n      return MongodbCommandType.AGGREGATE;\n    } else {\n      return MongodbCommandType.UNKNOWN;\n    }\n  }\n\n  /**\n   * Populate span's attributes by fetching related metadata from the context\n   * @param span span to add attributes to\n   * @param connectionCtx mongodb internal connection context\n   * @param ns mongodb namespace\n   * @param command mongodb internal representation of a command\n   */\n  private _populateV4Attributes(\n    span: Span,\n    connectionCtx: any,\n    ns: MongodbNamespace,\n    command?: any,\n    operation?: string\n  ) {\n    let host, port: undefined | string;\n    if (connectionCtx) {\n      const hostParts =\n        typeof connectionCtx.address === 'string'\n          ? connectionCtx.address.split(':')\n          : '';\n      if (hostParts.length === 2) {\n        host = hostParts[0];\n        port = hostParts[1];\n      }\n    }\n    // capture parameters within the query as well if enhancedDatabaseReporting is enabled.\n    let commandObj: Record<string, unknown>;\n    if (command?.documents && command.documents[0]) {\n      commandObj = command.documents[0];\n    } else if (command?.cursors) {\n      commandObj = command.cursors;\n    } else {\n      commandObj = command;\n    }\n\n    this._addAllSpanAttributes(\n      span,\n      ns.db,\n      ns.collection,\n      host,\n      port,\n      commandObj,\n      operation\n    );\n  }\n\n  /**\n   * Populate span's attributes by fetching related metadata from the context\n   * @param span span to add attributes to\n   * @param ns mongodb namespace\n   * @param topology mongodb internal representation of the network topology\n   * @param command mongodb internal representation of a command\n   */\n  private _populateV3Attributes(\n    span: Span,\n    ns: string,\n    topology: MongoInternalTopology,\n    command?: MongoInternalCommand,\n    operation?: string | undefined\n  ) {\n    // add network attributes to determine the remote server\n    let host: undefined | string;\n    let port: undefined | string;\n    if (topology && topology.s) {\n      host = topology.s.options?.host ?? topology.s.host;\n      port = (topology.s.options?.port ?? topology.s.port)?.toString();\n      if (host == null || port == null) {\n        const address = topology.description?.address;\n        if (address) {\n          const addressSegments = address.split(':');\n          host = addressSegments[0];\n          port = addressSegments[1];\n        }\n      }\n    }\n\n    // The namespace is a combination of the database name and the name of the\n    // collection or index, like so: [database-name].[collection-or-index-name].\n    // It could be a string or an instance of MongoDBNamespace, as such we\n    // always coerce to a string to extract db and collection.\n    const [dbName, dbCollection] = ns.toString().split('.');\n    // capture parameters within the query as well if enhancedDatabaseReporting is enabled.\n    const commandObj = command?.query ?? command?.q ?? command;\n\n    this._addAllSpanAttributes(\n      span,\n      dbName,\n      dbCollection,\n      host,\n      port,\n      commandObj,\n      operation\n    );\n  }\n\n  private _addAllSpanAttributes(\n    span: Span,\n    dbName?: string,\n    dbCollection?: string,\n    host?: undefined | string,\n    port?: undefined | string,\n    commandObj?: any,\n    operation?: string | undefined\n  ) {\n    // add database related attributes\n    span.setAttributes({\n      [SEMATTRS_DB_SYSTEM]: DBSYSTEMVALUES_MONGODB,\n      [SEMATTRS_DB_NAME]: dbName,\n      [SEMATTRS_DB_MONGODB_COLLECTION]: dbCollection,\n      [SEMATTRS_DB_OPERATION]: operation,\n      [SEMATTRS_DB_CONNECTION_STRING]: `mongodb://${host}:${port}/${dbName}`,\n    });\n\n    if (host && port) {\n      span.setAttribute(SEMATTRS_NET_PEER_NAME, host);\n      const portNumber = parseInt(port, 10);\n      if (!isNaN(portNumber)) {\n        span.setAttribute(SEMATTRS_NET_PEER_PORT, portNumber);\n      }\n    }\n    if (!commandObj) return;\n\n    const { dbStatementSerializer: configDbStatementSerializer } =\n      this.getConfig();\n    const dbStatementSerializer =\n      typeof configDbStatementSerializer === 'function'\n        ? configDbStatementSerializer\n        : this._defaultDbStatementSerializer.bind(this);\n\n    safeExecuteInTheMiddle(\n      () => {\n        const query = dbStatementSerializer(commandObj);\n        span.setAttribute(SEMATTRS_DB_STATEMENT, query);\n      },\n      err => {\n        if (err) {\n          this._diag.error('Error running dbStatementSerializer hook', err);\n        }\n      },\n      true\n    );\n  }\n\n  private _defaultDbStatementSerializer(commandObj: Record<string, unknown>) {\n    const { enhancedDatabaseReporting } = this.getConfig();\n    const resultObj = enhancedDatabaseReporting\n      ? commandObj\n      : this._scrubStatement(commandObj);\n    return JSON.stringify(resultObj);\n  }\n\n  private _scrubStatement(value: unknown): unknown {\n    if (Array.isArray(value)) {\n      return value.map(element => this._scrubStatement(element));\n    }\n\n    if (typeof value === 'object' && value !== null) {\n      return Object.fromEntries(\n        Object.entries(value).map(([key, element]) => [\n          key,\n          this._scrubStatement(element),\n        ])\n      );\n    }\n\n    // A value like string or number, possible contains PII, scrub it\n    return '?';\n  }\n\n  /**\n   * Triggers the response hook in case it is defined.\n   * @param span The span to add the results to.\n   * @param result The command result\n   */\n  private _handleExecutionResult(span: Span, result: CommandResult) {\n    const { responseHook } = this.getConfig();\n    if (typeof responseHook === 'function') {\n      safeExecuteInTheMiddle(\n        () => {\n          responseHook(span, { data: result });\n        },\n        err => {\n          if (err) {\n            this._diag.error('Error running response hook', err);\n          }\n        },\n        true\n      );\n    }\n  }\n\n  /**\n   * Ends a created span.\n   * @param span The created span to end.\n   * @param resultHandler A callback function.\n   * @param connectionId: The connection ID of the Command response.\n   */\n  private _patchEnd(\n    span: Span | undefined,\n    resultHandler: Function,\n    connectionId?: number,\n    commandType?: string\n  ): Function {\n    // mongodb is using \"tick\" when calling a callback, this way the context\n    // in final callback (resultHandler) is lost\n    const activeContext = context.active();\n    const instrumentation = this;\n    return function patchedEnd(this: {}, ...args: unknown[]) {\n      const error = args[0];\n      if (span) {\n        if (error instanceof Error) {\n          span?.setStatus({\n            code: SpanStatusCode.ERROR,\n            message: error.message,\n          });\n        } else {\n          const result = args[1] as CommandResult;\n          instrumentation._handleExecutionResult(span, result);\n        }\n        span.end();\n      }\n\n      return context.with(activeContext, () => {\n        if (commandType === 'endSessions') {\n          instrumentation._connectionsUsage.add(-1, {\n            state: 'idle',\n            'pool.name': instrumentation._poolName,\n          });\n        }\n        return resultHandler.apply(this, args);\n      });\n    };\n  }\n  private setPoolName(options: any) {\n    const host = options.hostAddress?.host;\n    const port = options.hostAddress?.port;\n    const database = options.dbName;\n    const poolName = `mongodb://${host}:${port}/${database}`;\n    this._poolName = poolName;\n  }\n\n  private _checkSkipInstrumentation(currentSpan: Span | undefined) {\n    const requireParentSpan = this.getConfig().requireParentSpan;\n    const hasNoParentSpan = currentSpan === undefined;\n    return requireParentSpan === true && hasNoParentSpan;\n  }\n}\n"]}