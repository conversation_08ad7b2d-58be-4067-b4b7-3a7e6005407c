"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Urls = void 0;
const general_js_1 = require("./general.js");
const customer_portal_subscription_url_js_1 = require("./customer-portal-subscription-url.js");
class Urls {
    constructor(urlsResponse) {
        var _a, _b;
        this.general = new general_js_1.General(urlsResponse.general);
        this.subscriptions =
            (_b = (_a = urlsResponse.subscriptions) === null || _a === void 0 ? void 0 : _a.map((subscription) => new customer_portal_subscription_url_js_1.CustomerPortalSubscriptionUrl(subscription))) !== null && _b !== void 0 ? _b : [];
    }
}
exports.Urls = Urls;
