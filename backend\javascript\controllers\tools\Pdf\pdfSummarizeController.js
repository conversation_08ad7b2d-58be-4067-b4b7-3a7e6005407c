// node_gemini_summarizer/controllers/pdf/pdfSummarizeController.js
import axios from 'axios';
import FormData from 'form-data';

// ... (URL definition is the same)
const PYTHON_INTERNAL_PDF_SUMMARIZE_URL =
    process.env.PYTHON_INTERNAL_PDF_SUMMARIZE_URL ||
    'http://localhost:5001/api/internal/pdf-summarization/summarize-uploaded-pdf';

export const summarizeUploadedPdf = async (req, res, next) => {
        if (!req.file) {
        return res.status(400).json({ error: 'No PDF file uploaded.' });
    }
    const originalFilename = req.file.originalname || 'uploaded.pdf';
    const { language } = req.body;
    console.log(`[NodeCtrl] Received PDF "${originalFilename}". Options: Language=${language}`);
    const formDataToPython = new FormData();
    formDataToPython.append('pdfFile', req.file.buffer, {
        filename: originalFilename,
        contentType: req.file.mimetype || 'application/pdf',
    });
    if (language !== undefined) {
        formDataToPython.append('language', language);
    }
    console.log(`[NodeCtrl] Forwarding PDF and options to Python service...`);

    try {
        const pythonResponse = await axios.post(PYTHON_INTERNAL_PDF_SUMMARIZE_URL, formDataToPython, {
            headers: { ...formDataToPython.getHeaders() },
            timeout: 180000, 
        });
        res.status(pythonResponse.status).json(pythonResponse.data);
    } catch (error) {
        console.error(`[NodeCtrl] Error calling Python service for "${originalFilename}":`, error.response?.data || error.message);

        let userMessage = 'An unexpected error occurred. Please try again.';
        let statusCode = 500;

        if (error.response) {
            // The Python service responded with an error status code.
            statusCode = error.response.status;
            const details = error.response.data?.details || error.response.data?.error;

            // --- MODIFICATION: Handle 503 and 504 specifically ---
            if (statusCode === 504 || statusCode === 503) {
                 // 504 Gateway Timeout or 503 Service Unavailable from our Python service.
                 // This implies a network issue between Python and Google.
                userMessage = 'The Summaray service is temporarily unable to connect to the AI provider. This may be due to a network issue or high demand. Please try again in a few moments.';
            } else if (statusCode >= 500) {
                // Other 5xx errors are true internal server issues on the Python side.
                userMessage = 'Internal Server Error: Our Summaray service experienced an unexpected issue. We have been notified. Please try again later.';
            } else if (statusCode >= 400) {
                // 4xx errors are client-side issues (e.g., bad file).
                userMessage = `Invalid Request: There was a problem with the submitted file or options. Details: ${details || 'No details provided.'}`;
            }
        } else if (error.request) {
            // No response was received from our Python service. THIS is the network error.
            statusCode = 503; // Service Unavailable
            if (error.code === 'ECONNABORTED') {
                userMessage = 'Request Timeout: The Summaray took too long to complete. This can happen with very large files or high server load. Please try again.';
            } else {
                // This will trigger if your Python server is completely down.
                userMessage = 'Network Error: Unable to connect to our Summaray service. Please check your internet connection and try again. If the problem persists, our service may be temporarily down.';
            }
        } else {
            // Something else happened in setting up the request.
            userMessage = 'An unexpected error occurred while preparing your request. Please try again.';
        }

        res.status(statusCode).json({ error: userMessage });
    }
};