// node_gemini_summarizer/controllers/auth/index.js
import { registerUser } from './auth.register.js';
import { loginUser } from './auth.login.js';
import { verifyUserEmail } from './auth.verify.js';
import { resendUserVerificationCode } from './auth.resendCode.js';
import { getCurrentUser } from './auth.currentUser.js';

export {
    registerUser as register,
    loginUser as login,
    verifyUserEmail as verifyEmail,
    resendUserVerificationCode as resendVerificationCode,
    getCurrentUser
};