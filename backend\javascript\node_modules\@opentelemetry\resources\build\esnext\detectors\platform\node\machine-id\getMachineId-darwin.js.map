{"version": 3, "file": "getMachineId-darwin.js", "sourceRoot": "", "sources": ["../../../../../../src/detectors/platform/node/machine-id/getMachineId-darwin.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAE1C,MAAM,CAAC,KAAK,UAAU,YAAY;IAChC,IAAI;QACF,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,wCAAwC,CAAC,CAAC;QAEzE,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM;aACzB,KAAK,CAAC,IAAI,CAAC;aACX,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAEjD,IAAI,CAAC,MAAM,EAAE;YACX,OAAO,EAAE,CAAC;SACX;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACpC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SAC9B;KACF;IAAC,OAAO,CAAC,EAAE;QACV,IAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC,EAAE,CAAC,CAAC;KAC9C;IAED,OAAO,EAAE,CAAC;AACZ,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { execAsync } from './execAsync';\nimport { diag } from '@opentelemetry/api';\n\nexport async function getMachineId(): Promise<string> {\n  try {\n    const result = await execAsync('ioreg -rd1 -c \"IOPlatformExpertDevice\"');\n\n    const idLine = result.stdout\n      .split('\\n')\n      .find(line => line.includes('IOPlatformUUID'));\n\n    if (!idLine) {\n      return '';\n    }\n\n    const parts = idLine.split('\" = \"');\n    if (parts.length === 2) {\n      return parts[1].slice(0, -1);\n    }\n  } catch (e) {\n    diag.debug(`error reading machine id: ${e}`);\n  }\n\n  return '';\n}\n"]}