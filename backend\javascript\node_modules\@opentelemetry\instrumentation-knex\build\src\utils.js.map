{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,8EAG6C;AAMtC,MAAM,YAAY,GAAG,CAAC,MAAW,EAAE,EAAE;IAC1C,IAAI,MAAM,EAAE;QACV,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,IAAI,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE;gBAC9B,OAAO,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;aACvD;iBAAM,IAAI,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE;gBAClC,OAAO,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;aACrE;SACF;QACD,IAAI,MAAM,CAAC,OAAO,EAAE;YAClB,OAAO,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;SACrD;KACF;IACD,OAAO,GAAG,EAAE,CAAC,kBAAkB,CAAC;AAClC,CAAC,CAAC;AAdW,QAAA,YAAY,gBAcvB;AAEF,SAAgB,0BAA0B,CACxC,GAAc,EACd,OAAe;IAEf,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,YAAY,KAAK,CAAC,EAAE;QAClC,OAAO,GAAG,CAAC;KACZ;IAED,OAAO;QACL,OAAO;QACP,IAAI,EAAE,GAAG,CAAC,IAAI;QACd,KAAK,EAAE,GAAG,CAAC,KAAK;QAChB,IAAI,EAAE,GAAG,CAAC,IAAI;KACf,CAAC;AACJ,CAAC;AAdD,gEAcC;AAED,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC;IACxB,CAAC,SAAS,EAAE,4CAAqB,CAAC;IAClC,CAAC,IAAI,EAAE,gDAAyB,CAAC;CAClC,CAAC,CAAC;AAEI,MAAM,SAAS,GAAG,CAAC,UAAkB,EAAE,EAAE;IAC9C,OAAO,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC;AACjD,CAAC,CAAC;AAFW,QAAA,SAAS,aAEpB;AAEK,MAAM,OAAO,GAAG,CAAC,EAAU,EAAE,SAAkB,EAAE,KAAc,EAAE,EAAE;IACxE,IAAI,SAAS,EAAE;QACb,IAAI,KAAK,EAAE;YACT,OAAO,GAAG,SAAS,IAAI,EAAE,IAAI,KAAK,EAAE,CAAC;SACtC;QACD,OAAO,GAAG,SAAS,IAAI,EAAE,EAAE,CAAC;KAC7B;IACD,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AARW,QAAA,OAAO,WAQlB;AAEK,MAAM,WAAW,GAAG,CAAC,GAAW,EAAE,SAAiB,EAAE,EAAE;IAC5D,IACE,OAAO,GAAG,KAAK,QAAQ;QACvB,OAAO,SAAS,KAAK,QAAQ;QAC7B,CAAC,GAAG,SAAS;QACb,SAAS,GAAG,GAAG,CAAC,MAAM,EACtB;QACA,OAAO,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;KAC3C;IACD,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AAVW,QAAA,WAAW,eAUtB;AAEK,MAAM,gBAAgB,GAAG,CAAC,OAAY,EAAU,EAAE;;IACvD,MAAM,KAAK,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,0CAAE,KAAK,CAAC;IACtC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO,IAAA,wBAAgB,EAAC,KAAK,CAAC,CAAC;KAChC;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AANW,QAAA,gBAAgB,oBAM3B", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Exception } from '@opentelemetry/api';\nimport {\n  DBSYSTEMVALUES_SQLITE,\n  DBSYSTEMVALUES_POSTGRESQL,\n} from '@opentelemetry/semantic-conventions';\n\ntype KnexError = Error & {\n  code?: string;\n};\n\nexport const getFormatter = (runner: any) => {\n  if (runner) {\n    if (runner.client) {\n      if (runner.client._formatQuery) {\n        return runner.client._formatQuery.bind(runner.client);\n      } else if (runner.client.SqlString) {\n        return runner.client.SqlString.format.bind(runner.client.SqlString);\n      }\n    }\n    if (runner.builder) {\n      return runner.builder.toString.bind(runner.builder);\n    }\n  }\n  return () => '<noop formatter>';\n};\n\nexport function otelExceptionFromKnexError(\n  err: KnexError,\n  message: string\n): Exception {\n  if (!(err && err instanceof Error)) {\n    return err;\n  }\n\n  return {\n    message,\n    code: err.code,\n    stack: err.stack,\n    name: err.name,\n  };\n}\n\nconst systemMap = new Map([\n  ['sqlite3', DBSYSTEMVALUES_SQLITE],\n  ['pg', DBSYSTEMVALUES_POSTGRESQL],\n]);\n\nexport const mapSystem = (knexSystem: string) => {\n  return systemMap.get(knexSystem) || knexSystem;\n};\n\nexport const getName = (db: string, operation?: string, table?: string) => {\n  if (operation) {\n    if (table) {\n      return `${operation} ${db}.${table}`;\n    }\n    return `${operation} ${db}`;\n  }\n  return db;\n};\n\nexport const limitLength = (str: string, maxLength: number) => {\n  if (\n    typeof str === 'string' &&\n    typeof maxLength === 'number' &&\n    0 < maxLength &&\n    maxLength < str.length\n  ) {\n    return str.substring(0, maxLength) + '..';\n  }\n  return str;\n};\n\nexport const extractTableName = (builder: any): string => {\n  const table = builder?._single?.table;\n  if (typeof table === 'object') {\n    return extractTableName(table);\n  }\n  return table;\n};\n"]}