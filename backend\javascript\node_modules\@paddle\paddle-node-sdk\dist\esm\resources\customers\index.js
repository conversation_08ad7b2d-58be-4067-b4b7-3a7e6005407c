var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { BaseResource, PathParameters, QueryParameters } from '../../internal/base/index.js';
import { CreditBalance, Customer, CustomerCollection, AuthToken } from '../../entities/index.js';
const CustomerPaths = {
    list: '/customers',
    create: '/customers',
    get: '/customers/{customer_id}',
    update: '/customers/{customer_id}',
    getCustomerBalance: '/customers/{customer_id}/credit-balances',
    generate: '/customers/{customer_id}/auth-token',
};
export * from './operations/index.js';
export class CustomersResource extends BaseResource {
    list(queryParams) {
        const queryParameters = new QueryParameters(queryParams);
        return new CustomerCollection(this.client, CustomerPaths.list + queryParameters.toQueryString());
    }
    create(createCustomerParameters) {
        return __awaiter(this, void 0, void 0, function* () {
            const response = yield this.client.post(CustomerPaths.create, createCustomerParameters);
            const data = this.handleResponse(response);
            return new Customer(data);
        });
    }
    get(customerId) {
        return __awaiter(this, void 0, void 0, function* () {
            const urlWithPathParams = new PathParameters(CustomerPaths.get, {
                customer_id: customerId,
            }).deriveUrl();
            const response = yield this.client.get(urlWithPathParams);
            const data = this.handleResponse(response);
            return new Customer(data);
        });
    }
    update(customerId, updateCustomer) {
        return __awaiter(this, void 0, void 0, function* () {
            const urlWithPathParams = new PathParameters(CustomerPaths.update, {
                customer_id: customerId,
            }).deriveUrl();
            const response = yield this.client.patch(urlWithPathParams, updateCustomer);
            const data = this.handleResponse(response);
            return new Customer(data);
        });
    }
    getCreditBalance(customerId, queryParams) {
        return __awaiter(this, void 0, void 0, function* () {
            const urlWithPathParams = new PathParameters(CustomerPaths.getCustomerBalance, {
                customer_id: customerId,
            }).deriveUrl();
            const queryParameters = new QueryParameters(queryParams);
            const response = yield this.client.get(urlWithPathParams, queryParameters);
            const data = this.handleResponse(response);
            return data.map((balance) => new CreditBalance(balance));
        });
    }
    archive(customerId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.update(customerId, { status: 'archived' });
        });
    }
    generateAuthToken(customerId) {
        return __awaiter(this, void 0, void 0, function* () {
            const urlWithPathParams = new PathParameters(CustomerPaths.generate, {
                customer_id: customerId,
            }).deriveUrl();
            const response = yield this.client.post(urlWithPathParams, undefined);
            const data = this.handleResponse(response);
            return new AuthToken(data);
        });
    }
}
