{"name": "@opentelemetry/instrumentation-kafkajs", "version": "0.7.1", "description": "OpenTelemetry instrumentation for `kafkajs` messaging client for Apache Kafka", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"test": "mocha --require @opentelemetry/contrib-test-utils 'test/**/*.test.ts'", "tdd": "npm run test -- --watch-extensions ts --watch", "clean": "rimraf build/*", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "lint:readme": "node ../../../scripts/lint-readme", "prewatch": "npm run precompile", "prepublishOnly": "npm run compile", "version:update": "node ../../../scripts/version-update.js", "compile": "tsc -p ."}, "keywords": ["kafka<PERSON>s", "instrumentation", "nodejs", "opentelemetry", "profiling", "tracing"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "devDependencies": {"@opentelemetry/api": "^1.3.0", "@opentelemetry/contrib-test-utils": "^0.45.1", "@opentelemetry/sdk-trace-base": "^1.24.0", "@types/mocha": "7.0.2", "@types/node": "18.18.14", "@types/sinon": "^10.0.11", "kafkajs": "^2.2.4", "nyc": "15.1.0", "rimraf": "5.0.10", "sinon": "15.2.0", "typescript": "4.4.4"}, "dependencies": {"@opentelemetry/instrumentation": "^0.57.1", "@opentelemetry/semantic-conventions": "^1.27.0"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/instrumentation-kafkajs#readme", "gitHead": "1eb77007669bae87fe5664d68ba6533b95275d52"}