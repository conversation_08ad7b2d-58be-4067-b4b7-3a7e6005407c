{"version": 3, "file": "Meter.js", "sourceRoot": "", "sources": ["../../../src/metrics/Meter.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  BatchObservableCallback,\n  Counter,\n  Gauge,\n  Histogram,\n  MetricAttributes,\n  MetricOptions,\n  Observable,\n  ObservableCounter,\n  ObservableGauge,\n  ObservableUpDownCounter,\n  UpDownCounter,\n} from './Metric';\n\n/**\n * An interface describes additional metadata of a meter.\n */\nexport interface MeterOptions {\n  /**\n   * The schemaUrl of the meter or instrumentation library\n   */\n  schemaUrl?: string;\n}\n\n/**\n * An interface to allow the recording metrics.\n *\n * {@link Metric}s are used for recording pre-defined aggregation (`Counter`),\n * or raw values (`Histogram`) in which the aggregation and attributes\n * for the exported metric are deferred.\n */\nexport interface Meter {\n  /**\n   * Creates and returns a new `Gauge`.\n   * @param name the name of the metric.\n   * @param [options] the metric options.\n   */\n  createGauge<AttributesTypes extends MetricAttributes = MetricAttributes>(\n    name: string,\n    options?: MetricOptions\n  ): Gauge<AttributesTypes>;\n\n  /**\n   * Creates and returns a new `Histogram`.\n   * @param name the name of the metric.\n   * @param [options] the metric options.\n   */\n  createHistogram<AttributesTypes extends MetricAttributes = MetricAttributes>(\n    name: string,\n    options?: MetricOptions\n  ): Histogram<AttributesTypes>;\n\n  /**\n   * Creates a new `Counter` metric. Generally, this kind of metric when the\n   * value is a quantity, the sum is of primary interest, and the event count\n   * and value distribution are not of primary interest.\n   * @param name the name of the metric.\n   * @param [options] the metric options.\n   */\n  createCounter<AttributesTypes extends MetricAttributes = MetricAttributes>(\n    name: string,\n    options?: MetricOptions\n  ): Counter<AttributesTypes>;\n\n  /**\n   * Creates a new `UpDownCounter` metric. UpDownCounter is a synchronous\n   * instrument and very similar to Counter except that Add(increment)\n   * supports negative increments. It is generally useful for capturing changes\n   * in an amount of resources used, or any quantity that rises and falls\n   * during a request.\n   * Example uses for UpDownCounter:\n   * <ol>\n   *   <li> count the number of active requests. </li>\n   *   <li> count memory in use by instrumenting new and delete. </li>\n   *   <li> count queue size by instrumenting enqueue and dequeue. </li>\n   *   <li> count semaphore up and down operations. </li>\n   * </ol>\n   *\n   * @param name the name of the metric.\n   * @param [options] the metric options.\n   */\n  createUpDownCounter<\n    AttributesTypes extends MetricAttributes = MetricAttributes,\n  >(\n    name: string,\n    options?: MetricOptions\n  ): UpDownCounter<AttributesTypes>;\n\n  /**\n   * Creates a new `ObservableGauge` metric.\n   *\n   * The callback SHOULD be safe to be invoked concurrently.\n   *\n   * @param name the name of the metric.\n   * @param [options] the metric options.\n   */\n  createObservableGauge<\n    AttributesTypes extends MetricAttributes = MetricAttributes,\n  >(\n    name: string,\n    options?: MetricOptions\n  ): ObservableGauge<AttributesTypes>;\n\n  /**\n   * Creates a new `ObservableCounter` metric.\n   *\n   * The callback SHOULD be safe to be invoked concurrently.\n   *\n   * @param name the name of the metric.\n   * @param [options] the metric options.\n   */\n  createObservableCounter<\n    AttributesTypes extends MetricAttributes = MetricAttributes,\n  >(\n    name: string,\n    options?: MetricOptions\n  ): ObservableCounter<AttributesTypes>;\n\n  /**\n   * Creates a new `ObservableUpDownCounter` metric.\n   *\n   * The callback SHOULD be safe to be invoked concurrently.\n   *\n   * @param name the name of the metric.\n   * @param [options] the metric options.\n   */\n  createObservableUpDownCounter<\n    AttributesTypes extends MetricAttributes = MetricAttributes,\n  >(\n    name: string,\n    options?: MetricOptions\n  ): ObservableUpDownCounter<AttributesTypes>;\n\n  /**\n   * Sets up a function that will be called whenever a metric collection is\n   * initiated.\n   *\n   * If the function is already in the list of callbacks for this Observable,\n   * the function is not added a second time.\n   *\n   * Only the associated observables can be observed in the callback.\n   * Measurements of observables that are not associated observed in the\n   * callback are dropped.\n   *\n   * @param callback the batch observable callback\n   * @param observables the observables associated with this batch observable callback\n   */\n  addBatchObservableCallback<\n    AttributesTypes extends MetricAttributes = MetricAttributes,\n  >(\n    callback: BatchObservableCallback<AttributesTypes>,\n    observables: Observable<AttributesTypes>[]\n  ): void;\n\n  /**\n   * Removes a callback previously registered with {@link Meter.addBatchObservableCallback}.\n   *\n   * The callback to be removed is identified using a combination of the callback itself,\n   * and the set of the observables associated with it.\n   *\n   * @param callback the batch observable callback\n   * @param observables the observables associated with this batch observable callback\n   */\n  removeBatchObservableCallback<\n    AttributesTypes extends MetricAttributes = MetricAttributes,\n  >(\n    callback: BatchObservableCallback<AttributesTypes>,\n    observables: Observable<AttributesTypes>[]\n  ): void;\n}\n"]}