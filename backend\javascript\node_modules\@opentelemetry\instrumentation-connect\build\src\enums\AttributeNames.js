"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConnectNames = exports.ConnectTypes = exports.AttributeNames = void 0;
var AttributeNames;
(function (AttributeNames) {
    AttributeNames["CONNECT_TYPE"] = "connect.type";
    AttributeNames["CONNECT_NAME"] = "connect.name";
})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));
var ConnectTypes;
(function (ConnectTypes) {
    ConnectTypes["MIDDLEWARE"] = "middleware";
    ConnectTypes["REQUEST_HANDLER"] = "request_handler";
})(ConnectTypes = exports.ConnectTypes || (exports.ConnectTypes = {}));
var ConnectNames;
(function (ConnectNames) {
    ConnectNames["MIDDLEWARE"] = "middleware";
    ConnectNames["REQUEST_HANDLER"] = "request handler";
})(ConnectNames = exports.ConnectNames || (exports.ConnectNames = {}));
//# sourceMappingURL=AttributeNames.js.map