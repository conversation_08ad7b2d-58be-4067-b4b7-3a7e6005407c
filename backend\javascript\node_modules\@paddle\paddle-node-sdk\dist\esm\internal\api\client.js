var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { Environment } from './environment.js';
import { API_ENVIRONMENT_TO_BASE_URL_MAP } from './constants.js';
import { Logger } from '../base/logger.js';
import { LogLevel } from './log-level.js';
import { RuntimeProvider } from '../providers/runtime-provider.js';
import { SDK_VERSION } from '../../version.js';
import { convertToSnakeCase } from './case-helpers.js';
export class Client {
    constructor(apiKey, options) {
        var _a;
        this.apiKey = apiKey;
        this.options = options;
        this.baseUrl = this.getBaseUrl(this.options.environment);
        Logger.logLevel = (_a = this.options.logLevel) !== null && _a !== void 0 ? _a : LogLevel.verbose;
    }
    getBaseUrl(environment) {
        const urlBasedOnEnv = API_ENVIRONMENT_TO_BASE_URL_MAP[environment !== null && environment !== void 0 ? environment : Environment.production];
        return urlBasedOnEnv || environment;
    }
    getHeaders() {
        var _a;
        let uuid;
        const cryptoProvider = (_a = RuntimeProvider.getProvider()) === null || _a === void 0 ? void 0 : _a.crypto;
        if (cryptoProvider) {
            uuid = cryptoProvider.randomUUID();
        }
        else {
            Logger.error('Unknown runtime. Cannot generate uuid');
        }
        return Object.assign({ Authorization: `bearer ${this.apiKey}`, 'Content-Type': 'application/json', 'user-agent': `PaddleSDK/node ${SDK_VERSION}`, 'X-Transaction-ID': uuid !== null && uuid !== void 0 ? uuid : '' }, this.options.customHeaders);
    }
    get(url, queryParams) {
        return __awaiter(this, void 0, void 0, function* () {
            let finalUrl = url.includes(this.baseUrl) ? url : `${this.baseUrl}${url}`;
            if (!finalUrl.includes('?') && queryParams) {
                finalUrl += queryParams.toQueryString();
            }
            const logUrl = finalUrl.split('?')[0];
            const headers = this.getHeaders();
            Logger.logRequest('GET', logUrl, headers);
            const rawResponse = yield fetch(finalUrl, {
                headers,
            });
            Logger.logResponse('GET', logUrl, headers, rawResponse);
            return rawResponse.json();
        });
    }
    post(url, requestBody) {
        return __awaiter(this, void 0, void 0, function* () {
            const logUrl = url.split('?')[0];
            const headers = this.getHeaders();
            Logger.logRequest('POST', logUrl, headers);
            const rawResponse = yield fetch(`${this.baseUrl}${url}`, {
                method: 'POST',
                body: JSON.stringify(convertToSnakeCase(requestBody)),
                headers,
            });
            Logger.logResponse('POST', logUrl, headers, rawResponse);
            return rawResponse.json();
        });
    }
    patch(url, requestBody) {
        return __awaiter(this, void 0, void 0, function* () {
            const logUrl = url.split('?')[0];
            const headers = this.getHeaders();
            Logger.logRequest('PATCH', logUrl, headers);
            const rawResponse = yield fetch(`${this.baseUrl}${url}`, {
                method: 'PATCH',
                body: JSON.stringify(convertToSnakeCase(requestBody)),
                headers,
            });
            Logger.logResponse('PATCH', logUrl, headers, rawResponse);
            return rawResponse.json();
        });
    }
    delete(url) {
        return __awaiter(this, void 0, void 0, function* () {
            const logUrl = url.split('?')[0];
            const headers = this.getHeaders();
            Logger.logRequest('DELETE', logUrl, headers);
            const rawResponse = yield fetch(`${this.baseUrl}${url}`, {
                method: 'DELETE',
                headers,
            });
            Logger.logResponse('DELETE', logUrl, headers, rawResponse);
            return rawResponse;
        });
    }
}
