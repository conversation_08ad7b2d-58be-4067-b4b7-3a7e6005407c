import { ImportMeta, Money, TimePeriod, UnitPriceOverride } from '../shared/index.js';
import { PriceQuantity } from './price-quantity.js';
import { Product } from '../product/index.js';
export class Price {
    constructor(price) {
        var _a, _b;
        this.id = price.id;
        this.productId = price.product_id;
        this.description = price.description;
        this.type = price.type;
        this.name = price.name ? price.name : null;
        this.billingCycle = price.billing_cycle ? new TimePeriod(price.billing_cycle) : null;
        this.trialPeriod = price.trial_period ? new TimePeriod(price.trial_period) : null;
        this.taxMode = price.tax_mode;
        this.unitPrice = new Money(price.unit_price);
        this.unitPriceOverrides =
            (_b = (_a = price.unit_price_overrides) === null || _a === void 0 ? void 0 : _a.map((unit_price_override) => new UnitPriceOverride(unit_price_override))) !== null && _b !== void 0 ? _b : [];
        this.quantity = new PriceQuantity(price.quantity);
        this.status = price.status;
        this.createdAt = price.created_at;
        this.updatedAt = price.updated_at;
        this.customData = price.custom_data ? price.custom_data : null;
        this.importMeta = price.import_meta ? new ImportMeta(price.import_meta) : null;
        this.product = price.product ? new Product(price.product) : null;
    }
}
