// models/businessPlansave.js
import mongoose from 'mongoose';

const savedBusinessPlanSchema = new mongoose.Schema({
    // Link to the user who created the plan
    user: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
        ref: 'User', // This should match the name you used when creating the User model
    },
    // The unique ID (UUID) generated on the frontend
    planId: {
        type: String,
        required: true,
        unique: true, // Ensures no two plan entries can have the same ID
    },
    // The name of the business for easy display in lists
    businessName: {
        type: String,
        required: [true, 'Business name is required.'],
        trim: true,
        default: 'Untitled Business Plan'
    },
    // The complete form data the user submitted to generate the plan
    formData: {
        type: Object,
        required: true,
    },
    // The full AI-generated report data
    reportData: {
        type: Object,
        required: true,
    },
}, {
    // Automatically adds createdAt and updatedAt fields
    timestamps: true,
});

// Create a compound index to ensure a user cannot have duplicate planIds
savedBusinessPlanSchema.index({ user: 1, planId: 1 }, { unique: true });

const SavedBusinessPlan = mongoose.model('SavedBusinessPlan', savedBusinessPlanSchema);

export default SavedBusinessPlan;