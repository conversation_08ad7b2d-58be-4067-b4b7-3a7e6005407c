{"version": 3, "file": "LoggerProvider.js", "sourceRoot": "", "sources": ["../../../src/types/LoggerProvider.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from './Logger';\nimport { LoggerOptions } from './LoggerOptions';\n\n/**\n * A registry for creating named {@link Logger}s.\n */\nexport interface LoggerProvider {\n  /**\n   * Returns a Logger, creating one if one with the given name, version, and\n   * schemaUrl pair is not already created.\n   *\n   * @param name The name of the logger or instrumentation library.\n   * @param version The version of the logger or instrumentation library.\n   * @param options The options of the logger or instrumentation library.\n   * @returns Logger A Logger with the given name and version\n   */\n  getLogger(name: string, version?: string, options?: LoggerOptions): Logger;\n}\n"]}