import { General } from './general.js';
import { CustomerPortalSubscriptionUrl } from './customer-portal-subscription-url.js';
export class Urls {
    constructor(urlsResponse) {
        var _a, _b;
        this.general = new General(urlsResponse.general);
        this.subscriptions =
            (_b = (_a = urlsResponse.subscriptions) === null || _a === void 0 ? void 0 : _a.map((subscription) => new CustomerPortalSubscriptionUrl(subscription))) !== null && _b !== void 0 ? _b : [];
    }
}
