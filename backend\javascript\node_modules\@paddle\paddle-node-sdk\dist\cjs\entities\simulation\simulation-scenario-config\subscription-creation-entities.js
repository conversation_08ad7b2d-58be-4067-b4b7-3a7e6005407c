"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubscriptionCreationEntities = void 0;
const subscription_creation_item_js_1 = require("./subscription-creation-item.js");
class SubscriptionCreationEntities {
    constructor(entities) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        this.customerId = (_a = entities === null || entities === void 0 ? void 0 : entities.customer_id) !== null && _a !== void 0 ? _a : null;
        this.addressId = (_b = entities === null || entities === void 0 ? void 0 : entities.address_id) !== null && _b !== void 0 ? _b : null;
        this.businessId = (_c = entities === null || entities === void 0 ? void 0 : entities.business_id) !== null && _c !== void 0 ? _c : null;
        this.paymentMethodId = (_d = entities === null || entities === void 0 ? void 0 : entities.payment_method_id) !== null && _d !== void 0 ? _d : null;
        this.discountId = (_e = entities === null || entities === void 0 ? void 0 : entities.discount_id) !== null && _e !== void 0 ? _e : null;
        this.transactionId = (_f = entities === null || entities === void 0 ? void 0 : entities.transaction_id) !== null && _f !== void 0 ? _f : null;
        this.items = (_h = (_g = entities === null || entities === void 0 ? void 0 : entities.items) === null || _g === void 0 ? void 0 : _g.map((item) => new subscription_creation_item_js_1.SubscriptionCreationItem(item))) !== null && _h !== void 0 ? _h : null;
    }
}
exports.SubscriptionCreationEntities = SubscriptionCreationEntities;
