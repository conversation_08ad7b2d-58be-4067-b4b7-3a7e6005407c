{"version": 3, "file": "suppress-tracing.js", "sourceRoot": "", "sources": ["../../../src/trace/suppress-tracing.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA+D;AAE/D,MAAM,oBAAoB,GAAG,IAAA,sBAAgB,EAC3C,gDAAgD,CACjD,CAAC;AAEF,SAAgB,eAAe,CAAC,OAAgB;IAC9C,OAAO,OAAO,CAAC,QAAQ,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;AACtD,CAAC;AAFD,0CAEC;AAED,SAAgB,iBAAiB,CAAC,OAAgB;IAChD,OAAO,OAAO,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;AACnD,CAAC;AAFD,8CAEC;AAED,SAAgB,mBAAmB,CAAC,OAAgB;IAClD,OAAO,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC,KAAK,IAAI,CAAC;AACzD,CAAC;AAFD,kDAEC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, createContextKey } from '@opentelemetry/api';\n\nconst SUPPRESS_TRACING_KEY = createContextKey(\n  'OpenTelemetry SDK Context Key SUPPRESS_TRACING'\n);\n\nexport function suppressTracing(context: Context): Context {\n  return context.setValue(SUPPRESS_TRACING_KEY, true);\n}\n\nexport function unsuppressTracing(context: Context): Context {\n  return context.deleteValue(SUPPRESS_TRACING_KEY);\n}\n\nexport function isTracingSuppressed(context: Context): boolean {\n  return context.getValue(SUPPRESS_TRACING_KEY) === true;\n}\n"]}