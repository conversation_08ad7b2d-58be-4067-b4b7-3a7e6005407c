import { PaymentCard, PayPal } from '../shared/index.js';
export class PaymentMethod {
    constructor(paymentMethodResponse) {
        this.id = paymentMethodResponse.id;
        this.customerId = paymentMethodResponse.customer_id;
        this.addressId = paymentMethodResponse.address_id;
        this.type = paymentMethodResponse.type;
        this.card = paymentMethodResponse.card ? new PaymentCard(paymentMethodResponse.card) : null;
        this.paypal = paymentMethodResponse.paypal ? new PayPal(paymentMethodResponse.paypal) : null;
        this.origin = paymentMethodResponse.origin;
        this.savedAt = paymentMethodResponse.saved_at;
        this.updatedAt = paymentMethodResponse.updated_at;
    }
}
