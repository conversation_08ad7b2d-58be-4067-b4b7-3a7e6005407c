"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PricingPreview = void 0;
const index_js_1 = require("../transaction/index.js");
const pricing_preview_details_js_1 = require("./pricing-preview-details.js");
class PricingPreview {
    constructor(pricePreview) {
        var _a, _b, _c, _d, _e, _f;
        this.customerId = (_a = pricePreview.customer_id) !== null && _a !== void 0 ? _a : null;
        this.addressId = (_b = pricePreview.address_id) !== null && _b !== void 0 ? _b : null;
        this.businessId = (_c = pricePreview.business_id) !== null && _c !== void 0 ? _c : null;
        this.currencyCode = pricePreview.currency_code;
        this.discountId = (_d = pricePreview.discount_id) !== null && _d !== void 0 ? _d : null;
        this.address = pricePreview.address ? new index_js_1.AddressPreview(pricePreview.address) : null;
        this.customerIpAddress = (_e = pricePreview.customer_ip_address) !== null && _e !== void 0 ? _e : null;
        this.details = new pricing_preview_details_js_1.PricingPreviewDetails(pricePreview.details);
        this.availablePaymentMethods = (_f = pricePreview.available_payment_methods) !== null && _f !== void 0 ? _f : [];
    }
}
exports.PricingPreview = PricingPreview;
