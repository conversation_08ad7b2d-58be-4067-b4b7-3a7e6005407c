// src/pages/admin/AdminDashboard.jsx
import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { Navigate } from 'react-router-dom';
import {
  FiUsers,
  FiShield,
  FiSettings,
  FiBarChart,
  FiLoader,
  FiAlertCircle,
  FiCreditCard
} from 'react-icons/fi';

// Import dashboard components
import UserManagement from '../../components/admin/UserManagement';
import AdminManagement from '../../components/admin/AdminManagement';
import SystemSettings from '../../components/admin/SystemSettings';
import Analytics from '../../components/admin/Analytics';
import PaymentManagement from '../../components/admin/PaymentManagement';

const AdminDashboard = () => {
  const { currentUser, isAuthenticated, isLoading } = useAuth();
  const [activeTab, setActiveTab] = useState('users');
  const [dashboardLoading, setDashboardLoading] = useState(true);

  useEffect(() => {
    // Simulate loading time for dashboard initialization
    const timer = setTimeout(() => {
      setDashboardLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Show loading spinner while checking authentication
  if (isLoading || dashboardLoading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <FiLoader className="w-12 h-12 text-purple-500 animate-spin mx-auto mb-4" />
          <p className="text-slate-300">Loading Admin Dashboard...</p>
        </div>
      </div>
    );
  }

  // Redirect if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/" replace />;
  }

  // Redirect if not admin
  if (!currentUser?.isAdmin) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <FiAlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-white mb-2">Access Denied</h1>
          <p className="text-slate-400 mb-6">
            You don't have administrator privileges to access this page.
          </p>
          <button
            onClick={() => window.history.back()}
            className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'users', label: 'User Management', icon: FiUsers },
    { id: 'admins', label: 'Admin Management', icon: FiShield },
    { id: 'payments', label: 'Payment Management', icon: FiCreditCard },
    { id: 'analytics', label: 'Analytics', icon: FiBarChart },
    { id: 'settings', label: 'System Settings', icon: FiSettings },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'users':
        return <UserManagement />;
      case 'admins':
        return <AdminManagement />;
      case 'payments':
        return <PaymentManagement />;
      case 'analytics':
        return <Analytics />;
      case 'settings':
        return <SystemSettings />;
      default:
        return <UserManagement />;
    }
  };

  return (
    <div className="min-h-screen bg-slate-900 flex flex-col">
      {/* Header */}
      <div className="bg-slate-800 border-b border-slate-700 flex-shrink-0">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <FiShield className="w-8 h-8 text-purple-500 mr-3" />
              <h1 className="text-xl font-bold text-white">Admin Dashboard</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-slate-400">
                Welcome, {currentUser.name || currentUser.email}
              </span>
              <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">
                  {(currentUser.name || currentUser.email).charAt(0).toUpperCase()}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Layout */}
      <div className="flex flex-1 overflow-hidden">
        {/* Fixed Sidebar Navigation */}
        <div className="w-64 bg-slate-800 border-r border-slate-700 flex-shrink-0">
          <nav className="p-4 space-y-2 h-full overflow-y-auto">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                    activeTab === tab.id
                      ? 'bg-purple-600 text-white'
                      : 'text-slate-300 hover:bg-slate-700 hover:text-white'
                  }`}
                >
                  <Icon className="w-5 h-5 mr-3" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Main Content Area with Scrolling */}
        <div className="flex-1 overflow-y-auto">
          <div className="bg-slate-900 min-h-full">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
