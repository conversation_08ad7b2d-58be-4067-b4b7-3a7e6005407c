// src/components/admin/AdminManagement.jsx
import React, { useState, useEffect } from 'react';
import {
  FiShield,
  FiLoader,
  FiStar,
  FiUser,
  FiMail,
  FiCalendar
} from 'react-icons/fi';

const AdminManagement = () => {
  const [adminUsers, setAdminUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const API_BASE_URL = import.meta.env.VITE_NODE_BACKEND_URL || 'http://localhost:3001';

  const fetchAdminUsers = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('authToken');
      
      const response = await fetch(`${API_BASE_URL}/api/admin/admins`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch admin users');
      }

      const data = await response.json();
      setAdminUsers(data.adminUsers);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAdminUsers();
  }, []);

  if (loading) {
    return (
      <div className="p-6 flex items-center justify-center">
        <FiLoader className="w-8 h-8 text-purple-500 animate-spin" />
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <FiShield className="w-6 h-6 text-purple-500 mr-3" />
          <h2 className="text-xl font-bold text-white">Admin Management</h2>
        </div>
        <div className="text-sm text-slate-400">
          Total Admins: {adminUsers.length}
        </div>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-900/50 border border-red-700 rounded-lg text-red-300">
          {error}
        </div>
      )}

      {/* Admin Info Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {adminUsers.map((admin, index) => (
          <div
            key={admin._id}
            className="bg-slate-700 rounded-lg p-6 border border-slate-600 hover:border-purple-500 transition-colors"
          >
            {/* Admin Avatar and Basic Info */}
            <div className="flex items-center mb-4">
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-lg font-bold">
                    {(admin.name || admin.email).charAt(0).toUpperCase()}
                  </span>
                </div>
                {index === 0 && (
                  <div className="absolute -top-1 -right-1 w-5 h-5 bg-yellow-500 rounded-full flex items-center justify-center">
                    <FiStar className="w-3 h-3 text-yellow-900" />
                  </div>
                )}
              </div>
              <div className="ml-4 flex-1">
                <h3 className="text-lg font-semibold text-white">
                  {admin.name || 'No Name'}
                </h3>
                <p className="text-sm text-slate-400">
                  {index === 0 ? 'System Administrator' : 'Administrator'}
                </p>
              </div>
            </div>

            {/* Admin Details */}
            <div className="space-y-3">
              <div className="flex items-center text-sm">
                <FiMail className="w-4 h-4 text-slate-400 mr-3" />
                <span className="text-slate-300">{admin.email}</span>
              </div>
              
              <div className="flex items-center text-sm">
                <FiUser className="w-4 h-4 text-slate-400 mr-3" />
                <span className="text-slate-300">
                  Status: {admin.isVerified ? 'Verified' : 'Unverified'}
                </span>
              </div>

              <div className="flex items-center text-sm">
                <FiCalendar className="w-4 h-4 text-slate-400 mr-3" />
                <span className="text-slate-300">
                  Joined: {new Date(admin.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>

            {/* Admin Privileges */}
            <div className="mt-4 pt-4 border-t border-slate-600">
              <h4 className="text-sm font-medium text-slate-300 mb-2">Privileges</h4>
              <div className="flex flex-wrap gap-2">
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-900/50 text-purple-300 border border-purple-700">
                  User Management
                </span>
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-900/50 text-blue-300 border border-blue-700">
                  System Settings
                </span>
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-900/50 text-green-300 border border-green-700">
                  Analytics
                </span>
                {index === 0 && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-900/50 text-yellow-300 border border-yellow-700">
                    Super Admin
                  </span>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Admin Registration Info */}
      <div className="mt-8 bg-slate-700 rounded-lg p-6 border border-slate-600">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiShield className="w-5 h-5 text-purple-500 mr-2" />
          Admin Registration Information
        </h3>
        
        <div className="grid gap-4 md:grid-cols-2">
          <div className="bg-slate-800 rounded-lg p-4">
            <h4 className="text-sm font-medium text-slate-300 mb-2">Current Status</h4>
            <p className="text-white">
              {adminUsers.length > 0 ? 'Admin system is active' : 'No administrators found'}
            </p>
            <p className="text-sm text-slate-400 mt-1">
              {adminUsers.length} administrator{adminUsers.length !== 1 ? 's' : ''} registered
            </p>
          </div>
          
          <div className="bg-slate-800 rounded-lg p-4">
            <h4 className="text-sm font-medium text-slate-300 mb-2">Registration Policy</h4>
            <p className="text-white">One-time registration</p>
            <p className="text-sm text-slate-400 mt-1">
              Admin registration is disabled after the first admin is created
            </p>
          </div>
        </div>

        <div className="mt-4 p-4 bg-blue-900/20 border border-blue-700 rounded-lg">
          <h4 className="text-sm font-medium text-blue-300 mb-2">Security Note</h4>
          <p className="text-sm text-slate-300">
            The first registered administrator becomes the system administrator with full privileges. 
            Additional admin privileges can only be granted by existing administrators through the user management interface.
          </p>
        </div>
      </div>

      {/* Empty State */}
      {adminUsers.length === 0 && !loading && (
        <div className="text-center py-12">
          <FiShield className="w-16 h-16 text-slate-600 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-slate-300 mb-2">No Administrators Found</h3>
          <p className="text-slate-400">
            No admin users are currently registered in the system.
          </p>
        </div>
      )}
    </div>
  );
};

export default AdminManagement;
