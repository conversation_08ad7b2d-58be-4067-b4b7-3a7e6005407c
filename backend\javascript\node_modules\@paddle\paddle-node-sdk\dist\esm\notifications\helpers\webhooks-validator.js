var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { RuntimeProvider } from '../../internal/providers/runtime-provider.js';
import { Logger } from '../../internal/base/logger.js';
export class WebhooksValidator {
    extractHeader(header) {
        const parts = header.split(';');
        let ts = '';
        let h1 = '';
        for (const part of parts) {
            const [key, value] = part.split('=');
            if (value) {
                if (key === 'ts') {
                    ts = value;
                }
                else if (key === 'h1') {
                    h1 = value;
                }
            }
        }
        if (ts && h1) {
            return { ts: parseInt(ts), h1 };
        }
        else {
            throw new Error('[Paddle] Invalid webhook signature');
        }
    }
    isValidSignature(requestBody, secretKey, signature) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            const cryptoProvider = (_a = RuntimeProvider.getProvider()) === null || _a === void 0 ? void 0 : _a.crypto;
            if (!cryptoProvider) {
                Logger.error('Unknown runtime. Cannot validate webhook signature');
                return false;
            }
            const headers = this.extractHeader(signature);
            const payloadWithTime = `${headers.ts}:${requestBody}`;
            if (new Date().getTime() > new Date((headers.ts + WebhooksValidator.MAX_VALID_TIME_DIFFERENCE) * 1000).getTime()) {
                return false;
            }
            const computedHash = yield cryptoProvider.computeHmac(payloadWithTime, secretKey);
            return computedHash === headers.h1;
        });
    }
}
WebhooksValidator.MAX_VALID_TIME_DIFFERENCE = 5;
