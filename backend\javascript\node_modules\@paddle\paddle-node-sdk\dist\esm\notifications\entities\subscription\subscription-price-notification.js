import { ImportMetaNotification, MoneyNotification, TimePeriodNotification, UnitPriceOverrideNotification, } from '../shared/index.js';
import { PriceQuantityNotification } from '../price/index.js';
export class SubscriptionPriceNotification {
    constructor(price) {
        var _a, _b;
        this.id = price.id;
        this.productId = price.product_id;
        this.description = price.description;
        this.type = (_a = price.type) !== null && _a !== void 0 ? _a : null;
        this.name = price.name ? price.name : null;
        this.billingCycle = price.billing_cycle ? new TimePeriodNotification(price.billing_cycle) : null;
        this.trialPeriod = price.trial_period ? new TimePeriodNotification(price.trial_period) : null;
        this.taxMode = price.tax_mode;
        this.unitPrice = price.unit_price ? new MoneyNotification(price.unit_price) : null;
        this.unitPriceOverrides = price.unit_price_overrides
            ? price.unit_price_overrides.map((unit_price_override) => new UnitPriceOverrideNotification(unit_price_override))
            : [];
        this.quantity = price.quantity ? new PriceQuantityNotification(price.quantity) : null;
        this.status = (_b = price.status) !== null && _b !== void 0 ? _b : null;
        this.customData = price.custom_data ? price.custom_data : null;
        this.importMeta = price.import_meta ? new ImportMetaNotification(price.import_meta) : null;
    }
}
