"use strict";
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.convertToSnakeCase = convertToSnakeCase;
function isTopLevelCustomDataCamel(input) {
    return 'customData' in input;
}
function isObject(input) {
    return input != null && typeof input === 'object';
}
function snakeCase(input) {
    return input
        .trim()
        .replace(/([a-z])([A-Z])/g, '$1_$2')
        .replace(/[\W]+/g, '_')
        .replace(/^_+|_+$/g, '')
        .toLowerCase();
}
function decamelizeKeys(obj) {
    if (!isObject(obj) ||
        obj instanceof Date ||
        obj instanceof RegExp ||
        typeof obj === 'boolean' ||
        typeof obj === 'function') {
        return obj;
    }
    let output;
    let i = 0;
    let l = 0;
    if (Array.isArray(obj)) {
        output = [];
        for (l = obj.length; i < l; i++) {
            output.push(decamelizeKeys(obj[i]));
        }
    }
    else {
        output = {};
        for (const key in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
                output[snakeCase(key)] = decamelizeKeys(obj[key]);
            }
        }
    }
    return output;
}
function convertToSnakeCase(input) {
    if (!input || !isObject(input)) {
        return input;
    }
    if (isTopLevelCustomDataCamel(input)) {
        const { customData } = input, rest = __rest(input, ["customData"]);
        const result = decamelizeKeys(rest);
        return Object.assign(Object.assign({}, result), { custom_data: customData });
    }
    else {
        return decamelizeKeys(input);
    }
}
