// routes/admin/adminRoutes.js
import express from 'express';
import { protect } from '../../middleware/authMiddleware.js';
import { requireAdmin } from '../../middleware/adminMiddleware.js';
import {
    getAllUsers,
    getUserById,
    updateUser,
    deleteUser,
    getAdminUsers,
    getSystemAnalytics,
    updateSystemSettings,
    checkAdminRegistrationStatus
} from '../../controllers/admin/adminController.js';

const router = express.Router();

// Public route to check admin registration status (no auth required)
router.get('/registration-status', checkAdminRegistrationStatus);

// Apply authentication and admin protection to protected routes
router.use(protect);
router.use(requireAdmin);

// User management routes
router.get('/users', getAllUsers);
router.get('/users/:id', getUserById);
router.put('/users/:id', updateUser);
router.delete('/users/:id', deleteUser);

// Admin management routes
router.get('/admins', getAdminUsers);

// System analytics routes
router.get('/analytics', getSystemAnalytics);

// System settings routes
router.put('/settings', updateSystemSettings);

export default router;
