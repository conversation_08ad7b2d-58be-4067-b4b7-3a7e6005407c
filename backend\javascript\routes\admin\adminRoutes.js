// routes/admin/adminRoutes.js
import express from 'express';
import { protect } from '../../middleware/authMiddleware.js';
import { requireAdmin } from '../../middleware/adminMiddleware.js';
import {
    validateUserUpdate,
    validateUserId,
    validateUserQuery,
    validateSystemSettings
} from '../../middleware/validation.js';
import { asyncHandler } from '../../middleware/errorHandler.js';
import {
    getAllUsers,
    getUserById,
    updateUser,
    deleteUser,
    getAdminUsers,
    getSystemAnalytics,
    updateSystemSettings,
    checkAdminRegistrationStatus,
    getSubscriptionPlans,
    getSubscriptionPlan,
    updateSubscriptionPlan,
    resetSubscriptionPlan,
    getSubscriptionStats
} from '../../controllers/admin/adminController.js';

const router = express.Router();

// Public route to check admin registration status (no auth required)
router.get('/registration-status', asyncHandler(checkAdminRegistrationStatus));

// Apply authentication and admin protection to protected routes
router.use(protect);
router.use(requireAdmin);

// User management routes
router.get('/users', validateUserQuery, asyncHandler(getAllUsers));
router.get('/users/:id', validateUserId, asyncHandler(getUserById));
router.put('/users/:id', validateUserUpdate, asyncHandler(updateUser));
router.delete('/users/:id', validateUserId, asyncHandler(deleteUser));

// Admin management routes
router.get('/admins', asyncHandler(getAdminUsers));

// System analytics routes
router.get('/analytics', asyncHandler(getSystemAnalytics));

// System settings routes
router.put('/settings', validateSystemSettings, asyncHandler(updateSystemSettings));

// Payment/Subscription management routes
router.get('/subscription-plans', asyncHandler(getSubscriptionPlans));
router.get('/subscription-plans/:planName', asyncHandler(getSubscriptionPlan));
router.put('/subscription-plans/:planName', asyncHandler(updateSubscriptionPlan));
router.post('/subscription-plans/:planName/reset', asyncHandler(resetSubscriptionPlan));
router.get('/subscription-stats', asyncHandler(getSubscriptionStats));

export default router;
