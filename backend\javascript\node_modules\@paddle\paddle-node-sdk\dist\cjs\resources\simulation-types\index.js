"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimulationTypesResource = void 0;
const index_js_1 = require("../../internal/base/index.js");
const index_js_2 = require("../../entities/index.js");
const SimulationTypesPaths = {
    list: '/simulation-types',
};
class SimulationTypesResource extends index_js_1.BaseResource {
    list() {
        return __awaiter(this, void 0, void 0, function* () {
            const response = yield this.client.get(SimulationTypesPaths.list);
            const data = this.handleResponse(response);
            return data.map((simulationType) => new index_js_2.SimulationType(simulationType));
        });
    }
}
exports.SimulationTypesResource = SimulationTypesResource;
