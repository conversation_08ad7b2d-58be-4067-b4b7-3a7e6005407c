{"version": 3, "file": "enum.js", "sourceRoot": "", "sources": ["../../src/enum.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,IAAY,qBAIX;AAJD,WAAY,qBAAqB;IAC/B,wCAAe,CAAA;IACf,8CAAqB,CAAA;IACrB,sDAA6B,CAAA;AAC/B,CAAC,EAJW,qBAAqB,GAArB,6BAAqB,KAArB,6BAAqB,QAIhC;AAED,IAAY,SAuBX;AAvBD,WAAY,SAAS;IACnB,0BAAa,CAAA;IACb,0BAAa,CAAA;IACb,uBAAU,CAAA;IACV,yBAAY,CAAA;IACZ,sBAAS,CAAA;IACT,0BAAa,CAAA;IACb,0BAAa,CAAA;IACb,2BAAc,CAAA;IACd,wBAAW,CAAA;IACX,yBAAY,CAAA;IACZ,qBAAQ,CAAA;IACR,4BAAe,CAAA;IACf,4BAAe,CAAA;IACf,0BAAa,CAAA;IACb,uBAAU,CAAA;IACV,0BAAa,CAAA;IACb,0BAAa,CAAA;IACb,wBAAW,CAAA;IACX,4BAAe,CAAA;IACf,8BAAiB,CAAA;IACjB,yCAA4B,CAAA;IAC5B,gCAAmB,CAAA;AACrB,CAAC,EAvBW,SAAS,GAAT,iBAAS,KAAT,iBAAS,QAuBpB;AAED,IAAY,SAOX;AAPD,WAAY,SAAS;IACnB,wCAA2B,CAAA;IAC3B,oCAAuB,CAAA;IACvB,wCAA2B,CAAA;IAC3B,0CAA6B,CAAA;IAC7B,uDAA0C,CAAA;IAC1C,iDAAoC,CAAA;AACtC,CAAC,EAPW,SAAS,GAAT,iBAAS,KAAT,iBAAS,QAOpB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport enum AllowedOperationTypes {\n  QUERY = 'query',\n  MUTATION = 'mutation',\n  SUBSCRIPTION = 'subscription',\n}\n\nexport enum TokenKind {\n  SOF = '<SOF>',\n  EOF = '<EOF>',\n  BANG = '!',\n  DOLLAR = '$',\n  AMP = '&',\n  PAREN_L = '(',\n  PAREN_R = ')',\n  SPREAD = '...',\n  COLON = ':',\n  EQUALS = '=',\n  AT = '@',\n  BRACKET_L = '[',\n  BRACKET_R = ']',\n  BRACE_L = '{',\n  PIPE = '|',\n  BRACE_R = '}',\n  NAME = 'Name',\n  INT = 'Int',\n  FLOAT = 'Float',\n  STRING = 'String',\n  BLOCK_STRING = 'BlockString',\n  COMMENT = 'Comment',\n}\n\nexport enum SpanNames {\n  EXECUTE = 'graphql.execute',\n  PARSE = 'graphql.parse',\n  RESOLVE = 'graphql.resolve',\n  VALIDATE = 'graphql.validate',\n  SCHEMA_VALIDATE = 'graphql.validateSchema',\n  SCHEMA_PARSE = 'graphql.parseSchema',\n}\n"]}