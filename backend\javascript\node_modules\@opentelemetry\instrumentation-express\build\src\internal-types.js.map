{"version": 3, "file": "internal-types.js", "sourceRoot": "", "sources": ["../../src/internal-types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAKH;;;GAGG;AACU,QAAA,aAAa,GAAkB,MAAM,CAAC,uBAAuB,CAAC,CAAC;AAE5E;;;;;;;;;;;;;;;GAeG;AACU,QAAA,sBAAsB,GAAG,kBAAkB,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { Request } from 'express';\nimport { Attributes } from '@opentelemetry/api';\n\n/**\n * This symbol is used to mark express layer as being already instrumented\n * since its possible to use a given layer multiple times (ex: middlewares)\n */\nexport const kLayerPatched: unique symbol = Symbol('express-layer-patched');\n\n/**\n * This const define where on the `request` object the Instrumentation will mount the\n * current stack of express layer.\n *\n * It is necessary because express doesn't store the different layers\n * (ie: middleware, router etc) that it called to get to the current layer.\n * Given that, the only way to know the route of a given layer is to\n * store the path of where each previous layer has been mounted.\n *\n * ex: bodyParser > auth middleware > /users router > get /:id\n *  in this case the stack would be: [\"/users\", \"/:id\"]\n *\n * ex2: bodyParser > /api router > /v1 router > /users router > get /:id\n *  stack: [\"/api\", \"/v1\", \"/users\", \":id\"]\n *\n */\nexport const _LAYERS_STORE_PROPERTY = '__ot_middlewares';\n\nexport type PatchedRequest = {\n  [_LAYERS_STORE_PROPERTY]?: string[];\n} & Request;\nexport type PathParams = string | RegExp | Array<string | RegExp>;\n\n// https://github.com/expressjs/express/blob/main/lib/router/index.js#L53\nexport type ExpressRouter = {\n  params: { [key: string]: string };\n  _params: string[];\n  caseSensitive: boolean;\n  mergeParams: boolean;\n  strict: boolean;\n  stack: ExpressLayer[];\n};\n\n// https://github.com/expressjs/express/blob/main/lib/router/layer.js#L33\nexport type ExpressLayer = {\n  handle: Function & Record<string, any>;\n  [kLayerPatched]?: boolean;\n  name: string;\n  params: { [key: string]: string };\n  path: string;\n  regexp: RegExp;\n  route?: ExpressLayer;\n};\n\nexport type LayerMetadata = {\n  attributes: Attributes;\n  name: string;\n};\n"]}