import { LoggerProvider } from './types/LoggerProvider';
import { Logger } from './types/Logger';
import { LoggerOptions } from './types/LoggerOptions';
export declare class ProxyLoggerProvider implements LoggerProvider {
    private _delegate?;
    getLogger(name: string, version?: string | undefined, options?: LoggerOptions | undefined): Logger;
    getDelegate(): LoggerProvider;
    /**
     * Set the delegate logger provider
     */
    setDelegate(delegate: LoggerProvider): void;
    getDelegateLogger(name: string, version?: string | undefined, options?: LoggerOptions | undefined): Logger | undefined;
}
//# sourceMappingURL=ProxyLoggerProvider.d.ts.map