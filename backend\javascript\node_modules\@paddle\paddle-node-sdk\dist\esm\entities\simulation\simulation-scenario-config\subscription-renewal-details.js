import { SubscriptionRenewalEntities } from './subscription-renewal-entities.js';
import { SubscriptionRenewalOptions } from './subscription-renewal-options.js';
export class SubscriptionRenewalDetails {
    constructor(config) {
        this.entities = config.entities ? new SubscriptionRenewalEntities(config.entities) : null;
        this.options = config.options ? new SubscriptionRenewalOptions(config.options) : null;
    }
}
