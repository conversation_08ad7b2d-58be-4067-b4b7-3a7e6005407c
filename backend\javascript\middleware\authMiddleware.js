// middleware/authMiddleware.js
import jwt from 'jsonwebtoken';
import User from '../models/User.js'; // Adjust path if your User model is elsewhere

/**
 * Protects routes by verifying a JWT token from the Authorization header.
 * If valid, it attaches the user object (minus password) to the request object (req.user).
 * This middleware is crucial for ensuring that only authenticated users of YOUR application
 * can access certain endpoints, like those initiating PayPal payments.
 */
const protect = async (req, res, next) => {
    let token;
    // For debugging, you can log which route is being protected:
    // console.log(`Protect middleware: Attempting to authenticate for ${req.method} ${req.originalUrl}`);

    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
        try {
            // Extract token from "Bearer <token>"
            token = req.headers.authorization.split(' ')[1];

            if (!process.env.JWT_SECRET) {
                console.error('CRITICAL SERVER ERROR: JWT_SECRET is not defined in environment variables!');
                // This is a server configuration issue, not a client error.
                return res.status(500).json({ error: 'Server configuration error: Unable to process authentication.' });
            }

            // Verify the token
            const decoded = jwt.verify(token, process.env.JWT_SECRET);

            // Ensure the JWT payload has an 'id' field (or whatever field you use for user ID in the token)
            if (!decoded.id) {
                console.error('Authorization Error: User ID (decoded.id) not found in JWT payload.', decoded);
                return res.status(401).json({ error: 'Not authorized, token is missing user identification.' });
            }

            // Fetch the user from the database using the ID from the token.
            // Deselect the password so it's not inadvertently exposed.
            // req.user will be available in subsequent middleware and route handlers.
            req.user = await User.findById(decoded.id).select('-password');

            if (!req.user) {
                console.error('Authorization Error: User not found in database for ID from token:', decoded.id);
                return res.status(401).json({ error: 'Not authorized, user associated with this token no longer exists.' });
            }

            // User is authenticated, proceed to the next middleware or route handler
            // console.log(`User ${req.user._id} authenticated successfully for ${req.originalUrl}`);
            next();
        } catch (error) {
            console.error('Authentication error in protect middleware:', error.name, '-', error.message);
            if (error.name === 'JsonWebTokenError') {
                return res.status(401).json({ error: 'Not authorized, token is invalid or malformed.' });
            }
            if (error.name === 'TokenExpiredError') {
                return res.status(401).json({ error: 'Not authorized, token has expired. Please log in again.' });
            }
            // For other unexpected errors during token processing
            return res.status(401).json({ error: 'Not authorized, token processing failed.' });
        }
    } else {
        // console.log('Authorization Error: No Authorization header with Bearer token found.');
        return res.status(401).json({ error: 'Not authorized, no token provided or authentication header is malformed.' });
    }
};

export { protect };