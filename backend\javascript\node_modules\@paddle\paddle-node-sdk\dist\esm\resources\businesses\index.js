var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { BaseResource, PathParameters, QueryParameters } from '../../internal/base/index.js';
import { Business, BusinessCollection } from '../../entities/index.js';
const BusinessPaths = {
    list: '/customers/{customer_id}/businesses',
    create: '/customers/{customer_id}/businesses',
    get: '/customers/{customer_id}/businesses/{business_id}',
    update: '/customers/{customer_id}/businesses/{business_id}',
};
export * from './operations/index.js';
export class BusinessesResource extends BaseResource {
    list(customerId, queryParams) {
        const queryParameters = new QueryParameters(queryParams);
        const urlWithPathParams = new PathParameters(BusinessPaths.list, {
            customer_id: customerId,
        }).deriveUrl();
        return new BusinessCollection(this.client, urlWithPathParams + queryParameters.toQueryString());
    }
    create(customerId, createBusinessParameters) {
        return __awaiter(this, void 0, void 0, function* () {
            const urlWithPathParams = new PathParameters(BusinessPaths.create, {
                customer_id: customerId,
            }).deriveUrl();
            const response = yield this.client.post(urlWithPathParams, createBusinessParameters);
            const data = this.handleResponse(response);
            return new Business(data);
        });
    }
    get(customerId, businessId) {
        return __awaiter(this, void 0, void 0, function* () {
            const urlWithPathParams = new PathParameters(BusinessPaths.get, {
                customer_id: customerId,
                business_id: businessId,
            }).deriveUrl();
            const response = yield this.client.get(urlWithPathParams);
            const data = this.handleResponse(response);
            return new Business(data);
        });
    }
    update(customerId, businessId, updateBusiness) {
        return __awaiter(this, void 0, void 0, function* () {
            const urlWithPathParams = new PathParameters(BusinessPaths.update, {
                customer_id: customerId,
                business_id: businessId,
            }).deriveUrl();
            const response = yield this.client.patch(urlWithPathParams, updateBusiness);
            const data = this.handleResponse(response);
            return new Business(data);
        });
    }
    archive(customerId, businessId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.update(customerId, businessId, { status: 'archived' });
        });
    }
}
