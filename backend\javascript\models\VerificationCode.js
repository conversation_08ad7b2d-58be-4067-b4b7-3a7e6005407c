// models/VerificationCode.js
import mongoose from 'mongoose';
import shortid from 'shortid'; // For simpler record IDs if needed

const VerificationCodeSchema = new mongoose.Schema({
  _id: {
    type: String,
    default: shortid.generate, // Optional: for easier to read IDs in DB
  },
  email: {
    type: String,
    required: true,
    lowercase: true,
    trim: true,
  },
  code: {
    type: String,
    required: true,
  },
  expiresAt: {
    type: Date,
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

// Create an index for automatic deletion of expired codes by MongoDB TTL feature.
// This requires MongoDB server version 2.2 or later.
// The 'expireAfterSeconds: 0' means documents are deleted when 'expiresAt' time is reached.
// It's good practice to define indexes on the schema directly.
VerificationCodeSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Optional: Index on email for faster lookups if you query by email frequently
VerificationCodeSchema.index({ email: 1 });

const VerificationCode = mongoose.model('VerificationCode', VerificationCodeSchema);

export default VerificationCode; // Export the VerificationCode model as the default export