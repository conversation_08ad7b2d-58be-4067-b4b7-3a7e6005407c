export class SubscriptionRenewalOptions {
    constructor(options) {
        var _a, _b;
        this.paymentOutcome = (_a = options === null || options === void 0 ? void 0 : options.payment_outcome) !== null && _a !== void 0 ? _a : null;
        this.dunningExhaustedAction = (_b = options === null || options === void 0 ? void 0 : options.dunning_exhausted_action) !== null && _b !== void 0 ? _b : null;
    }
}
