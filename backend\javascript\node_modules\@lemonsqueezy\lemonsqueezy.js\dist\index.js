var k={};function $(t){return k[t]}function C(t,e){k[t]=e}var v="__config__",I="https://api.lemonsqueezy.com";function T(t){return Object.prototype.toString.call(t)==="[object Object]"}function U(t){return t.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`)}function c(t,e=void 0){let o={};for(let a in t)t[a]!==e&&(o[U(a)]=T(t[a])?c(t[a],e):t[a]);return o}function i(t){return!t||!Array.isArray(t)||!t.length?"":`?include=${t.join(",")}`}function n(t){let{filter:e={},page:o={},include:a=[]}=t,u=c({filter:e,page:o,include:a.join(",")}),p={};for(let y in u){let h=u[y];if(T(h))for(let m in h)p[`${y}[${m}]`]=`${h[m]}`;else p[`${y}`]=`${u[y]}`}let d=new URLSearchParams(p).toString();return d?`?${d}`:""}function O(){return btoa(Date.now().toString()).slice(-10,-2).toUpperCase()}function s(t){for(let e in t)if(!t[e])throw Error(`Please provide the required parameter: ${e}.`)}function D(t){let{apiKey:e,onError:o}=t;return C(v,{apiKey:e,onError:o}),t}async function r(t,e=!0){let o={statusCode:null,data:null,error:Error()},{apiKey:a,onError:u}=$(v)||{};try{if(e&&!a)return o.error=G("Please provide your Lemon Squeezy API key. Create a new API key: https://app.lemonsqueezy.com/settings/api","Missing API key"),u?.(o.error),o;let{path:p,method:d="GET",query:y,body:h}=t,m={method:d},b=new URL(`${I}${p}`);for(let P in y)b.searchParams.append(P,y[P]);m.headers=new Headers,m.headers.set("Accept","application/vnd.api+json"),m.headers.set("Content-Type","application/vnd.api+json"),e&&m.headers.set("Authorization",`Bearer ${a}`),["PATCH","POST"].includes(d)&&(m.body=h?JSON.stringify(h):null);let f=await fetch(b.href,m),g=await f.json(),l=f.ok,L=f.status;if(l)Object.assign(o,{statusCode:L,data:g,error:null});else{let{errors:P,error:S,message:x}=g,R=P||S||x||"unknown cause";Object.assign(o,{statusCode:L,data:g,error:G(f.statusText,R)})}}catch(p){Object.assign(o,{error:p})}return o.error&&u?.(o.error),o}function G(t,e="unknown"){let o=new Error(t);return o.name="Lemon Squeezy Error",o.cause=e,o}function K(){return r({path:"/v1/users/me"})}function w(t,e={}){return s({storeId:t}),r({path:`/v1/stores/${t}${i(e.include)}`})}function Q(t={}){return r({path:`/v1/stores${n(t)}`})}function q(t,e){return s({storeId:t}),r({path:"/v1/customers",method:"POST",body:{data:{type:"customers",attributes:e,relationships:{store:{data:{type:"stores",id:t.toString()}}}}}})}function W(t,e){return s({customerId:t}),r({path:`/v1/customers/${t}`,method:"PATCH",body:{data:{type:"customers",id:t.toString(),attributes:e}}})}function A(t){return s({customerId:t}),r({path:`/v1/customers/${t}`,method:"PATCH",body:{data:{type:"customers",id:t.toString(),attributes:{status:"archived"}}}})}function E(t,e={}){return s({customerId:t}),r({path:`/v1/customers/${t}${i(e.include)}`})}function F(t={}){return r({path:`/v1/customers${n(t)}`})}function V(t,e={}){return s({productId:t}),r({path:`/v1/products/${t}${i(e.include)}`})}function N(t={}){return r({path:`/v1/products${n(t)}`})}function _(t,e={}){return s({variantId:t}),r({path:`/v1/variants/${t}${i(e.include)}`})}function j(t={}){return r({path:`/v1/variants${n(t)}`})}function H(t,e={}){return s({priceId:t}),r({path:`/v1/prices/${t}${i(e.include)}`})}function z(t={}){return r({path:`/v1/prices${n(t)}`})}function M(t,e={}){return s({fileId:t}),r({path:`/v1/files/${t}${i(e.include)}`})}function B(t={}){return r({path:`/v1/files${n(t)}`})}function J(t,e={}){return s({orderId:t}),r({path:`/v1/orders/${t}${i(e.include)}`})}function Y(t={}){return r({path:`/v1/orders${n(t)}`})}function Z(t,e={}){s({orderId:t});let o=c(e),a=new URLSearchParams(o).toString(),u=a?`?${a}`:"";return r({path:`/v1/orders/${t}/generate-invoice${u}`,method:"POST"})}function X(t,e){s({orderId:t,amount:e});let o={amount:e};return r({path:`/v1/orders/${t}/refund`,method:"POST",body:{data:{type:"orders",id:t.toString(),attributes:c(o)}}})}function tt(t,e={}){return s({orderItemId:t}),r({path:`/v1/order-items/${t}${i(e.include)}`})}function et(t={}){return r({path:`/v1/order-items${n(t)}`})}function rt(t,e={}){return s({subscriptionId:t}),r({path:`/v1/subscriptions/${t}${i(e.include)}`})}function st(t,e){s({subscriptionId:t});let{variantId:o,cancelled:a,billingAnchor:u,invoiceImmediately:p,disableProrations:d,pause:y,trialEndsAt:h}=e,m=c({variantId:o,cancelled:a,billingAnchor:u,invoiceImmediately:p,disableProrations:d,pause:y,trialEndsAt:h});return r({path:`/v1/subscriptions/${t}`,method:"PATCH",body:{data:{type:"subscriptions",id:t.toString(),attributes:m}}})}function ot(t){return s({subscriptionId:t}),r({path:`/v1/subscriptions/${t}`,method:"DELETE"})}function it(t={}){return r({path:`/v1/subscriptions${n(t)}`})}function nt(t,e={}){return s({subscriptionInvoiceId:t}),r({path:`/v1/subscription-invoices/${t}${i(e.include)}`})}function at(t={}){return r({path:`/v1/subscription-invoices${n(t)}`})}function ct(t,e={}){s({subscriptionInvoiceId:t});let o=c(e),a=new URLSearchParams(o).toString(),u=a?`?${a}`:"";return r({path:`/v1/subscription-invoices/${t}/generate-invoice${u}`,method:"POST"})}function ut(t,e){s({subscriptionInvoiceId:t,amount:e});let o={amount:e};return r({path:`/v1/subscription-invoices/${t}/refund`,method:"POST",body:{data:{type:"subscription-invoices",id:t.toString(),attributes:c(o)}}})}function pt(t,e={}){return s({subscriptionItemId:t}),r({path:`/v1/subscription-items/${t}${i(e.include)}`})}function mt(t){return s({subscriptionItemId:t}),r({path:`/v1/subscription-items/${t}/current-usage`})}function dt(t={}){return r({path:`/v1/subscription-items${n(t)}`})}function yt(t,e){return ht(t,e)}async function ht(t,e){s({subscriptionItemId:t});let o;if(typeof e=="number")o={quantity:e};else{let{quantity:a,invoiceImmediately:u=!1,disableProrations:p=!1}=e;o=c({quantity:a,invoiceImmediately:u,disableProrations:p})}return r({path:`/v1/subscription-items/${t}`,method:"PATCH",body:{data:{type:"subscription-items",id:t.toString(),attributes:o}}})}function ft(t,e={}){return s({usageRecordId:t}),r({path:`/v1/usage-records/${t}${i(e.include)}`})}function gt(t={}){return r({path:`/v1/usage-records${n(t)}`})}function bt(t){let{quantity:e,action:o="increment",subscriptionItemId:a}=t;return s({quantity:e,subscriptionItemId:a}),r({path:"/v1/usage-records",method:"POST",body:{data:{type:"usage-records",attributes:{quantity:e,action:o},relationships:{"subscription-item":{data:{type:"subscription-items",id:a.toString()}}}}}})}function Pt(t){let{storeId:e,variantIds:o,name:a,amount:u,amountType:p="fixed",code:d=O(),isLimitedToProducts:y=!1,isLimitedRedemptions:h=!1,maxRedemptions:m=0,startsAt:b=null,expiresAt:f=null,duration:g="once",durationInMonths:l=1,testMode:L}=t;s({storeId:e,name:a,code:d,amount:u});let P=c({name:a,amount:u,amountType:p,code:d,isLimitedRedemptions:h,isLimitedToProducts:y,maxRedemptions:m,startsAt:b,expiresAt:f,duration:g,durationInMonths:l,testMode:L}),S={store:{data:{type:"stores",id:e.toString()}}};return o&&o.length>0&&(S.variants={data:o.map(x=>({type:"variants",id:x.toString()}))}),r({path:"/v1/discounts",method:"POST",body:{data:{type:"discounts",attributes:P,relationships:S}}})}function Lt(t={}){return r({path:`/v1/discounts${n(t)}`})}function St(t,e={}){return s({discountId:t}),r({path:`/v1/discounts/${t}${i(e.include)}`})}function vt(t){return s({discountId:t}),r({path:`/v1/discounts/${t}`,method:"DELETE"})}function lt(t,e={}){return s({discountRedemptionId:t}),r({path:`/v1/discount-redemptions/${t}${i(e.include)}`})}function xt(t={}){return r({path:`/v1/discount-redemptions${n(t)}`})}function kt(t,e={}){return s({licenseKeyId:t}),r({path:`/v1/license-keys/${t}${i(e.include)}`})}function $t(t={}){return r({path:`/v1/license-keys${n(t)}`})}function Ct(t,e){s({licenseKeyId:t});let{activationLimit:o,disabled:a=!1,expiresAt:u}=e,p=c({activationLimit:o,disabled:a,expiresAt:u});return r({path:`/v1/license-keys/${t}`,method:"PATCH",body:{data:{type:"license-keys",id:t.toString(),attributes:p}}})}function It(t,e={}){return s({licenseKeyInstanceId:t}),r({path:`/v1/license-key-instances/${t}${i(e.include)}`})}function Tt(t={}){return r({path:`/v1/license-key-instances${n(t)}`})}function Ot(t,e,o={}){s({storeId:t,variantId:e});let{customPrice:a,productOptions:u,checkoutOptions:p,checkoutData:d,expiresAt:y,preview:h,testMode:m}=o,b={store:{data:{type:"stores",id:t.toString()}},variant:{data:{type:"variants",id:e.toString()}}},f={customPrice:a,expiresAt:y,preview:h,testMode:m,productOptions:u,checkoutOptions:p,checkoutData:{...d,variantQuantities:d?.variantQuantities?.map(g=>c(g))}};return r({path:"/v1/checkouts",method:"POST",body:{data:{type:"checkouts",attributes:c(f),relationships:b}}})}function Gt(t,e={}){return s({checkoutId:t}),r({path:`/v1/checkouts/${t}${i(e.include)}`})}function Rt(t={}){return r({path:`/v1/checkouts${n(t)}`})}function Ut(t,e){s({storeId:t});let{url:o,events:a,secret:u,testMode:p}=e;return r({path:"/v1/webhooks",method:"POST",body:{data:{type:"webhooks",attributes:c({url:o,events:a,secret:u,testMode:p}),relationships:{store:{data:{type:"stores",id:t.toString()}}}}}})}function Dt(t,e={}){return s({webhookId:t}),r({path:`/v1/webhooks/${t}${i(e.include)}`})}function Kt(t,e){s({webhookId:t});let{url:o,events:a,secret:u}=e;return r({path:`/v1/webhooks/${t}`,method:"PATCH",body:{data:{id:t.toString(),type:"webhooks",attributes:c({url:o,events:a,secret:u})}}})}function wt(t){return s({webhookId:t}),r({path:`/v1/webhooks/${t}`,method:"DELETE"})}function Qt(t={}){return r({path:`/v1/webhooks${n(t)}`})}async function qt(t,e){return s({licenseKey:t,instanceName:e}),r({path:"/v1/licenses/activate",method:"POST",body:c({licenseKey:t,instanceName:e})},!1)}async function Wt(t,e){return s({licenseKey:t}),r({path:"/v1/licenses/validate",method:"POST",body:c({licenseKey:t,instanceId:e})},!1)}async function At(t,e){return s({licenseKey:t,instanceId:e}),r({path:"/v1/licenses/deactivate",method:"POST",body:c({licenseKey:t,instanceId:e})},!1)}export{qt as activateLicense,A as archiveCustomer,ot as cancelSubscription,Ot as createCheckout,q as createCustomer,Pt as createDiscount,bt as createUsageRecord,Ut as createWebhook,At as deactivateLicense,vt as deleteDiscount,wt as deleteWebhook,Z as generateOrderInvoice,ct as generateSubscriptionInvoice,K as getAuthenticatedUser,Gt as getCheckout,E as getCustomer,St as getDiscount,lt as getDiscountRedemption,M as getFile,kt as getLicenseKey,It as getLicenseKeyInstance,J as getOrder,tt as getOrderItem,H as getPrice,V as getProduct,w as getStore,rt as getSubscription,nt as getSubscriptionInvoice,pt as getSubscriptionItem,mt as getSubscriptionItemCurrentUsage,ft as getUsageRecord,_ as getVariant,Dt as getWebhook,X as issueOrderRefund,ut as issueSubscriptionInvoiceRefund,D as lemonSqueezySetup,Rt as listCheckouts,F as listCustomers,xt as listDiscountRedemptions,Lt as listDiscounts,B as listFiles,Tt as listLicenseKeyInstances,$t as listLicenseKeys,et as listOrderItems,Y as listOrders,z as listPrices,N as listProducts,Q as listStores,at as listSubscriptionInvoices,dt as listSubscriptionItems,it as listSubscriptions,gt as listUsageRecords,j as listVariants,Qt as listWebhooks,W as updateCustomer,Ct as updateLicenseKey,st as updateSubscription,yt as updateSubscriptionItem,Kt as updateWebhook,Wt as validateLicense};
