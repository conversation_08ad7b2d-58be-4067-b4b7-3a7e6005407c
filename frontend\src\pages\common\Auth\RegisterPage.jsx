// src/pages/common/Auth/RegisterPage.jsx
import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../context/AuthContext';
import { <PERSON><PERSON>ser, FiMail, <PERSON>Lock, <PERSON>Eye, <PERSON><PERSON>yeOff, <PERSON><PERSON><PERSON><PERSON>, FiShield } from 'react-icons/fi';
import { <PERSON>a<PERSON>oogle, FaGithub } from 'react-icons/fa';

// Helper function to check password strength (can be made more complex)
const getPasswordStrength = (password) => {
  let score = 0;
  if (!password) return 0;
  if (password.length >= 8) score++;
  if (password.length >= 12) score++;
  if (/[A-Z]/.test(password)) score++;
  if (/[0-9]/.test(password)) score++;
  if (/[^A-Za-z0-9]/.test(password)) score++;
  return score;
};

const RegisterPageContent = ({ onSwitchToLogin, onSuccess }) => {
  const { register, isLoading, setVerificationEmail } = useAuth();
  
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [isAdmin, setIsAdmin] = useState(false);
  const [canRegisterAdmin, setCanRegisterAdmin] = useState(false);
  const [checkingAdminStatus, setCheckingAdminStatus] = useState(true);

  // UX: Real-time feedback for password strength
  useEffect(() => {
    setPasswordStrength(getPasswordStrength(password));
  }, [password]);

  // Check admin registration status on component mount
  useEffect(() => {
    const checkAdminRegistrationStatus = async () => {
      try {
        const API_BASE_URL = import.meta.env.VITE_NODE_BACKEND_URL || 'http://localhost:3001';
        const response = await fetch(`${API_BASE_URL}/api/admin/registration-status`);
        const data = await response.json();

        if (response.ok) {
          setCanRegisterAdmin(data.canRegisterAdmin);
        }
      } catch (error) {
        console.error('Error checking admin registration status:', error);
        setCanRegisterAdmin(false);
      } finally {
        setCheckingAdminStatus(false);
      }
    };

    checkAdminRegistrationStatus();
  }, []);

  const handleRegister = async (e) => {
    e.preventDefault();
    setError('');
    if (!name || !email || !password) {
      setError('Please fill in all fields.');
      return;
    }
    if (password.length < 8) {
        setError('Password must be at least 8 characters long.');
        return;
    }

    const result = await register(name, email, password, isAdmin);
    
    if (result.success) {
      if (result.email && setVerificationEmail) {
        setVerificationEmail(result.email);
      }
      if (onSuccess) {
        onSuccess(result.email);
      }
    } else {
      setError(result.error || 'Registration failed. Please try again.');
    }
  };

  const strengthColors = ['bg-slate-700', 'bg-red-500', 'bg-red-500', 'bg-yellow-500', 'bg-yellow-500', 'bg-green-500'];
  const strengthLabels = ['Too weak', 'Weak', 'Weak', 'Medium', 'Medium', 'Strong'];

  const SocialButton = ({ icon, text }) => (
    <button type="button" className="flex-1 flex items-center justify-center py-3 px-4 bg-slate-800/70 border border-slate-700 rounded-lg text-slate-300 hover:bg-slate-700/80 hover:border-slate-600 transition-all duration-200">
      {icon}
      <span className="ml-3 font-medium">{text}</span>
    </button>
  );

  return (
    <div className="flex flex-col space-y-6">
      <div>
        <div className="flex items-center space-x-4">
          <SocialButton icon={<FaGoogle className="w-5 h-5 text-white" />} text="Google" />
          <SocialButton icon={<FaGithub className="w-5 h-5 text-white" />} text="GitHub" />
        </div>
      </div>

      <div className="flex items-center">
        <div className="flex-grow border-t border-slate-700"></div>
        <span className="flex-shrink mx-4 text-xs text-slate-500 uppercase">Or create an account</span>
        <div className="flex-grow border-t border-slate-700"></div>
      </div>

      <form onSubmit={handleRegister} className="space-y-5">
        {/* Name Input */}
        <div className="relative group">
          <FiUser className="absolute top-1/2 -translate-y-1/2 left-4 text-slate-400 group-focus-within:text-purple-400" />
          <input type="text" placeholder="Your full name" value={name} onChange={(e) => setName(e.target.value)} required className="w-full pl-12 pr-4 py-3 bg-slate-800/70 border border-slate-700 rounded-lg text-slate-100 placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500" />
        </div>

        {/* Email Input */}
        <div className="relative group">
          <FiMail className="absolute top-1/2 -translate-y-1/2 left-4 text-slate-400 group-focus-within:text-purple-400" />
          <input type="email" placeholder="<EMAIL>" value={email} onChange={(e) => setEmail(e.target.value)} required className="w-full pl-12 pr-4 py-3 bg-slate-800/70 border border-slate-700 rounded-lg text-slate-100 placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500" />
        </div>
        
        {/* Password Input */}
        <div className="relative group">
          <FiLock className="absolute top-1/2 -translate-y-1/2 left-4 text-slate-400 group-focus-within:text-purple-400" />
          <input type={showPassword ? 'text' : 'password'} placeholder="Create a password" value={password} onChange={(e) => setPassword(e.target.value)} required className="w-full pl-12 pr-12 py-3 bg-slate-800/70 border border-slate-700 rounded-lg text-slate-100 placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500" />
          <button type="button" onClick={() => setShowPassword(!showPassword)} className="absolute top-1/2 -translate-y-1/2 right-4 text-slate-400 hover:text-slate-200">
            {showPassword ? <FiEyeOff /> : <FiEye />}
          </button>
        </div>

        {/* Password Strength Meter */}
        {password.length > 0 && (
          <div className="flex items-center space-x-3">
            <div className="w-full bg-slate-700 rounded-full h-1.5">
              <div
                className={`h-1.5 rounded-full ${strengthColors[passwordStrength]} transition-all duration-300`}
                style={{ width: `${(passwordStrength / 5) * 100}%` }}
              ></div>
            </div>
            <span className="text-xs text-slate-400 w-16 text-right">{strengthLabels[passwordStrength]}</span>
          </div>
        )}

        {/* Admin Registration Checkbox */}
        {!checkingAdminStatus && canRegisterAdmin && (
          <div className="flex items-center space-x-3 p-4 bg-slate-800/50 border border-slate-700 rounded-lg">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="adminCheckbox"
                checked={isAdmin}
                onChange={(e) => setIsAdmin(e.target.checked)}
                className="w-4 h-4 text-purple-600 bg-slate-700 border-slate-600 rounded focus:ring-purple-500 focus:ring-2"
              />
              <label htmlFor="adminCheckbox" className="ml-3 flex items-center text-sm text-slate-300 cursor-pointer">
                <FiShield className="w-4 h-4 mr-2 text-purple-400" />
                Register as Administrator
              </label>
            </div>
            <div className="flex-1">
              <p className="text-xs text-slate-500">
                This option is only available for the first admin registration. Once an admin is registered, this option will be disabled.
              </p>
            </div>
          </div>
        )}

        {error && <p className="text-xs text-red-400 text-center">{error}</p>}

        <button type="submit" disabled={isLoading} className="w-full flex items-center justify-center font-semibold text-white bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg py-3 px-6 transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-purple-500/30 disabled:opacity-70 disabled:cursor-not-allowed transform hover:-translate-y-0.5">
          {isLoading ? <FiLoader className="w-6 h-6 animate-spin" /> : 'Create Account'}
        </button>
      </form>
      
      <p className="text-center text-sm text-slate-400">
        Already have an account?{' '}
        <button type="button" onClick={onSwitchToLogin} className="font-medium text-purple-400 hover:text-purple-300 hover:underline">
          Sign In
        </button>
      </p>
    </div>
  );
};

export default RegisterPageContent;