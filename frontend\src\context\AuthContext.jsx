// src/context/AuthContext.jsx
import React, { createContext, useContext, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import axios from 'axios'; // Using axios for cleaner API calls
import {
  loginUser,
  registerUser,
  verifyUserEmail,
  resendVerificationCode,
  logout as logoutAction,
  incrementUserUploadCountThunk,
  fetchCurrentUser,
  updateUserSubscription,
  selectCurrentUser,
  selectAuthToken,
  selectIsAuthenticated,
  selectAuthIsLoading,
  selectAuthError,
  setEmailForVerificationState,
  selectEmailForVerification,
  clearError as clearAuthErrorAction,
} from '../store/features/auth/authSlice';

const AuthContext = createContext(null);

export const useAuth = () => useContext(AuthContext);

// Define API base URL from environment variables
const API_BASE_URL = import.meta.env.VITE_NODE_BACKEND_URL || 'http://localhost:3001';

export const AuthProvider = ({ children }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const currentUser = useSelector(selectCurrentUser);
  const token = useSelector(selectAuthToken);
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const isLoading = useSelector(selectAuthIsLoading);
  const authError = useSelector(selectAuthError);
  const emailForVerification = useSelector(selectEmailForVerification);

  useEffect(() => {
    // If a token exists but no user data, try fetching the user to verify the token
    if (token && !currentUser && !isLoading) {
      dispatch(fetchCurrentUser());
    }
  }, [token, currentUser, isLoading, dispatch]);


  const login = async (email, password) => {
    const resultAction = await dispatch(loginUser({ email, password }));
    if (loginUser.fulfilled.match(resultAction)) {
      navigate('/'); // Navigate to main page on successful user login
      return { success: true, user: resultAction.payload.user };
    } else {
      return { success: false, error: resultAction.payload || 'Login failed' };
    }
  };



  const register = async (name, email, password, isAdmin = false) => {
    const resultAction = await dispatch(registerUser({ name, email, password, isAdmin }));
    if (registerUser.fulfilled.match(resultAction)) {
      return { success: true, message: resultAction.payload.message, email: resultAction.payload.email };
    } else {
      return { success: false, error: resultAction.payload || 'Registration failed' };
    }
  };

  const verifyEmailContext = async (emailToVerify, verificationCode) => {
    const email = emailToVerify || emailForVerification;
    if (!email) {
        return { success: false, error: "Email for verification not found." };
    }
    const resultAction = await dispatch(verifyUserEmail({ email, verificationCode }));
    if (verifyUserEmail.fulfilled.match(resultAction)) {
      navigate('/');
      return { success: true, message: resultAction.payload.message, user: resultAction.payload.user };
    } else {
      return { success: false, error: resultAction.payload || 'Verification failed' };
    }
  };

  const resendVerificationCodeContext = async (emailToResend) => {
     const email = emailToResend || emailForVerification;
     if (!email) {
        return { success: false, error: "Email for resending code not found." };
    }
    const resultAction = await dispatch(resendVerificationCode(email));
    if (resendVerificationCode.fulfilled.match(resultAction)) {
      return { success: true, message: resultAction.payload.message };
    } else {
      return { success: false, error: resultAction.payload || 'Failed to resend code' };
    }
  };

  const logout = () => {
    dispatch(logoutAction());
    // On logout, always navigate to the public home page.
    navigate('/');
  };

  const incrementUploadCount = async () => {
    const resultAction = await dispatch(incrementUserUploadCountThunk());
    if (incrementUserUploadCountThunk.fulfilled.match(resultAction)) {
      return { success: true, subscription: resultAction.payload };
    } else {
      return { success: false, error: resultAction.payload || 'Failed to increment count' };
    }
  };

  const refreshUser = async () => {
    const resultAction = await dispatch(fetchCurrentUser());
    if (fetchCurrentUser.fulfilled.match(resultAction)) {
      return { success: true, user: resultAction.payload };
    } else {
      return { success: false, error: resultAction.payload || 'Failed to refresh user data' };
    }
  };

  const incrementMessageCount = async () => {
    try {
        const response = await axios.post(
            `${API_BASE_URL}/api/users/me/increment-message-count`,
            {},
            { headers: { Authorization: `Bearer ${token}` } }
        );
        return { success: true, subscription: response.data.subscription };
    } catch (error) {
        console.error("Failed to increment message count:", error);
        return { success: false, error: error.message };
    }
  };
  
  const updateSubscriptionInContext = (newSubscriptionData) => {
    dispatch(updateUserSubscription(newSubscriptionData));
  };

  const setVerificationEmail = (email) => {
    dispatch(setEmailForVerificationState(email));
  };

  const clearAuthError = () => {
    dispatch(clearAuthErrorAction());
  };

  // The value provided to the context consumers
  const value = {
    currentUser,
    token,
    isAuthenticated,
    isLoading,
    authError,
    emailForVerification,
    login,
    register,
    logout,
    verifyEmail: verifyEmailContext,
    resendVerificationCode: resendVerificationCodeContext,
    incrementUploadCount,
    incrementMessageCount,
    refreshUser,

    updateSubscriptionInContext,
    setVerificationEmail,
    clearAuthError,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export default AuthProvider;