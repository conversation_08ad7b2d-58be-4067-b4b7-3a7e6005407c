"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubscriptionCancellationOptions = void 0;
class SubscriptionCancellationOptions {
    constructor(options) {
        var _a, _b;
        this.effectiveFrom = (_a = options === null || options === void 0 ? void 0 : options.effective_from) !== null && _a !== void 0 ? _a : null;
        this.hasPastDueTransaction = (_b = options === null || options === void 0 ? void 0 : options.has_past_due_transaction) !== null && _b !== void 0 ? _b : null;
    }
}
exports.SubscriptionCancellationOptions = SubscriptionCancellationOptions;
