import { SimulationScenarioConfig } from '../index.js';
export class Simulation {
    constructor(simulationResponse) {
        var _a, _b;
        this.id = simulationResponse.id;
        this.status = simulationResponse.status;
        this.notificationSettingId = simulationResponse.notification_setting_id;
        this.name = simulationResponse.name;
        this.type = simulationResponse.type;
        this.payload = (_a = simulationResponse.payload) !== null && _a !== void 0 ? _a : null;
        this.lastRunAt = (_b = simulationResponse.last_run_at) !== null && _b !== void 0 ? _b : null;
        this.createdAt = simulationResponse.created_at;
        this.updatedAt = simulationResponse.updated_at;
        this.config = simulationResponse.config ? new SimulationScenarioConfig(simulationResponse.config) : null;
    }
}
