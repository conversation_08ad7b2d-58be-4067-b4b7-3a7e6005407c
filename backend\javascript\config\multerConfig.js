// File Path: /backend/javascrupt/config/multerConfig.js
import multer from 'multer';
import fs from 'node:fs/promises';
import { UPLOAD_DIR } from './appConfig.js';

const storage = multer.diskStorage({
    destination: async (req, file, cb) => {
        try {
            await fs.mkdir(UPLOAD_DIR, { recursive: true });
            cb(null, UPLOAD_DIR);
        } catch (error) {
            console.error("Multer: Error creating upload directory:", error);
            cb(error);
        }
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const safeOriginalName = file.originalname.replace(/[^a-zA-Z0-9_.-]/g, '_');
        cb(null, uniqueSuffix + '-' + safeOriginalName);
    }
});

const upload = multer({
    storage: storage,
    limits: { fileSize: 25 * 1024 * 1024 }, // 25MB
    fileFilter: (req, file, cb) => {
        if (file.mimetype === "application/pdf") {
            cb(null, true);
        } else {
            cb(new Error("File type not allowed. Only PDF files are accepted."), false);
        }
    }
});

export default upload;