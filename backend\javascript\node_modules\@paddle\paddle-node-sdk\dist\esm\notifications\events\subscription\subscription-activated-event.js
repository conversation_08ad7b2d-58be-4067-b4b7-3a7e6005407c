import { Event } from '../../../entities/events/event.js';
import { EventName } from '../../helpers/index.js';
import { SubscriptionNotification } from '../../entities/index.js';
export class SubscriptionActivatedEvent extends Event {
    constructor(response) {
        super(response);
        this.eventType = EventName.SubscriptionActivated;
        this.data = new SubscriptionNotification(response.data);
    }
}
