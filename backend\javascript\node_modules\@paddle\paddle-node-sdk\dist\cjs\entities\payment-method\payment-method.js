"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentMethod = void 0;
const index_js_1 = require("../shared/index.js");
class PaymentMethod {
    constructor(paymentMethodResponse) {
        this.id = paymentMethodResponse.id;
        this.customerId = paymentMethodResponse.customer_id;
        this.addressId = paymentMethodResponse.address_id;
        this.type = paymentMethodResponse.type;
        this.card = paymentMethodResponse.card ? new index_js_1.PaymentCard(paymentMethodResponse.card) : null;
        this.paypal = paymentMethodResponse.paypal ? new index_js_1.PayPal(paymentMethodResponse.paypal) : null;
        this.origin = paymentMethodResponse.origin;
        this.savedAt = paymentMethodResponse.saved_at;
        this.updatedAt = paymentMethodResponse.updated_at;
    }
}
exports.PaymentMethod = PaymentMethod;
