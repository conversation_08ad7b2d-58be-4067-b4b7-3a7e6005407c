// src/routes/Tools/Business/businessQARoutes.js

import express from 'express';
import { generateBusinessQA } from '../../../controllers/tools/BusinessPlan/businessQAController.js';
import { protect } from '../../../middleware/authMiddleware.js';

const router = express.Router();

// @desc    Generate a business Q&A answer from user question
// @route   POST /api/business-qa/generate
// @access  Private (requires authentication)
router.post('/generate', protect, generateBusinessQA);

export default router;