export class TransactionPayoutTotalsNotification {
    constructor(transactionPayoutTotals) {
        this.subtotal = transactionPayoutTotals.subtotal;
        this.discount = transactionPayoutTotals.discount;
        this.tax = transactionPayoutTotals.tax;
        this.total = transactionPayoutTotals.total;
        this.credit = transactionPayoutTotals.credit;
        this.creditToBalance = transactionPayoutTotals.credit_to_balance;
        this.balance = transactionPayoutTotals.balance;
        this.grandTotal = transactionPayoutTotals.grand_total;
        this.fee = transactionPayoutTotals.fee;
        this.earnings = transactionPayoutTotals.earnings;
        this.currencyCode = transactionPayoutTotals.currency_code;
        this.exchangeRate = transactionPayoutTotals.exchange_rate;
        this.feeRate = transactionPayoutTotals.fee_rate;
    }
}
