"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpanNames = void 0;
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
// Contains span names produced by instrumentation
var SpanNames;
(function (SpanNames) {
    SpanNames["QUERY_PREFIX"] = "pg.query";
    SpanNames["CONNECT"] = "pg.connect";
    SpanNames["POOL_CONNECT"] = "pg-pool.connect";
})(SpanNames = exports.SpanNames || (exports.SpanNames = {}));
//# sourceMappingURL=SpanNames.js.map