// node_gemini_summarizer/controller/Tools/pdf/chatController.js
import { 
    getPdfTextFromPython, 
    chatWithPdfContent, 
    setSessionPdfContent, 
    getSessionPdfContent 
} from '../../../services/geminiChatService.js';
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';

const FREE_TIER_UPLOAD_LIMIT = 5;
const PRO_TIER_UPLOAD_LIMIT = 25;
const FREE_TIER_MESSAGE_LIMIT = 100;
const PRO_TIER_MESSAGE_LIMIT = 300;

// Session storage for message counts
const sessionMessageCounts = new Map();

// Helper function to get user subscription details
const getMockUserSubscription = (req) => {
    return {
        _id: req.user?._id || 'mockUserId',
        subscription: req.user?.subscription || {
            planName: 'Starter',
            freeTierUploadCount: 0,
            proTierUploadCount: 0,
            freeTierMessageCount: 0,
            proTierMessageCount: 0,
        }
    };
};

// Controller for uploading PDF and starting chat session
export const uploadPdfAndInitiateSession = async (req) => {
    const uploadedFile = req.file;
    const mockUser = getMockUserSubscription(req);

    if (!uploadedFile) {
        return {
            error: "No PDF file uploaded. Please upload a PDF file to start chatting.",
            statusCode: 400
        };
    }

    const currentUploadCount = mockUser.subscription.planName === 'Starter' 
        ? mockUser.subscription.freeTierUploadCount 
        : mockUser.subscription.proTierUploadCount;
    
    const uploadLimit = mockUser.subscription.planName === 'Starter' 
        ? FREE_TIER_UPLOAD_LIMIT 
        : PRO_TIER_UPLOAD_LIMIT;
    
    if (currentUploadCount >= uploadLimit) {
        return {
            error: `Upload limit reached for your ${mockUser.subscription.planName} plan.`,
            message: `You have reached your upload limit (${uploadLimit}) for the ${mockUser.subscription.planName} plan.`,
            limitReached: true,
            upgradePath: '/price',
            sessionUsage: {
                messagesSent: 0,
                messageLimit: mockUser.subscription.planName === 'Pro' ? PRO_TIER_MESSAGE_LIMIT : FREE_TIER_MESSAGE_LIMIT,
                planName: mockUser.subscription.planName,
            },
            statusCode: 403
        };
    }
    
    // --- ADDED: Robust error handling around the Python service call ---
    try {
        const extractedText = await getPdfTextFromPython(uploadedFile.originalname, uploadedFile.buffer);

        if (!extractedText || extractedText.trim().length === 0) {
            return {
                sessionId: null,
                error: "The PDF has no extractable text content or seems to be empty.",
                statusCode: 400
            };
        }

        const sessionId = uuidv4();
        setSessionPdfContent(sessionId, uploadedFile.originalname, extractedText);
        sessionMessageCounts.set(sessionId, 0);

        const initialPrompt = "You are a helpful assistant specialized in analyzing PDF documents. Provide a brief, concise welcome message. Ask the user what kind of questions they have about this document.";
        const initialAiResponse = await chatWithPdfContent(extractedText, initialPrompt);

        const messageLimit = mockUser.subscription.planName === 'Pro' 
            ? PRO_TIER_MESSAGE_LIMIT 
            : FREE_TIER_MESSAGE_LIMIT;

        return {
            sessionId,
            filename: uploadedFile.originalname,
            initialMessage: {
                id: `ai-init-${Date.now()}`,
                text: initialAiResponse,
                fullResponse: initialAiResponse,
                sender: 'ai',
                isLoading: false,
                timestamp: Date.now(),
            },
            sessionUsage: { messagesSent: 0, messageLimit, planName: mockUser.subscription.planName },
            message: "PDF uploaded and chat session initiated successfully.",
            statusCode: 200
        };
    } catch (error) {
        // Log the detailed technical error for your own debugging
        console.error(`[Node ChatCtrl] Error processing PDF "${uploadedFile.originalname}":`, error.response?.data || error.message);

        let userMessage = 'An unexpected error occurred during PDF processing. Please try again.';
        let statusCode = 500;

        if (error.response) {
            // The Python text extraction service responded with an error (4xx, 5xx).
            statusCode = error.response.status;
            const details = error.response.data?.details || error.response.data?.error || "No details provided.";

            if (statusCode >= 500) {
                userMessage = 'Internal Server Error: Our document processing service is currently experiencing issues. Please try again later.';
            } else if (statusCode >= 400) {
                userMessage = `Invalid Document: There was a problem with the uploaded file. Details: ${details}`;
            }
        } else if (error.request) {
            // The request was made but no response was received (network error).
            statusCode = 503; // Service Unavailable
            if (error.code === 'ECONNABORTED') {
                userMessage = 'Request Timeout: Processing the PDF took too long. This can happen with very large or complex files.';
            } else {
                userMessage = 'Network Error: Unable to connect to the document processing service. It may be temporarily down.';
            }
        }
        
        // Return a structured, user-friendly error object
        return { error: userMessage, statusCode };
    }
};

// Controller for handling chat messages
export const sessionMessage = async (req) => {
    const { sessionId, message, messages: chatHistory } = req.body;
    const userQuery = message?.text;
    const mockUser = getMockUserSubscription(req);

    if (!sessionId || !userQuery) {
        throw new Error("Session ID and user message are required.");
    }

    const sessionData = getSessionPdfContent(sessionId);
    const currentMessageCount = sessionMessageCounts.get(sessionId) ?? 0;
    const messageLimit = mockUser.subscription.planName === 'Pro' 
        ? PRO_TIER_MESSAGE_LIMIT 
        : FREE_TIER_MESSAGE_LIMIT;

    if (!sessionData) {
        return {
            error: "Chat session not found or expired. Please start a new chat.",
            statusCode: 404
        };
    }

    if (currentMessageCount >= messageLimit) {
        return {
            error: `Message limit reached for your ${mockUser.subscription.planName} plan.`,
            message: `You have reached your message limit (${messageLimit}) for this chat session.`,
            limitReached: true,
            upgradePath: '/price',
            sessionUsage: {
                messagesSent: currentMessageCount,
                messageLimit,
                planName: mockUser.subscription.planName,
            },
            statusCode: 403
        };
    }

    if (req.user?._id) {
        try {
            await axios.post(
                `${process.env.NODE_API_URL || 'http://localhost:3001'}/api/users/me/increment-message-count`,
                {},
                { headers: { 'Authorization': `Bearer ${req.headers.authorization?.split(' ')[1]}` } }
            );
        } catch (error) {
            console.error("Failed to increment message count:", error);
        }
    }

    const newMessageCount = currentMessageCount + 1;
    sessionMessageCounts.set(sessionId, newMessageCount);

    const pdfContent = sessionData.extractedText;
    const aiResponseText = await chatWithPdfContent(pdfContent, userQuery, chatHistory);

    return {
        id: `ai-resp-${Date.now()}`,
        text: aiResponseText,
        fullResponse: aiResponseText,
        sender: 'ai',
        isLoading: false,
        timestamp: Date.now(),
        sessionUsage: {
            messagesSent: newMessageCount,
            messageLimit,
            planName: mockUser.subscription.planName,
        },
        statusCode: 200
    };
};

// Controller for direct PDF Q&A without session
export const pdfQa = async (req) => {
    const { userQuery, filename: existingFilename } = req.body;
    const uploadedFile = req.file;

    if (!userQuery) {
        throw new Error("User query is required.");
    }

    let pdfText;
    if (uploadedFile) {
        pdfText = await getPdfTextFromPython(uploadedFile.originalname, uploadedFile.buffer);
    } else if (existingFilename) {
        pdfText = await getPdfTextFromPython(existingFilename);
    } else {
        throw new Error("No PDF file uploaded and no filename provided for chat.");
    }

    if (!pdfText || pdfText.trim().length === 0) {
        return {
            error: "Could not extract readable text from the PDF. The document might be scanned or empty.",
            statusCode: 500
        };
    }

    const aiResponse = await chatWithPdfContent(pdfText, userQuery);

    return {
        response: aiResponse,
        statusCode: 200
    };
};