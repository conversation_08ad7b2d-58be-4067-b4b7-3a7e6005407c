{"version": 3, "file": "global-utils.js", "sourceRoot": "", "sources": ["../../../src/internal/global-utils.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAKH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;AAG1C,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,OAAO,EAAE,YAAY,EAAE,MAAM,UAAU,CAAC;AAExC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,MAAM,4BAA4B,GAAG,MAAM,CAAC,GAAG,CAC7C,wBAAwB,KAAK,EAAE,CAChC,CAAC;AAEF,MAAM,OAAO,GAAG,WAAyB,CAAC;AAE1C,MAAM,UAAU,cAAc,CAC5B,IAAU,EACV,QAA6B,EAC7B,IAAgB,EAChB,aAAa,GAAG,KAAK;;IAErB,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,4BAA4B,CAAC,GAAG,MAAA,OAAO,CAC1D,4BAA4B,CAC7B,mCAAI;QACH,OAAO,EAAE,OAAO;KACjB,CAAC,CAAC;IAEH,IAAI,CAAC,aAAa,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE;QAC/B,yCAAyC;QACzC,MAAM,GAAG,GAAG,IAAI,KAAK,CACnB,gEAAgE,IAAI,EAAE,CACvE,CAAC;QACF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;QACrC,OAAO,KAAK,CAAC;KACd;IAED,IAAI,GAAG,CAAC,OAAO,KAAK,OAAO,EAAE;QAC3B,0DAA0D;QAC1D,MAAM,GAAG,GAAG,IAAI,KAAK,CACnB,gDAAgD,GAAG,CAAC,OAAO,QAAQ,IAAI,8CAA8C,OAAO,EAAE,CAC/H,CAAC;QACF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;QACrC,OAAO,KAAK,CAAC;KACd;IAED,GAAG,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;IACrB,IAAI,CAAC,KAAK,CACR,+CAA+C,IAAI,KAAK,OAAO,GAAG,CACnE,CAAC;IAEF,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,UAAU,SAAS,CACvB,IAAU;;IAEV,MAAM,aAAa,GAAG,MAAA,OAAO,CAAC,4BAA4B,CAAC,0CAAE,OAAO,CAAC;IACrE,IAAI,CAAC,aAAa,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE;QAClD,OAAO;KACR;IACD,OAAO,MAAA,OAAO,CAAC,4BAA4B,CAAC,0CAAG,IAAI,CAAC,CAAC;AACvD,CAAC;AAED,MAAM,UAAU,gBAAgB,CAAC,IAAyB,EAAE,IAAgB;IAC1E,IAAI,CAAC,KAAK,CACR,kDAAkD,IAAI,KAAK,OAAO,GAAG,CACtE,CAAC;IACF,MAAM,GAAG,GAAG,OAAO,CAAC,4BAA4B,CAAC,CAAC;IAElD,IAAI,GAAG,EAAE;QACP,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC;KAClB;AACH,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MeterProvider } from '../metrics/MeterProvider';\nimport { ContextManager } from '../context/types';\nimport { DiagLogger } from '../diag/types';\nimport { _globalThis } from '../platform';\nimport { TextMapPropagator } from '../propagation/TextMapPropagator';\nimport type { TracerProvider } from '../trace/tracer_provider';\nimport { VERSION } from '../version';\nimport { isCompatible } from './semver';\n\nconst major = VERSION.split('.')[0];\nconst GLOBAL_OPENTELEMETRY_API_KEY = Symbol.for(\n  `opentelemetry.js.api.${major}`\n);\n\nconst _global = _globalThis as OTelGlobal;\n\nexport function registerGlobal<Type extends keyof OTelGlobalAPI>(\n  type: Type,\n  instance: OTelGlobalAPI[Type],\n  diag: DiagLogger,\n  allowOverride = false\n): boolean {\n  const api = (_global[GLOBAL_OPENTELEMETRY_API_KEY] = _global[\n    GLOBAL_OPENTELEMETRY_API_KEY\n  ] ?? {\n    version: VERSION,\n  });\n\n  if (!allowOverride && api[type]) {\n    // already registered an API of this type\n    const err = new Error(\n      `@opentelemetry/api: Attempted duplicate registration of API: ${type}`\n    );\n    diag.error(err.stack || err.message);\n    return false;\n  }\n\n  if (api.version !== VERSION) {\n    // All registered APIs must be of the same version exactly\n    const err = new Error(\n      `@opentelemetry/api: Registration of version v${api.version} for ${type} does not match previously registered API v${VERSION}`\n    );\n    diag.error(err.stack || err.message);\n    return false;\n  }\n\n  api[type] = instance;\n  diag.debug(\n    `@opentelemetry/api: Registered a global for ${type} v${VERSION}.`\n  );\n\n  return true;\n}\n\nexport function getGlobal<Type extends keyof OTelGlobalAPI>(\n  type: Type\n): OTelGlobalAPI[Type] | undefined {\n  const globalVersion = _global[GLOBAL_OPENTELEMETRY_API_KEY]?.version;\n  if (!globalVersion || !isCompatible(globalVersion)) {\n    return;\n  }\n  return _global[GLOBAL_OPENTELEMETRY_API_KEY]?.[type];\n}\n\nexport function unregisterGlobal(type: keyof OTelGlobalAPI, diag: DiagLogger) {\n  diag.debug(\n    `@opentelemetry/api: Unregistering a global for ${type} v${VERSION}.`\n  );\n  const api = _global[GLOBAL_OPENTELEMETRY_API_KEY];\n\n  if (api) {\n    delete api[type];\n  }\n}\n\ntype OTelGlobal = {\n  [GLOBAL_OPENTELEMETRY_API_KEY]?: OTelGlobalAPI;\n};\n\ntype OTelGlobalAPI = {\n  version: string;\n\n  diag?: DiagLogger;\n  trace?: TracerProvider;\n  context?: ContextManager;\n  metrics?: MeterProvider;\n  propagation?: TextMapPropagator;\n};\n"]}