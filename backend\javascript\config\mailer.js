// File Path: Dosky/config/mailer.js (or your chosen path for mailer config)
import nodemailer from 'nodemailer';
import cluster from 'cluster';

// Ensure .env variables are loaded. 
// If server.js loads dotenv globally, this might be redundant but ensures mailer can be tested standalone.
// If you are sure server.js always loads it first, you can remove this.
import dotenv from 'dotenv';
dotenv.config(); 

const EMAIL_SERVICE = process.env.EMAIL_SERVICE;
const EMAIL_HOST = process.env.EMAIL_HOST;
const EMAIL_PORT = parseInt(process.env.EMAIL_PORT || "587", 10); // Default to 587 for TLS
const EMAIL_SECURE = process.env.EMAIL_SECURE === 'true'; // True for port 465 (SSL), false for 587 (STARTTLS)
const EMAIL_USER = process.env.EMAIL_USER;
const EMAIL_PASS = process.env.EMAIL_PASS; // If Gmail with 2FA, this MUST be an App Password

const workerIdLogPrefix = cluster.isWorker 
    ? `Worker ${process.pid} (ID: ${cluster.worker.id})` 
    : `Primary Process ${process.pid}`;

let transporterInstance = null;

if (!EMAIL_USER || !EMAIL_PASS || (!EMAIL_SERVICE && !EMAIL_HOST)) {
    console.error(`${workerIdLogPrefix} - FATAL MAILER ERROR: Email configuration (EMAIL_USER, EMAIL_PASS, and EMAIL_SERVICE/EMAIL_HOST) is missing or incomplete in .env. Mailer will not operate.`);
    // transporterInstance will remain null, sendVerificationEmail will fail gracefully.
} else {
    try {
        const transportOptions = {
            auth: {
                user: EMAIL_USER,
                pass: EMAIL_PASS,
            },
            // Consider adding a logger for debugging nodemailer issues if they persist
            // logger: process.env.NODE_ENV !== 'production', // Log in dev
            // debug: process.env.NODE_ENV !== 'production',  // More verbose debug in dev
        };

        if (EMAIL_SERVICE) {
            transportOptions.service = EMAIL_SERVICE;
        } else if (EMAIL_HOST) {
            transportOptions.host = EMAIL_HOST;
            transportOptions.port = EMAIL_PORT;
            transportOptions.secure = EMAIL_SECURE; // For direct host config, secure needs to be explicit
        } else {
            // This case should be caught by the check above, but as a safeguard:
            throw new Error("Neither EMAIL_SERVICE nor EMAIL_HOST is defined for mailer.");
        }
        
        transporterInstance = nodemailer.createTransport(transportOptions);

        // --- VERIFY TRANSPORTER ONLY IN PRIMARY PROCESS ---
        if (cluster.isPrimary) {
            console.log(`${workerIdLogPrefix} - Nodemailer transporter configured. Initiating verification (Primary process only)...`);
            transporterInstance.verify((error, success) => {
                if (error) {
                    console.error(`${workerIdLogPrefix} - MAILER TRANSPORTER VERIFICATION ERROR (Primary Process): ${error.message}`);
                    // For detailed diagnosis:
                    // console.error("Full verification error object:", error);
                    // Depending on criticality, you might decide if this failure should prevent app start or just log.
                } else {
                    console.log(`${workerIdLogPrefix} - Mailer Transporter is verified and ready to send emails (Primary Process).`);
                }
            });
        } else {
            // Workers will use the configured transporterInstance without re-verifying.
            // console.log(`${workerIdLogPrefix} - Nodemailer transporter configured for worker. Verification skipped for worker.`);
        }

    } catch (error) {
        console.error(`${workerIdLogPrefix} - ERROR CREATING NODEMAILER TRANSPORTER: ${error.message}`, error.stack);
        transporterInstance = null; // Ensure it's null if creation failed
    }
}

export const sendVerificationEmail = async (toEmail, code) => {
  if (!transporterInstance) {
    console.error(`${workerIdLogPrefix} - Nodemailer transporter is not initialized. Cannot send verification email to ${toEmail}. Check mailer configuration and startup logs.`);
    return false; // Indicate failure
  }

  const mailOptions = {
    from: `"PDF Analyzer" <${EMAIL_USER}>`, // Ensure EMAIL_USER is a valid "From" address for your provider
    to: toEmail,
    subject: 'Verify Your Email for PDF Analyzer',
    html: `
      <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background-color: #f9f9f9;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h1 style="color: #0056b3; font-size: 24px;">PDF Analyzer</h1>
        </div>
        <h2 style="color: #0056b3; border-bottom: 2px solid #eee; padding-bottom: 10px;">Email Verification</h2>
        <p>Hi there,</p>
        <p>Thank you for registering with PDF Analyzer! To complete your registration, please use the verification code below:</p>
        <p style="text-align: center; margin: 20px 0;">
          <strong style="font-size: 1.5em; color: #ffffff; background-color: #007bff; padding: 10px 20px; border-radius: 5px; letter-spacing: 2px;">${code}</strong>
        </p>
        <p>This code is valid for <strong>10 minutes</strong>.</p>
        <p>If you did not request this verification, please ignore this email. Your account will remain inactive.</p>
        <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
        <p style="font-size: 0.9em; color: #777;">
          Thanks,<br>
          The PDF Analyzer Team
        </p>
        <div style="text-align: center; margin-top: 20px; font-size: 0.8em; color: #aaa;">
          © ${new Date().getFullYear()} PDF Analyzer. All rights reserved.
        </div>
      </div>
    `,
    text: `
Hello,

Thank you for registering with PDF Analyzer!
Your verification code is: ${code}
This code is valid for 10 minutes.

If you did not request this verification, please ignore this email.

Thanks,
The PDF Analyzer Team
    `
  };

  try {
    let info = await transporterInstance.sendMail(mailOptions);
    if (process.env.NODE_ENV !== 'production') {
        // console.log(`${workerIdLogPrefix} - Verification email sent to ${toEmail}. Message ID: ${info.messageId}`);
    }
    return true;
  } catch (error) {
    console.error(`${workerIdLogPrefix} - ERROR SENDING VERIFICATION EMAIL to ${toEmail}: ${error.message}`, error.stack);
    // For more detailed SMTP error info:
    // if (error.responseCode) console.error("SMTP Response Code:", error.responseCode);
    // if (error.response) console.error("SMTP Response:", error.response);
    return false;
  }
};

// Export the transporter instance itself if other parts of the app need to send different types of emails
export { transporterInstance as transporter };