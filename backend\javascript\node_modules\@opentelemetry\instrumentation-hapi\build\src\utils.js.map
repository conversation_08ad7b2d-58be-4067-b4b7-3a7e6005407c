{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,8EAG6C;AAE7C,qDAM0B;AAC1B,2DAAwD;AAExD,SAAgB,aAAa,CAAI,MAAsB;IACrD,IAAK,MAAiC,CAAC,IAAI,EAAE;QAC3C,OAAQ,MAAiC,CAAC,IAAI,CAAC;KAChD;SAAM;QACL,OAAQ,MAA6B,CAAC,GAAG,CAAC,IAAI,CAAC;KAChD;AACH,CAAC;AAND,sCAMC;AAEM,MAAM,kBAAkB,GAAG,CAChC,eAAwB,EACsB,EAAE;IAChD,OAAO,CACL,OAAO,eAAe,KAAK,QAAQ;QACnC,yCAAwB,CAAC,GAAG,CAAC,eAAe,CAAC,CAC9C,CAAC;AACJ,CAAC,CAAC;AAPW,QAAA,kBAAkB,sBAO7B;AAEK,MAAM,sBAAsB,GAAG,CACpC,eAAwB,EAC8B,EAAE;;IACxD,MAAM,KAAK,GAAG,MAAC,eAAqD,0CAAE,IAAI,CAAC;IAC3E,OAAO,KAAK,KAAK,SAAS,IAAI,IAAA,0BAAkB,EAAC,KAAK,CAAC,CAAC;AAC1D,CAAC,CAAC;AALW,QAAA,sBAAsB,0BAKjC;AAEK,MAAM,gBAAgB,GAAG,CAC9B,eAAwB,EACiB,EAAE;IAC3C,OAAO,CACL,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC;QAC9B,eAAe,CAAC,MAAM,IAAI,CAAC;QAC3B,IAAA,0BAAkB,EAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACtC,OAAO,eAAe,CAAC,CAAC,CAAC,KAAK,UAAU,CACzC,CAAC;AACJ,CAAC,CAAC;AATW,QAAA,gBAAgB,oBAS3B;AAEK,MAAM,oBAAoB,GAAG,CAClC,eAA0D,EACnB,EAAE;IACzC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;AACzC,CAAC,CAAC;AAJW,QAAA,oBAAoB,wBAI/B;AAEK,MAAM,gBAAgB,GAAG,CAC9B,KAAuB,EACvB,UAAmB,EAInB,EAAE;IACF,IAAI,UAAU,EAAE;QACd,OAAO;YACL,UAAU,EAAE;gBACV,CAAC,0CAAmB,CAAC,EAAE,KAAK,CAAC,IAAI;gBACjC,CAAC,2CAAoB,CAAC,EAAE,KAAK,CAAC,MAAM;gBACpC,CAAC,+BAAc,CAAC,SAAS,CAAC,EAAE,8BAAa,CAAC,MAAM;gBAChD,CAAC,+BAAc,CAAC,WAAW,CAAC,EAAE,UAAU;aACzC;YACD,IAAI,EAAE,GAAG,UAAU,aAAa,KAAK,CAAC,IAAI,EAAE;SAC7C,CAAC;KACH;IACD,OAAO;QACL,UAAU,EAAE;YACV,CAAC,0CAAmB,CAAC,EAAE,KAAK,CAAC,IAAI;YACjC,CAAC,2CAAoB,CAAC,EAAE,KAAK,CAAC,MAAM;YACpC,CAAC,+BAAc,CAAC,SAAS,CAAC,EAAE,8BAAa,CAAC,MAAM;SACjD;QACD,IAAI,EAAE,WAAW,KAAK,CAAC,IAAI,EAAE;KAC9B,CAAC;AACJ,CAAC,CAAC;AA1BW,QAAA,gBAAgB,oBA0B3B;AAEK,MAAM,cAAc,GAAG,CAC5B,QAAmC,EACnC,UAAmB,EAInB,EAAE;IACF,IAAI,UAAU,EAAE;QACd,OAAO;YACL,UAAU,EAAE;gBACV,CAAC,+BAAc,CAAC,QAAQ,CAAC,EAAE,QAAQ;gBACnC,CAAC,+BAAc,CAAC,SAAS,CAAC,EAAE,8BAAa,CAAC,GAAG;gBAC7C,CAAC,+BAAc,CAAC,WAAW,CAAC,EAAE,UAAU;aACzC;YACD,IAAI,EAAE,GAAG,UAAU,WAAW,QAAQ,EAAE;SACzC,CAAC;KACH;IACD,OAAO;QACL,UAAU,EAAE;YACV,CAAC,+BAAc,CAAC,QAAQ,CAAC,EAAE,QAAQ;YACnC,CAAC,+BAAc,CAAC,SAAS,CAAC,EAAE,8BAAa,CAAC,GAAG;SAC9C;QACD,IAAI,EAAE,SAAS,QAAQ,EAAE;KAC1B,CAAC;AACJ,CAAC,CAAC;AAxBW,QAAA,cAAc,kBAwBzB;AAEK,MAAM,kBAAkB,GAAG,CAChC,SAA8B,EACR,EAAE;IACxB,IAAI,QAAQ,IAAI,SAAS,EAAE;QACzB,IAAI,QAAQ,IAAI,SAAS,CAAC,MAAM,EAAE;YAChC,OAAO,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC;SAChC;QACD,OAAO,SAAS,CAAC,MAAM,CAAC;KACzB;IACD,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAVW,QAAA,kBAAkB,sBAU7B", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SpanAttributes } from '@opentelemetry/api';\nimport {\n  SEMATTRS_HTTP_METHOD,\n  SEMATTRS_HTTP_ROUTE,\n} from '@opentelemetry/semantic-conventions';\nimport type * as Hapi from '@hapi/hapi';\nimport {\n  HapiLayerType,\n  HapiLifecycleMethodNames,\n  HapiPluginObject,\n  PatchableExtMethod,\n  ServerExtDirectInput,\n} from './internal-types';\nimport { AttributeNames } from './enums/AttributeNames';\n\nexport function getPluginName<T>(plugin: Hapi.Plugin<T>): string {\n  if ((plugin as Hapi.PluginNameVersion).name) {\n    return (plugin as Hapi.PluginNameVersion).name;\n  } else {\n    return (plugin as Hapi.PluginPackage).pkg.name;\n  }\n}\n\nexport const isLifecycleExtType = (\n  variableToCheck: unknown\n): variableToCheck is Hapi.ServerRequestExtType => {\n  return (\n    typeof variableToCheck === 'string' &&\n    HapiLifecycleMethodNames.has(variableToCheck)\n  );\n};\n\nexport const isLifecycleExtEventObj = (\n  variableToCheck: unknown\n): variableToCheck is Hapi.ServerExtEventsRequestObject => {\n  const event = (variableToCheck as Hapi.ServerExtEventsRequestObject)?.type;\n  return event !== undefined && isLifecycleExtType(event);\n};\n\nexport const isDirectExtInput = (\n  variableToCheck: unknown\n): variableToCheck is ServerExtDirectInput => {\n  return (\n    Array.isArray(variableToCheck) &&\n    variableToCheck.length <= 3 &&\n    isLifecycleExtType(variableToCheck[0]) &&\n    typeof variableToCheck[1] === 'function'\n  );\n};\n\nexport const isPatchableExtMethod = (\n  variableToCheck: PatchableExtMethod | PatchableExtMethod[]\n): variableToCheck is PatchableExtMethod => {\n  return !Array.isArray(variableToCheck);\n};\n\nexport const getRouteMetadata = (\n  route: Hapi.ServerRoute,\n  pluginName?: string\n): {\n  attributes: SpanAttributes;\n  name: string;\n} => {\n  if (pluginName) {\n    return {\n      attributes: {\n        [SEMATTRS_HTTP_ROUTE]: route.path,\n        [SEMATTRS_HTTP_METHOD]: route.method,\n        [AttributeNames.HAPI_TYPE]: HapiLayerType.PLUGIN,\n        [AttributeNames.PLUGIN_NAME]: pluginName,\n      },\n      name: `${pluginName}: route - ${route.path}`,\n    };\n  }\n  return {\n    attributes: {\n      [SEMATTRS_HTTP_ROUTE]: route.path,\n      [SEMATTRS_HTTP_METHOD]: route.method,\n      [AttributeNames.HAPI_TYPE]: HapiLayerType.ROUTER,\n    },\n    name: `route - ${route.path}`,\n  };\n};\n\nexport const getExtMetadata = (\n  extPoint: Hapi.ServerRequestExtType,\n  pluginName?: string\n): {\n  attributes: SpanAttributes;\n  name: string;\n} => {\n  if (pluginName) {\n    return {\n      attributes: {\n        [AttributeNames.EXT_TYPE]: extPoint,\n        [AttributeNames.HAPI_TYPE]: HapiLayerType.EXT,\n        [AttributeNames.PLUGIN_NAME]: pluginName,\n      },\n      name: `${pluginName}: ext - ${extPoint}`,\n    };\n  }\n  return {\n    attributes: {\n      [AttributeNames.EXT_TYPE]: extPoint,\n      [AttributeNames.HAPI_TYPE]: HapiLayerType.EXT,\n    },\n    name: `ext - ${extPoint}`,\n  };\n};\n\nexport const getPluginFromInput = <T>(\n  pluginObj: HapiPluginObject<T>\n): Hapi.Plugin<T, void> => {\n  if ('plugin' in pluginObj) {\n    if ('plugin' in pluginObj.plugin) {\n      return pluginObj.plugin.plugin;\n    }\n    return pluginObj.plugin;\n  }\n  return pluginObj;\n};\n"]}