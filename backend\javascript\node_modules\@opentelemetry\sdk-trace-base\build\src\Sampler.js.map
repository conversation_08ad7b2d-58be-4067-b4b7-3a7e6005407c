{"version": 3, "file": "Sampler.js", "sourceRoot": "", "sources": ["../../src/Sampler.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAUH;;;GAGG;AACH,IAAY,gBAgBX;AAhBD,WAAY,gBAAgB;IAC1B;;;OAGG;IACH,mEAAU,CAAA;IACV;;;OAGG;IACH,2DAAM,CAAA;IACN;;;OAGG;IACH,mFAAkB,CAAA;AACpB,CAAC,EAhBW,gBAAgB,GAAhB,wBAAgB,KAAhB,wBAAgB,QAgB3B", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Context,\n  Link,\n  SpanAttributes,\n  SpanKind,\n  TraceState,\n} from '@opentelemetry/api';\n\n/**\n * A sampling decision that determines how a {@link Span} will be recorded\n * and collected.\n */\nexport enum SamplingDecision {\n  /**\n   * `Span.isRecording() === false`, span will not be recorded and all events\n   * and attributes will be dropped.\n   */\n  NOT_RECORD,\n  /**\n   * `Span.isRecording() === true`, but `Sampled` flag in {@link TraceFlags}\n   * MUST NOT be set.\n   */\n  RECORD,\n  /**\n   * `Span.isRecording() === true` AND `Sampled` flag in {@link TraceFlags}\n   * MUST be set.\n   */\n  RECORD_AND_SAMPLED,\n}\n\n/**\n * A sampling result contains a decision for a {@link Span} and additional\n * attributes the sampler would like to added to the Span.\n */\nexport interface SamplingResult {\n  /**\n   * A sampling decision, refer to {@link SamplingDecision} for details.\n   */\n  decision: SamplingDecision;\n  /**\n   * The list of attributes returned by SamplingResult MUST be immutable.\n   * Caller may call {@link Sampler}.shouldSample any number of times and\n   * can safely cache the returned value.\n   */\n  attributes?: Readonly<SpanAttributes>;\n  /**\n   * A {@link TraceState} that will be associated with the {@link Span} through\n   * the new {@link SpanContext}. Samplers SHOULD return the TraceState from\n   * the passed-in {@link Context} if they do not intend to change it. Leaving\n   * the value undefined will also leave the TraceState unchanged.\n   */\n  traceState?: TraceState;\n}\n\n/**\n * This interface represent a sampler. Sampling is a mechanism to control the\n * noise and overhead introduced by OpenTelemetry by reducing the number of\n * samples of traces collected and sent to the backend.\n */\nexport interface Sampler {\n  /**\n   * Checks whether span needs to be created and tracked.\n   *\n   * @param context Parent Context which may contain a span.\n   * @param traceId of the span to be created. It can be different from the\n   *     traceId in the {@link SpanContext}. Typically in situations when the\n   *     span to be created starts a new trace.\n   * @param spanName of the span to be created.\n   * @param spanKind of the span to be created.\n   * @param attributes Initial set of SpanAttributes for the Span being constructed.\n   * @param links Collection of links that will be associated with the Span to\n   *     be created. Typically useful for batch operations.\n   * @returns a {@link SamplingResult}.\n   */\n  shouldSample(\n    context: Context,\n    traceId: string,\n    spanName: string,\n    spanKind: SpanKind,\n    attributes: SpanAttributes,\n    links: Link[]\n  ): SamplingResult;\n\n  /** Returns the sampler name or short description with the configuration. */\n  toString(): string;\n}\n"]}