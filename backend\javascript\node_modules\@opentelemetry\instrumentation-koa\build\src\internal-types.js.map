{"version": 3, "file": "internal-types.js", "sourceRoot": "", "sources": ["../../src/internal-types.ts"], "names": [], "mappings": ";;;AA0BA;;;GAGG;AACU,QAAA,aAAa,GAAkB,MAAM,CAAC,mBAAmB,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport type { Middleware, ParameterizedContext, DefaultState } from 'koa';\nimport type * as Router from '@koa/router';\n\nexport type KoaContext = ParameterizedContext<\n  DefaultState,\n  Router.RouterParamContext\n>;\nexport type KoaMiddleware = Middleware<DefaultState, KoaContext> & {\n  router?: Router;\n};\n\n/**\n * This symbol is used to mark a Koa layer as being already instrumented\n * since its possible to use a given layer multiple times (ex: middlewares)\n */\nexport const kLayerPatched: unique symbol = Symbol('koa-layer-patched');\n\nexport type KoaPatchedMiddleware = KoaMiddleware & {\n  [kLayerPatched]?: boolean;\n};\n"]}