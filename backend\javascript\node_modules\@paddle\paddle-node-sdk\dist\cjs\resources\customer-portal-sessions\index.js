"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerPortalSessionsResource = void 0;
const index_js_1 = require("../../entities/index.js");
const index_js_2 = require("../../internal/base/index.js");
const CustomerPortalSessionPaths = {
    create: '/customers/{customer_id}/portal-sessions',
};
class CustomerPortalSessionsResource extends index_js_2.BaseResource {
    create(customerId, subscriptionIds) {
        return __awaiter(this, void 0, void 0, function* () {
            const urlWithPathParams = new index_js_2.PathParameters(CustomerPortalSessionPaths.create, {
                customer_id: customerId,
            }).deriveUrl();
            const response = yield this.client.post(urlWithPathParams, {
                subscriptionIds,
            });
            const data = this.handleResponse(response);
            return new index_js_1.CustomerPortalSession(data);
        });
    }
}
exports.CustomerPortalSessionsResource = CustomerPortalSessionsResource;
