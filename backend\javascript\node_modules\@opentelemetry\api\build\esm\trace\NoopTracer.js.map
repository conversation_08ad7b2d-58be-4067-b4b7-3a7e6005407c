{"version": 3, "file": "NoopTracer.js", "sourceRoot": "", "sources": ["../../../src/trace/NoopTracer.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AAE5C,OAAO,EAAE,cAAc,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAC;AACjE,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAEtD,OAAO,EAAE,kBAAkB,EAAE,MAAM,qBAAqB,CAAC;AAKzD,IAAM,UAAU,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;AAE5C;;GAEG;AACH;IAAA;IAoEA,CAAC;IAnEC,gCAAgC;IAChC,8BAAS,GAAT,UACE,IAAY,EACZ,OAAqB,EACrB,OAA6B;QAA7B,wBAAA,EAAA,UAAU,UAAU,CAAC,MAAM,EAAE;QAE7B,IAAM,IAAI,GAAG,OAAO,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CAAC,CAAC;QACpC,IAAI,IAAI,EAAE;YACR,OAAO,IAAI,gBAAgB,EAAE,CAAC;SAC/B;QAED,IAAM,iBAAiB,GAAG,OAAO,IAAI,cAAc,CAAC,OAAO,CAAC,CAAC;QAE7D,IACE,aAAa,CAAC,iBAAiB,CAAC;YAChC,kBAAkB,CAAC,iBAAiB,CAAC,EACrC;YACA,OAAO,IAAI,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;SAChD;aAAM;YACL,OAAO,IAAI,gBAAgB,EAAE,CAAC;SAC/B;IACH,CAAC;IAiBD,oCAAe,GAAf,UACE,IAAY,EACZ,IAAsB,EACtB,IAAkB,EAClB,IAAQ;QAER,IAAI,IAA6B,CAAC;QAClC,IAAI,GAAwB,CAAC;QAC7B,IAAI,EAAK,CAAC;QAEV,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YACxB,OAAO;SACR;aAAM,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACjC,EAAE,GAAG,IAAS,CAAC;SAChB;aAAM,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACjC,IAAI,GAAG,IAA+B,CAAC;YACvC,EAAE,GAAG,IAAS,CAAC;SAChB;aAAM;YACL,IAAI,GAAG,IAA+B,CAAC;YACvC,GAAG,GAAG,IAA2B,CAAC;YAClC,EAAE,GAAG,IAAS,CAAC;SAChB;QAED,IAAM,aAAa,GAAG,GAAG,aAAH,GAAG,cAAH,GAAG,GAAI,UAAU,CAAC,MAAM,EAAE,CAAC;QACjD,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;QACvD,IAAM,kBAAkB,GAAG,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QAExD,OAAO,UAAU,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IAClE,CAAC;IACH,iBAAC;AAAD,CAAC,AApED,IAoEC;;AAED,SAAS,aAAa,CAAC,WAAgB;IACrC,OAAO,CACL,OAAO,WAAW,KAAK,QAAQ;QAC/B,OAAO,WAAW,CAAC,QAAQ,CAAC,KAAK,QAAQ;QACzC,OAAO,WAAW,CAAC,SAAS,CAAC,KAAK,QAAQ;QAC1C,OAAO,WAAW,CAAC,YAAY,CAAC,KAAK,QAAQ,CAC9C,CAAC;AACJ,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ContextAPI } from '../api/context';\nimport { Context } from '../context/types';\nimport { getSpanContext, setSpan } from '../trace/context-utils';\nimport { NonRecordingSpan } from './NonRecordingSpan';\nimport { Span } from './span';\nimport { isSpanContextValid } from './spancontext-utils';\nimport { SpanOptions } from './SpanOptions';\nimport { SpanContext } from './span_context';\nimport { Tracer } from './tracer';\n\nconst contextApi = ContextAPI.getInstance();\n\n/**\n * No-op implementations of {@link Tracer}.\n */\nexport class NoopTracer implements Tracer {\n  // startSpan starts a noop span.\n  startSpan(\n    name: string,\n    options?: SpanOptions,\n    context = contextApi.active()\n  ): Span {\n    const root = Boolean(options?.root);\n    if (root) {\n      return new NonRecordingSpan();\n    }\n\n    const parentFromContext = context && getSpanContext(context);\n\n    if (\n      isSpanContext(parentFromContext) &&\n      isSpanContextValid(parentFromContext)\n    ) {\n      return new NonRecordingSpan(parentFromContext);\n    } else {\n      return new NonRecordingSpan();\n    }\n  }\n\n  startActiveSpan<F extends (span: Span) => ReturnType<F>>(\n    name: string,\n    fn: F\n  ): ReturnType<F>;\n  startActiveSpan<F extends (span: Span) => ReturnType<F>>(\n    name: string,\n    opts: SpanOptions | undefined,\n    fn: F\n  ): ReturnType<F>;\n  startActiveSpan<F extends (span: Span) => ReturnType<F>>(\n    name: string,\n    opts: SpanOptions | undefined,\n    ctx: Context | undefined,\n    fn: F\n  ): ReturnType<F>;\n  startActiveSpan<F extends (span: Span) => ReturnType<F>>(\n    name: string,\n    arg2?: F | SpanOptions,\n    arg3?: F | Context,\n    arg4?: F\n  ): ReturnType<F> | undefined {\n    let opts: SpanOptions | undefined;\n    let ctx: Context | undefined;\n    let fn: F;\n\n    if (arguments.length < 2) {\n      return;\n    } else if (arguments.length === 2) {\n      fn = arg2 as F;\n    } else if (arguments.length === 3) {\n      opts = arg2 as SpanOptions | undefined;\n      fn = arg3 as F;\n    } else {\n      opts = arg2 as SpanOptions | undefined;\n      ctx = arg3 as Context | undefined;\n      fn = arg4 as F;\n    }\n\n    const parentContext = ctx ?? contextApi.active();\n    const span = this.startSpan(name, opts, parentContext);\n    const contextWithSpanSet = setSpan(parentContext, span);\n\n    return contextApi.with(contextWithSpanSet, fn, undefined, span);\n  }\n}\n\nfunction isSpanContext(spanContext: any): spanContext is SpanContext {\n  return (\n    typeof spanContext === 'object' &&\n    typeof spanContext['spanId'] === 'string' &&\n    typeof spanContext['traceId'] === 'string' &&\n    typeof spanContext['traceFlags'] === 'number'\n  );\n}\n"]}