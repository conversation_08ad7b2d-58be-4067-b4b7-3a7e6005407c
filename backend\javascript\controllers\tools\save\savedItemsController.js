// src/controllers/savedItemsController.js
import SavedAnalysis from '../../../models/save/pdf/SavedAnalysis.js';
import SavedMindMap from '../../../models/save/pdf/SavedMindMap.js';
import SavedChat from '../../../models/save/pdf/SavedChat.js';
import mongoose from 'mongoose';

// POST /api/saved-items/analysis
export const saveAnalysis = async (req, res) => {
  try {
    const { fileName, summary, language, isDeepDive, pdfType } = req.body;
    if (!fileName || !summary) {
      return res.status(400).json({ error: 'Missing required fields: fileName and summary.' });
    }
    const newAnalysis = new SavedAnalysis({
      userId: req.user.id, fileName, summary, language, isDeepDive, pdfType, savedAt: new Date(),
    });
    const savedAnalysis = await newAnalysis.save();
    res.status(201).json(savedAnalysis.toJSON());
  } catch (error)
  {
    console.error('Error saving analysis:', error);
    res.status(500).json({ error: 'Server error while saving analysis.', details: error.message });
  }
};

// POST /api/saved-items/mindmap
export const saveMindMap = async (req, res) => {
  try {
    const { id, generationTaskId, fileName, mindMapData, thumbnailUrl } = req.body;

    if (!fileName || !mindMapData || !mindMapData.name) {
      return res.status(400).json({ error: 'Missing required fields: fileName and mindMapData with a root name.' });
    }

    let summaryPreview = "Mind map data available.";
    if (mindMapData && mindMapData.name) {
      summaryPreview = `Central Topic: ${mindMapData.name.substring(0, 150)}${mindMapData.name.length > 150 ? '...' : ''}`;
      if (mindMapData.children && mindMapData.children.length > 0) {
        const branchCount = mindMapData.children.length;
        summaryPreview += ` With ${branchCount} main ${branchCount > 1 ? 'branches' : 'branch'}.`;
      }
    }

    let savedMindMapDoc;

    if (id) { // Handle UPDATE of an existing mind map
      if (!mongoose.Types.ObjectId.isValid(id)) {
        return res.status(400).json({ error: 'Invalid MindMap ID format for update.' });
      }
      const existingMindMap = await SavedMindMap.findOne({ _id: id, userId: req.user.id });

      if (!existingMindMap) {
        return res.status(404).json({ error: 'Mind map not found to update or user not authorized.' });
      }

      existingMindMap.fileName = fileName;
      existingMindMap.mindMapData = mindMapData;
      existingMindMap.thumbnailUrl = thumbnailUrl;
      existingMindMap.summary = summaryPreview;
      existingMindMap.savedAt = new Date();
      if (generationTaskId) existingMindMap.generationTaskId = generationTaskId;

      savedMindMapDoc = await existingMindMap.save();
      return res.status(200).json(savedMindMapDoc.toJSON());

    } else { // Handle NEW save
      // Check for an existing mind map with the same generationTaskId to prevent duplicates
      if (generationTaskId) {
        const duplicateCheck = await SavedMindMap.findOne({ userId: req.user.id, generationTaskId: generationTaskId });
        if (duplicateCheck) {
          // If found, update it instead of creating a new one
          duplicateCheck.fileName = fileName;
          duplicateCheck.mindMapData = mindMapData;
          duplicateCheck.thumbnailUrl = thumbnailUrl;
          duplicateCheck.summary = summaryPreview;
          duplicateCheck.savedAt = new Date();
          savedMindMapDoc = await duplicateCheck.save();
          // Return 200 OK because we updated an existing resource
          return res.status(200).json(savedMindMapDoc.toJSON());
        }
      }

      // If no duplicate is found, create a new document
      const newMindMap = new SavedMindMap({
        userId: req.user.id,
        generationTaskId: generationTaskId,
        fileName,
        mindMapData,
        thumbnailUrl,
        summary: summaryPreview,
        savedAt: new Date(),
      });
      savedMindMapDoc = await newMindMap.save();
      // Return 201 Created because a new resource was made
      return res.status(201).json(savedMindMapDoc.toJSON());
    }

  } catch (error) {
    if (error.code === 11000) {
        return res.status(409).json({ error: 'A mind map with this identifier already exists or a conflict occurred.', details: error.message });
    }
    console.error('Error saving/updating mind map:', error);
    res.status(500).json({ error: 'Server error while saving/updating mind map.', details: error.message });
  }
};


// POST /api/saved-items/chat
export const saveChatSession = async (req, res) => {
  try {
    const userId = req.user.id;
    const { sessionId, pdfName, messages, firstMessageTimestamp } = req.body;

    if (!sessionId || !pdfName || !messages || !Array.isArray(messages)) {
      return res.status(400).json({ success: false, message: "Missing or invalid fields (sessionId, pdfName, messages array)." });
    }

    const savedChat = await SavedChat.findOneAndUpdate(
      { userId: userId, sessionId: sessionId },
      {
        userId: userId,
        sessionId: sessionId,
        pdfName: pdfName,
        messages: messages,
        firstMessageTimestamp: firstMessageTimestamp ? new Date(firstMessageTimestamp) : (messages.length > 0 && messages[0].id ? new Date(parseInt(messages[0].id.split('-').pop())) : new Date()),
        savedAt: new Date()
      },
      { new: true, upsert: true, runValidators: true }
    );

    if (!savedChat) {
      return res.status(500).json({ success: false, message: "Failed to save or update chat session." });
    }
    
    res.status(200).json({ success: true, message: "Chat saved successfully.", savedChat: savedChat.toJSON() });

  } catch (error) {
    if (error.code === 11000) {
        return res.status(409).json({ success: false, message: 'This chat session might already be saved or a conflict occurred.', details: error.message });
    }
    console.error("Error in /api/saved-items/chat:", error);
    res.status(500).json({ success: false, message: "Server error while saving chat.", details: error.message });
  }
};


// GET /api/saved-items
export const getSavedItems = async (req, res) => {
  try {
    const analyses = await SavedAnalysis.find({ userId: req.user.id }).sort({ savedAt: -1 });
    const mindMaps = await SavedMindMap.find({ userId: req.user.id }).sort({ savedAt: -1 });
    const chats = await SavedChat.find({ userId: req.user.id }).sort({ savedAt: -1 });

    res.status(200).json({
        analyses: analyses.map(doc => doc.toJSON()),
        mindMaps: mindMaps.map(doc => doc.toJSON()),
        chats: chats.map(doc => doc.toJSON())
    });
  } catch (error) {
    console.error('Error fetching saved items:', error);
    res.status(500).json({ error: 'Server error while fetching saved items.', details: error.message });
  }
};

// DELETE /api/saved-items/:itemType/:itemId
export const deleteSavedItem = async (req, res) => {
  try {
    const { itemType, itemId } = req.params;
    let deletedItem;

    if (!mongoose.Types.ObjectId.isValid(itemId)) {
      return res.status(400).json({ error: `Invalid ${itemType} ID format.` });
    }

    if (itemType === 'analysis') {
      deletedItem = await SavedAnalysis.findOneAndDelete({ _id: itemId, userId: req.user.id });
    } else if (itemType === 'mindmap') {
      deletedItem = await SavedMindMap.findOneAndDelete({ _id: itemId, userId: req.user.id });
    } else if (itemType === 'chat') {
      deletedItem = await SavedChat.findOneAndDelete({ _id: itemId, userId: req.user.id });
    } else {
      return res.status(400).json({ error: 'Invalid item type specified.' });
    }

    if (!deletedItem) {
      return res.status(404).json({ error: 'Item not found or user not authorized to delete.' });
    }

    res.status(200).json({ message: `${itemType.charAt(0).toUpperCase() + itemType.slice(1)} deleted successfully.`, itemId: itemId, itemType: itemType });
  } catch (error) {
    console.error(`Error deleting ${itemType}:`, error);
    res.status(500).json({ error: `Server error while deleting ${itemType}.`, details: error.message });
  }
};