"use strict";var k=Object.defineProperty;var At=Object.getOwnPropertyDescriptor;var Et=Object.getOwnPropertyNames;var Ft=Object.prototype.hasOwnProperty;var Vt=(t,e)=>{for(var o in e)k(t,o,{get:e[o],enumerable:!0})},Nt=(t,e,o,i)=>{if(e&&typeof e=="object"||typeof e=="function")for(let c of Et(e))!Ft.call(t,c)&&c!==o&&k(t,c,{get:()=>e[c],enumerable:!(i=At(e,c))||i.enumerable});return t};var _t=t=>Nt(k({},"__esModule",{value:!0}),t);var zt={};Vt(zt,{activateLicense:()=>wt,archiveCustomer:()=>W,cancelSubscription:()=>st,createCheckout:()=>It,createCustomer:()=>Q,createDiscount:()=>gt,createUsageRecord:()=>ft,createWebhook:()=>Gt,deactivateLicense:()=>qt,deleteDiscount:()=>Lt,deleteWebhook:()=>Dt,generateOrderInvoice:()=>Y,generateSubscriptionInvoice:()=>at,getAuthenticatedUser:()=>D,getCheckout:()=>Tt,getCustomer:()=>A,getDiscount:()=>Pt,getDiscountRedemption:()=>St,getFile:()=>z,getLicenseKey:()=>lt,getLicenseKeyInstance:()=>$t,getOrder:()=>B,getOrderItem:()=>X,getPrice:()=>j,getProduct:()=>F,getStore:()=>K,getSubscription:()=>et,getSubscriptionInvoice:()=>it,getSubscriptionItem:()=>ut,getSubscriptionItemCurrentUsage:()=>pt,getUsageRecord:()=>yt,getVariant:()=>N,getWebhook:()=>Rt,issueOrderRefund:()=>Z,issueSubscriptionInvoiceRefund:()=>ct,lemonSqueezySetup:()=>R,listCheckouts:()=>Ot,listCustomers:()=>E,listDiscountRedemptions:()=>vt,listDiscounts:()=>bt,listFiles:()=>M,listLicenseKeyInstances:()=>Ct,listLicenseKeys:()=>xt,listOrderItems:()=>tt,listOrders:()=>J,listPrices:()=>H,listProducts:()=>V,listStores:()=>w,listSubscriptionInvoices:()=>nt,listSubscriptionItems:()=>mt,listSubscriptions:()=>ot,listUsageRecords:()=>ht,listVariants:()=>_,listWebhooks:()=>Kt,updateCustomer:()=>q,updateLicenseKey:()=>kt,updateSubscription:()=>rt,updateSubscriptionItem:()=>dt,updateWebhook:()=>Ut,validateLicense:()=>Qt});module.exports=_t(zt);var $={};function C(t){return $[t]}function I(t,e){$[t]=e}var v="__config__",T="https://api.lemonsqueezy.com";function O(t){return Object.prototype.toString.call(t)==="[object Object]"}function jt(t){return t.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`)}function u(t,e=void 0){let o={};for(let i in t)t[i]!==e&&(o[jt(i)]=O(t[i])?u(t[i],e):t[i]);return o}function n(t){return!t||!Array.isArray(t)||!t.length?"":`?include=${t.join(",")}`}function a(t){let{filter:e={},page:o={},include:i=[]}=t,c=u({filter:e,page:o,include:i.join(",")}),p={};for(let y in c){let h=c[y];if(O(h))for(let m in h)p[`${y}[${m}]`]=`${h[m]}`;else p[`${y}`]=`${c[y]}`}let d=new URLSearchParams(p).toString();return d?`?${d}`:""}function G(){return btoa(Date.now().toString()).slice(-10,-2).toUpperCase()}function s(t){for(let e in t)if(!t[e])throw Error(`Please provide the required parameter: ${e}.`)}function R(t){let{apiKey:e,onError:o}=t;return I(v,{apiKey:e,onError:o}),t}async function r(t,e=!0){let o={statusCode:null,data:null,error:Error()},{apiKey:i,onError:c}=C(v)||{};try{if(e&&!i)return o.error=U("Please provide your Lemon Squeezy API key. Create a new API key: https://app.lemonsqueezy.com/settings/api","Missing API key"),c?.(o.error),o;let{path:p,method:d="GET",query:y,body:h}=t,m={method:d},b=new URL(`${T}${p}`);for(let P in y)b.searchParams.append(P,y[P]);m.headers=new Headers,m.headers.set("Accept","application/vnd.api+json"),m.headers.set("Content-Type","application/vnd.api+json"),e&&m.headers.set("Authorization",`Bearer ${i}`),["PATCH","POST"].includes(d)&&(m.body=h?JSON.stringify(h):null);let f=await fetch(b.href,m),g=await f.json(),l=f.ok,L=f.status;if(l)Object.assign(o,{statusCode:L,data:g,error:null});else{let{errors:P,error:S,message:x}=g,Wt=P||S||x||"unknown cause";Object.assign(o,{statusCode:L,data:g,error:U(f.statusText,Wt)})}}catch(p){Object.assign(o,{error:p})}return o.error&&c?.(o.error),o}function U(t,e="unknown"){let o=new Error(t);return o.name="Lemon Squeezy Error",o.cause=e,o}function D(){return r({path:"/v1/users/me"})}function K(t,e={}){return s({storeId:t}),r({path:`/v1/stores/${t}${n(e.include)}`})}function w(t={}){return r({path:`/v1/stores${a(t)}`})}function Q(t,e){return s({storeId:t}),r({path:"/v1/customers",method:"POST",body:{data:{type:"customers",attributes:e,relationships:{store:{data:{type:"stores",id:t.toString()}}}}}})}function q(t,e){return s({customerId:t}),r({path:`/v1/customers/${t}`,method:"PATCH",body:{data:{type:"customers",id:t.toString(),attributes:e}}})}function W(t){return s({customerId:t}),r({path:`/v1/customers/${t}`,method:"PATCH",body:{data:{type:"customers",id:t.toString(),attributes:{status:"archived"}}}})}function A(t,e={}){return s({customerId:t}),r({path:`/v1/customers/${t}${n(e.include)}`})}function E(t={}){return r({path:`/v1/customers${a(t)}`})}function F(t,e={}){return s({productId:t}),r({path:`/v1/products/${t}${n(e.include)}`})}function V(t={}){return r({path:`/v1/products${a(t)}`})}function N(t,e={}){return s({variantId:t}),r({path:`/v1/variants/${t}${n(e.include)}`})}function _(t={}){return r({path:`/v1/variants${a(t)}`})}function j(t,e={}){return s({priceId:t}),r({path:`/v1/prices/${t}${n(e.include)}`})}function H(t={}){return r({path:`/v1/prices${a(t)}`})}function z(t,e={}){return s({fileId:t}),r({path:`/v1/files/${t}${n(e.include)}`})}function M(t={}){return r({path:`/v1/files${a(t)}`})}function B(t,e={}){return s({orderId:t}),r({path:`/v1/orders/${t}${n(e.include)}`})}function J(t={}){return r({path:`/v1/orders${a(t)}`})}function Y(t,e={}){s({orderId:t});let o=u(e),i=new URLSearchParams(o).toString(),c=i?`?${i}`:"";return r({path:`/v1/orders/${t}/generate-invoice${c}`,method:"POST"})}function Z(t,e){s({orderId:t,amount:e});let o={amount:e};return r({path:`/v1/orders/${t}/refund`,method:"POST",body:{data:{type:"orders",id:t.toString(),attributes:u(o)}}})}function X(t,e={}){return s({orderItemId:t}),r({path:`/v1/order-items/${t}${n(e.include)}`})}function tt(t={}){return r({path:`/v1/order-items${a(t)}`})}function et(t,e={}){return s({subscriptionId:t}),r({path:`/v1/subscriptions/${t}${n(e.include)}`})}function rt(t,e){s({subscriptionId:t});let{variantId:o,cancelled:i,billingAnchor:c,invoiceImmediately:p,disableProrations:d,pause:y,trialEndsAt:h}=e,m=u({variantId:o,cancelled:i,billingAnchor:c,invoiceImmediately:p,disableProrations:d,pause:y,trialEndsAt:h});return r({path:`/v1/subscriptions/${t}`,method:"PATCH",body:{data:{type:"subscriptions",id:t.toString(),attributes:m}}})}function st(t){return s({subscriptionId:t}),r({path:`/v1/subscriptions/${t}`,method:"DELETE"})}function ot(t={}){return r({path:`/v1/subscriptions${a(t)}`})}function it(t,e={}){return s({subscriptionInvoiceId:t}),r({path:`/v1/subscription-invoices/${t}${n(e.include)}`})}function nt(t={}){return r({path:`/v1/subscription-invoices${a(t)}`})}function at(t,e={}){s({subscriptionInvoiceId:t});let o=u(e),i=new URLSearchParams(o).toString(),c=i?`?${i}`:"";return r({path:`/v1/subscription-invoices/${t}/generate-invoice${c}`,method:"POST"})}function ct(t,e){s({subscriptionInvoiceId:t,amount:e});let o={amount:e};return r({path:`/v1/subscription-invoices/${t}/refund`,method:"POST",body:{data:{type:"subscription-invoices",id:t.toString(),attributes:u(o)}}})}function ut(t,e={}){return s({subscriptionItemId:t}),r({path:`/v1/subscription-items/${t}${n(e.include)}`})}function pt(t){return s({subscriptionItemId:t}),r({path:`/v1/subscription-items/${t}/current-usage`})}function mt(t={}){return r({path:`/v1/subscription-items${a(t)}`})}function dt(t,e){return Ht(t,e)}async function Ht(t,e){s({subscriptionItemId:t});let o;if(typeof e=="number")o={quantity:e};else{let{quantity:i,invoiceImmediately:c=!1,disableProrations:p=!1}=e;o=u({quantity:i,invoiceImmediately:c,disableProrations:p})}return r({path:`/v1/subscription-items/${t}`,method:"PATCH",body:{data:{type:"subscription-items",id:t.toString(),attributes:o}}})}function yt(t,e={}){return s({usageRecordId:t}),r({path:`/v1/usage-records/${t}${n(e.include)}`})}function ht(t={}){return r({path:`/v1/usage-records${a(t)}`})}function ft(t){let{quantity:e,action:o="increment",subscriptionItemId:i}=t;return s({quantity:e,subscriptionItemId:i}),r({path:"/v1/usage-records",method:"POST",body:{data:{type:"usage-records",attributes:{quantity:e,action:o},relationships:{"subscription-item":{data:{type:"subscription-items",id:i.toString()}}}}}})}function gt(t){let{storeId:e,variantIds:o,name:i,amount:c,amountType:p="fixed",code:d=G(),isLimitedToProducts:y=!1,isLimitedRedemptions:h=!1,maxRedemptions:m=0,startsAt:b=null,expiresAt:f=null,duration:g="once",durationInMonths:l=1,testMode:L}=t;s({storeId:e,name:i,code:d,amount:c});let P=u({name:i,amount:c,amountType:p,code:d,isLimitedRedemptions:h,isLimitedToProducts:y,maxRedemptions:m,startsAt:b,expiresAt:f,duration:g,durationInMonths:l,testMode:L}),S={store:{data:{type:"stores",id:e.toString()}}};return o&&o.length>0&&(S.variants={data:o.map(x=>({type:"variants",id:x.toString()}))}),r({path:"/v1/discounts",method:"POST",body:{data:{type:"discounts",attributes:P,relationships:S}}})}function bt(t={}){return r({path:`/v1/discounts${a(t)}`})}function Pt(t,e={}){return s({discountId:t}),r({path:`/v1/discounts/${t}${n(e.include)}`})}function Lt(t){return s({discountId:t}),r({path:`/v1/discounts/${t}`,method:"DELETE"})}function St(t,e={}){return s({discountRedemptionId:t}),r({path:`/v1/discount-redemptions/${t}${n(e.include)}`})}function vt(t={}){return r({path:`/v1/discount-redemptions${a(t)}`})}function lt(t,e={}){return s({licenseKeyId:t}),r({path:`/v1/license-keys/${t}${n(e.include)}`})}function xt(t={}){return r({path:`/v1/license-keys${a(t)}`})}function kt(t,e){s({licenseKeyId:t});let{activationLimit:o,disabled:i=!1,expiresAt:c}=e,p=u({activationLimit:o,disabled:i,expiresAt:c});return r({path:`/v1/license-keys/${t}`,method:"PATCH",body:{data:{type:"license-keys",id:t.toString(),attributes:p}}})}function $t(t,e={}){return s({licenseKeyInstanceId:t}),r({path:`/v1/license-key-instances/${t}${n(e.include)}`})}function Ct(t={}){return r({path:`/v1/license-key-instances${a(t)}`})}function It(t,e,o={}){s({storeId:t,variantId:e});let{customPrice:i,productOptions:c,checkoutOptions:p,checkoutData:d,expiresAt:y,preview:h,testMode:m}=o,b={store:{data:{type:"stores",id:t.toString()}},variant:{data:{type:"variants",id:e.toString()}}},f={customPrice:i,expiresAt:y,preview:h,testMode:m,productOptions:c,checkoutOptions:p,checkoutData:{...d,variantQuantities:d?.variantQuantities?.map(g=>u(g))}};return r({path:"/v1/checkouts",method:"POST",body:{data:{type:"checkouts",attributes:u(f),relationships:b}}})}function Tt(t,e={}){return s({checkoutId:t}),r({path:`/v1/checkouts/${t}${n(e.include)}`})}function Ot(t={}){return r({path:`/v1/checkouts${a(t)}`})}function Gt(t,e){s({storeId:t});let{url:o,events:i,secret:c,testMode:p}=e;return r({path:"/v1/webhooks",method:"POST",body:{data:{type:"webhooks",attributes:u({url:o,events:i,secret:c,testMode:p}),relationships:{store:{data:{type:"stores",id:t.toString()}}}}}})}function Rt(t,e={}){return s({webhookId:t}),r({path:`/v1/webhooks/${t}${n(e.include)}`})}function Ut(t,e){s({webhookId:t});let{url:o,events:i,secret:c}=e;return r({path:`/v1/webhooks/${t}`,method:"PATCH",body:{data:{id:t.toString(),type:"webhooks",attributes:u({url:o,events:i,secret:c})}}})}function Dt(t){return s({webhookId:t}),r({path:`/v1/webhooks/${t}`,method:"DELETE"})}function Kt(t={}){return r({path:`/v1/webhooks${a(t)}`})}async function wt(t,e){return s({licenseKey:t,instanceName:e}),r({path:"/v1/licenses/activate",method:"POST",body:u({licenseKey:t,instanceName:e})},!1)}async function Qt(t,e){return s({licenseKey:t}),r({path:"/v1/licenses/validate",method:"POST",body:u({licenseKey:t,instanceId:e})},!1)}async function qt(t,e){return s({licenseKey:t,instanceId:e}),r({path:"/v1/licenses/deactivate",method:"POST",body:u({licenseKey:t,instanceId:e})},!1)}0&&(module.exports={activateLicense,archiveCustomer,cancelSubscription,createCheckout,createCustomer,createDiscount,createUsageRecord,createWebhook,deactivateLicense,deleteDiscount,deleteWebhook,generateOrderInvoice,generateSubscriptionInvoice,getAuthenticatedUser,getCheckout,getCustomer,getDiscount,getDiscountRedemption,getFile,getLicenseKey,getLicenseKeyInstance,getOrder,getOrderItem,getPrice,getProduct,getStore,getSubscription,getSubscriptionInvoice,getSubscriptionItem,getSubscriptionItemCurrentUsage,getUsageRecord,getVariant,getWebhook,issueOrderRefund,issueSubscriptionInvoiceRefund,lemonSqueezySetup,listCheckouts,listCustomers,listDiscountRedemptions,listDiscounts,listFiles,listLicenseKeyInstances,listLicenseKeys,listOrderItems,listOrders,listPrices,listProducts,listStores,listSubscriptionInvoices,listSubscriptionItems,listSubscriptions,listUsageRecords,listVariants,listWebhooks,updateCustomer,updateLicenseKey,updateSubscription,updateSubscriptionItem,updateWebhook,validateLicense});
