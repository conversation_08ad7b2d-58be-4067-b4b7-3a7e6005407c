// This file is the single source of truth for creating business plan prompts.

// --- Import all individual prompt builders from their dedicated files ---
import { buildOverviewPromptPart } from './Prompts/OverviewPrompt.js';
import { buildMarketAnalysisPromptPart } from './Prompts/MarketAnalysisPrompt.js';
import { buildMarketingStrategyPromptPart } from './Prompts/MarketingStrategyPrompt.js';
// NOTE: I am including placeholders for the prompt parts from previous steps for completeness.
import { buildOperationalPlanPromptPart } from './Prompts/OperationalPlanPrompt.js';

/**
 * Builds the full business plan prompt by assembling all parts and adding the final wrapper.
 * This is the ONLY function that should be exported from this file.
 * @param {object} formData - The user's form data.
 * @param {object} marketData - The fetched market analysis data.
 * @returns {string} The complete prompt for the AI.
 */
export const buildFullBusinessPlanPrompt = (formData, marketData = null) => {
    const { planType, idea, audience, profit, problem, language, name, period, budget, industry, keywords } = formData;
    
    // --- NEW LOGIC START ---

    let visionContext;
    let ideaForAnalysis;

    // Conditionally build the context based on whether the user wants the AI to generate an idea.
    if (planType === 'ai') {
        // This is the "AI Magic" path. Instruct the AI to invent the idea first.
        visionContext = `
**Client's Vision (AI-Generation Task):**
- **Task:** You must first invent a creative, viable business idea.
- **User's Preferences for Idea Generation:**
    - **Investment Budget:** "${budget || 'Not specified'}"
    - **Industry Interest:** "${industry || 'Any'}"
    - **Keywords / Themes:** "${keywords || 'Any'}"
- **Instructions:** Based on these preferences, generate a compelling business idea. Then, for that generated idea, you must also define the problem it solves, its target audience, and a suitable revenue model. Use this newly created concept to fill out the entire business report below.
- **Project Name:** For the idea you invent, also generate a creative, brandable English name for it.
- **Plan Duration:** "${period}"
        `;
        // For subsequent prompt parts, the "idea" is the one the AI will generate.
        ideaForAnalysis = "[the business idea you just generated]";
    } else {
        // This is the original "Your Vision" path.
        const appNameInstruction = name || "[Generate a creative, brandable English name for the app]";
        visionContext = `
**Client's Vision (for your context):**
- **Project Name:** ${appNameInstruction}
- **Core Idea:** "${idea}"
- **Problem It Solves:** "${problem}"
- **Target Audience:** "${audience}"
- **Revenue Model:** "${profit}"
- **Plan Duration:** "${period}"
        `;
        // For subsequent prompt parts, the "idea" is the one provided by the user.
        ideaForAnalysis = idea;
    }

    // --- NEW LOGIC END ---

    // Assemble all the parts internally by calling the imported functions.
    // We pass `ideaForAnalysis` to the Market Analysis prompt builder.
    const overviewPrompt = buildOverviewPromptPart(formData);
    const analysisPrompt = buildMarketAnalysisPromptPart({ idea: ideaForAnalysis }, marketData);
    const marketingPrompt = buildMarketingStrategyPromptPart(formData);
    const operationalPrompt = buildOperationalPlanPromptPart(formData); // This is PART 5

    // Create the final prompt with all context and parts in the correct order
    return `
You are an expert business strategist and analyst. Your task is to generate a complete and comprehensive business report based on the client's vision.
Your final, entire response MUST be written in the following language: ${language}.
Adhere strictly to all formatting markers provided in the structure below (~H~, ~S~, ~T_H~, ~STACK~, etc.). Each part must be clearly separated by the "### PART X ###" marker.

${visionContext}

--- START OF REPORT ---
${overviewPrompt}
${analysisPrompt}
${marketingPrompt}
${operationalPrompt}
--- END OF REPORT ---

Begin the report now.
        `;
};