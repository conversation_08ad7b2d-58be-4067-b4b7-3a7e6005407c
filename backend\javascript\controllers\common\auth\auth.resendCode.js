// node_gemini_summarizer/controllers/auth/auth.resendCode.js
import User from '../../../models/User.js';
import VerificationCode from '../../../models/VerificationCode.js';
import { sendVerificationEmail } from '../../../config/mailer.js';
import { generateOTP } from './auth.helpers.js';
import { OTP_EXPIRY_MINUTES } from './auth.config.js';

export const resendUserVerificationCode = async (req, res) => {
    const { email } = req.body;
    if (!email) {
        return res.status(400).json({ error: 'Email is required.' });
    }

    try {
        const user = await User.findOne({ email });
        if (!user) {
            return res.status(404).json({ error: 'User with this email not found.' });
        }
        if (user.isVerified) {
            return res.status(400).json({ error: 'This email is already verified.' });
        }

        const otp = generateOTP();
        const expiresAt = new Date(Date.now() + OTP_EXPIRY_MINUTES * 60 * 1000);

        await VerificationCode.deleteMany({ email: user.email });
        const newCode = new VerificationCode({ email, code: otp, expiresAt });
        await newCode.save();

        try {
            await sendVerificationEmail(email, otp);
            res.status(200).json({ message: 'New verification code sent to your email.' });
        } catch (emailError) {
            console.error(`Failed to resend verification email to ${email}: ${emailError.message}`);
            res.status(500).json({ error: 'Failed to send verification email. Please try again.' });
        }

    } catch (error) {
        console.error('Resend verification code error:', error.message, error.stack);
        res.status(500).json({ error: 'Server error while resending verification code.' });
    }
};