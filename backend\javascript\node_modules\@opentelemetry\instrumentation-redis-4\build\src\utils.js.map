{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";;;AAgBA,8EAM6C;AAE7C,SAAgB,mBAAmB,CACjC,IAAgB,EAChB,OAAY;;IAEZ,OAAO;QACL,CAAC,yCAAkB,CAAC,EAAE,2CAAoB;QAC1C,CAAC,6CAAsB,CAAC,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,0CAAE,IAAI;QAC/C,CAAC,6CAAsB,CAAC,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,0CAAE,IAAI;QAC/C,CAAC,oDAA6B,CAAC,EAC7B,gDAAgD,CAAC,IAAI,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,CAAC;KACvE,CAAC;AACJ,CAAC;AAXD,kDAWC;AAED;;;;;;GAMG;AACH,SAAS,gDAAgD,CACvD,IAAgB,EAChB,GAAa;IAEb,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,GAAG,EAAE;QACnC,OAAO;KACR;IAED,IAAI;QACF,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QACvB,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAClC,CAAC,CAAC,QAAQ,GAAG,EAAE,CAAC;QAChB,CAAC,CAAC,QAAQ,GAAG,EAAE,CAAC;QAChB,OAAO,CAAC,CAAC,IAAI,CAAC;KACf;IAAC,OAAO,GAAG,EAAE;QACZ,IAAI,CAAC,KAAK,CAAC,yCAAyC,EAAE,GAAG,CAAC,CAAC;KAC5D;IACD,OAAO;AACT,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { Attributes, DiagLogger } from '@opentelemetry/api';\nimport {\n  SEMATTRS_DB_SYSTEM,\n  SEMATTRS_DB_CONNECTION_STRING,\n  SEMATTRS_NET_PEER_NAME,\n  SEMATTRS_NET_PEER_PORT,\n  DBSYSTEMVALUES_REDIS,\n} from '@opentelemetry/semantic-conventions';\n\nexport function getClientAttributes(\n  diag: DiagLogger,\n  options: any\n): Attributes {\n  return {\n    [SEMATTRS_DB_SYSTEM]: DBSYSTEMVALUES_REDIS,\n    [SEMATTRS_NET_PEER_NAME]: options?.socket?.host,\n    [SEMATTRS_NET_PEER_PORT]: options?.socket?.port,\n    [SEMATTRS_DB_CONNECTION_STRING]:\n      removeCredentialsFromDBConnectionStringAttribute(diag, options?.url),\n  };\n}\n\n/**\n * removeCredentialsFromDBConnectionStringAttribute removes basic auth from url and user_pwd from query string\n *\n * Examples:\n *   redis://user:pass@localhost:6379/mydb => redis://localhost:6379/mydb\n *   redis://localhost:6379?db=mydb&user_pwd=pass => redis://localhost:6379?db=mydb\n */\nfunction removeCredentialsFromDBConnectionStringAttribute(\n  diag: DiagLogger,\n  url?: unknown\n): string | undefined {\n  if (typeof url !== 'string' || !url) {\n    return;\n  }\n\n  try {\n    const u = new URL(url);\n    u.searchParams.delete('user_pwd');\n    u.username = '';\n    u.password = '';\n    return u.href;\n  } catch (err) {\n    diag.error('failed to sanitize redis connection url', err);\n  }\n  return;\n}\n"]}