// src/store/features/auth/authSlice.js
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

// Use environment variable for API base URL
  const API_BASE_URL = import.meta.env.VITE_NODE_BACKEND_URL || 'http://localhost:3001';

// Helper function to get initial state from localStorage
const loadInitialState = () => {
  let token = null;
  let user = null;
  let emailForVerification = null;

  if (typeof window !== 'undefined') { // Ensure localStorage is available (for SSR/testing)
    token = localStorage.getItem('authToken');
    const userString = localStorage.getItem('authUser');
    emailForVerification = localStorage.getItem('emailForVerification');

    if (userString) {
      try {
        user = JSON.parse(userString);
      } catch (e) {
        console.error("Error parsing stored user for Redux:", e);
        localStorage.removeItem('authUser'); // Clear corrupted data
        localStorage.removeItem('authToken'); // Also clear token if user data is bad
        token = null; // Reset token
        user = null;
      }
    }

    // If token exists but user doesn't, or vice-versa (inconsistent state), clear both
    if ((token && !user) || (!token && user)) {
        console.warn("Inconsistent auth state in localStorage. Clearing auth data.");
        localStorage.removeItem('authUser');
        localStorage.removeItem('authToken');
        user = null;
        token = null;
    }
  }

  return {
    user: user,
    token: token,
    isAuthenticated: !!token && !!user, // True only if both token and user are present
    isLoading: false,
    error: null,
    emailForVerification: emailForVerification,
  };
};

const initialState = loadInitialState();

// --- Async Thunks for API calls ---

export const loginUser = createAsyncThunk(
  'auth/loginUser',
  async ({ email, password }, { rejectWithValue }) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password }),
      });
      const data = await response.json();
      if (!response.ok) {
        return rejectWithValue(data.error || data.message || 'Login Failed');
      }
      if (typeof window !== 'undefined') {
        localStorage.setItem('authToken', data.token);
        localStorage.setItem('authUser', JSON.stringify(data.user)); // User object now includes subscription
        localStorage.removeItem('emailForVerification'); // Clear this on successful login
      }
      return data; // { user, token, message }
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const registerUser = createAsyncThunk(
  'auth/registerUser',
  async ({ name, email, password, isAdmin }, { rejectWithValue }) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name, email, password, isAdmin }),
      });
      const data = await response.json();
      if (!response.ok) {
        return rejectWithValue(data.error || data.message || 'Registration Failed');
      }
      if (typeof window !== 'undefined') {
        localStorage.setItem('emailForVerification', data.email); // Store for verify page
      }
      return data; // { message, email }
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const verifyUserEmail = createAsyncThunk(
  'auth/verifyUserEmail',
  async ({ email, verificationCode }, { rejectWithValue }) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/verify-email`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, verificationCode }),
      });
      const data = await response.json();
      if (!response.ok) {
        return rejectWithValue(data.error || data.message || 'Verification Failed');
      }
      if (typeof window !== 'undefined') {
        localStorage.setItem('authToken', data.token);
        localStorage.setItem('authUser', JSON.stringify(data.user)); // User object includes subscription
        localStorage.removeItem('emailForVerification');
      }
      return data; // { user, token, message }
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const resendVerificationCode = createAsyncThunk(
  'auth/resendVerificationCode',
  async (email, { rejectWithValue }) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/resend-verification`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email }),
      });
      const data = await response.json();
      if (!response.ok) {
        return rejectWithValue(data.error || data.message || 'Failed to resend code');
      }
      return data; // { message }
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const incrementUserUploadCountThunk = createAsyncThunk(
  'auth/incrementUserUploadCount',
  async (_, { getState, rejectWithValue }) => {
    const token = getState().auth.token;
    if (!token) {
      return rejectWithValue('User not authenticated to increment count.');
    }
    try {
      const response = await fetch(`${API_BASE_URL}/api/users/me/increment-upload-count`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });
      const data = await response.json(); // Expects { subscription: {...} } on success
      if (!response.ok) {
        return rejectWithValue(data.message || 'Failed to increment upload count');
      }
      return data.subscription; // This is the updated subscription object
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const incrementBusinessPlanCountThunk = createAsyncThunk(
  'auth/incrementBusinessPlanCount',
  async (_, { getState, rejectWithValue }) => {
    const token = getState().auth.token;
    if (!token) {
      return rejectWithValue('User not authenticated to increment business plan count.');
    }
    try {
      const response = await fetch(`${API_BASE_URL}/api/users/me/increment-plan-count`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });
      const data = await response.json(); // Expects { subscription: {...} } on success
      if (!response.ok) {
        return rejectWithValue(data.message || 'Failed to increment business plan count');
      }
      return data.subscription; // This is the updated subscription object
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const incrementInvestorPitchCountThunk = createAsyncThunk(
  'auth/incrementInvestorPitchCount',
  async (_, { getState, rejectWithValue }) => {
    const token = getState().auth.token;
    if (!token) {
      return rejectWithValue('User not authenticated to increment investor pitch count.');
    }
    try {
      const response = await fetch(`${API_BASE_URL}/api/users/me/increment-pitch-count`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });
      const data = await response.json(); // Expects { subscription: {...} } on success
      if (!response.ok) {
        return rejectWithValue(data.message || 'Failed to increment investor pitch count');
      }
      return data.subscription; // This is the updated subscription object
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const incrementBusinessQACountThunk = createAsyncThunk(
  'auth/incrementBusinessQACount',
  async (_, { getState, rejectWithValue }) => {
    const token = getState().auth.token;
    if (!token) {
      return rejectWithValue('User not authenticated to increment business Q&A count.');
    }
    try {
      const response = await fetch(`${API_BASE_URL}/api/users/me/increment-qa-count`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });
      const data = await response.json(); // Expects { subscription: {...} } on success
      if (!response.ok) {
        return rejectWithValue(data.message || 'Failed to increment business Q&A count');
      }
      return data.subscription; // This is the updated subscription object
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchCurrentUser = createAsyncThunk(
  'auth/fetchCurrentUser',
  async (_, { getState, rejectWithValue }) => {
    const token = getState().auth.token;
    if (!token) {
      return rejectWithValue('No token available. Cannot fetch user.');
    }
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/me`, { // Your "get current user" endpoint
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      const data = await response.json(); // Expects { user: {...} } on success
      if (!response.ok) {
        return rejectWithValue(data.error || data.message || 'Failed to fetch user data');
      }
      if (typeof window !== 'undefined') {
         localStorage.setItem('authUser', JSON.stringify(data.user));
      }
      return data.user; // Return the user object (which includes subscription)
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// --- Redux Slice ---
export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    logout: (state) => {
      if (typeof window !== 'undefined') {
        localStorage.removeItem('authToken');
        localStorage.removeItem('authUser');
        localStorage.removeItem('emailForVerification');
      }
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
      state.error = null;
      state.emailForVerification = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    setEmailForVerificationState: (state, action) => {
        state.emailForVerification = action.payload;
        if (typeof window !== 'undefined') {
            if (action.payload) {
                localStorage.setItem('emailForVerification', action.payload);
            } else {
                localStorage.removeItem('emailForVerification');
            }
        }
    },
    updateUserSubscription: (state, action) => { // For manual/optimistic updates
      if (state.user && action.payload) {
        state.user.subscription = {
          ...(state.user.subscription || {}), // Preserve existing fields
          ...action.payload, // Merge new fields from payload
        };
        if (typeof window !== 'undefined') {
          localStorage.setItem('authUser', JSON.stringify(state.user));
        }
      }
    }
  },
  extraReducers: (builder) => {
    builder
      // Login
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true; state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false; state.isAuthenticated = true;
        state.user = action.payload.user; state.token = action.payload.token;
        state.error = null; state.emailForVerification = null; // Clear this field
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false; state.error = action.payload;
        state.isAuthenticated = false; state.user = null; state.token = null;
      })
      // Register
      .addCase(registerUser.pending, (state) => {
        state.isLoading = true; state.error = null;
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.isLoading = false; state.error = null;
        state.emailForVerification = action.payload.email;
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.isLoading = false; state.error = action.payload;
      })
      // Verify Email
      .addCase(verifyUserEmail.pending, (state) => {
        state.isLoading = true; state.error = null;
      })
      .addCase(verifyUserEmail.fulfilled, (state, action) => {
        state.isLoading = false; state.isAuthenticated = true;
        state.user = action.payload.user; state.token = action.payload.token;
        state.error = null; state.emailForVerification = null; // Clear this field
      })
      .addCase(verifyUserEmail.rejected, (state, action) => {
        state.isLoading = false; state.error = action.payload;
      })
      // Resend Verification Code
      .addCase(resendVerificationCode.pending, (state) => {
        state.isLoading = true; state.error = null;
      })
      .addCase(resendVerificationCode.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(resendVerificationCode.rejected, (state, action) => {
        state.isLoading = false; state.error = action.payload;
      })
      // Increment Upload Count
      .addCase(incrementUserUploadCountThunk.pending, (state) => {
        // Optional: state.isUpdatingCount = true;
      })
      .addCase(incrementUserUploadCountThunk.fulfilled, (state, action) => {
        // state.isUpdatingCount = false;
        if (state.user) {
          state.user.subscription = action.payload; // action.payload IS the updated subscription
          if (typeof window !== 'undefined') {
            localStorage.setItem('authUser', JSON.stringify(state.user));
          }
        }
      })
      .addCase(incrementUserUploadCountThunk.rejected, (state, action) => {
        // state.isUpdatingCount = false;
        state.error = action.payload; // Or a specific error field
        console.error("Redux: Increment upload count failed:", action.payload);
      })
      // Increment Business Plan Count
      .addCase(incrementBusinessPlanCountThunk.pending, (state) => {
        // Optional: state.isUpdatingCount = true;
      })
      .addCase(incrementBusinessPlanCountThunk.fulfilled, (state, action) => {
        // state.isUpdatingCount = false;
        if (state.user) {
          state.user.subscription = action.payload; // action.payload IS the updated subscription
          if (typeof window !== 'undefined') {
            localStorage.setItem('authUser', JSON.stringify(state.user));
          }
        }
      })
      .addCase(incrementBusinessPlanCountThunk.rejected, (state, action) => {
        // state.isUpdatingCount = false;
        state.error = action.payload; // Or a specific error field
        console.error("Redux: Increment business plan count failed:", action.payload);
      })
      // Increment Investor Pitch Count
      .addCase(incrementInvestorPitchCountThunk.pending, (state) => {
        // Optional: state.isUpdatingCount = true;
      })
      .addCase(incrementInvestorPitchCountThunk.fulfilled, (state, action) => {
        // state.isUpdatingCount = false;
        if (state.user) {
          state.user.subscription = action.payload; // action.payload IS the updated subscription
          if (typeof window !== 'undefined') {
            localStorage.setItem('authUser', JSON.stringify(state.user));
          }
        }
      })
      .addCase(incrementInvestorPitchCountThunk.rejected, (state, action) => {
        // state.isUpdatingCount = false;
        state.error = action.payload; // Or a specific error field
        console.error("Redux: Increment investor pitch count failed:", action.payload);
      })
      // Increment Business Q&A Count
      .addCase(incrementBusinessQACountThunk.pending, (state) => {
        // Optional: state.isUpdatingCount = true;
      })
      .addCase(incrementBusinessQACountThunk.fulfilled, (state, action) => {
        // state.isUpdatingCount = false;
        if (state.user) {
          state.user.subscription = action.payload; // action.payload IS the updated subscription
          if (typeof window !== 'undefined') {
            localStorage.setItem('authUser', JSON.stringify(state.user));
          }
        }
      })
      .addCase(incrementBusinessQACountThunk.rejected, (state, action) => {
        // state.isUpdatingCount = false;
        state.error = action.payload; // Or a specific error field
        console.error("Redux: Increment business Q&A count failed:", action.payload);
      })
      // Fetch Current User
      .addCase(fetchCurrentUser.pending, (state) => {
        state.isLoading = true; state.error = null;
      })
      .addCase(fetchCurrentUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload; // Full user object including subscription
        state.isAuthenticated = true; // Reaffirm authentication
        state.error = null;
        // localStorage is updated within the thunk itself for this one
      })
      .addCase(fetchCurrentUser.rejected, (state, action) => {
        state.isLoading = false; state.error = action.payload;
        // Critical failure to fetch user might mean token is invalid
        // Consider logging out the user in this case.
        // This is a design decision based on how your backend handles invalid tokens.
        // Example: if (action.payload === 'Invalid token' || action.payload === 'User not found') {
        //   localStorage.clear(); state.user = null; state.token = null; state.isAuthenticated = false;
        // }
        console.error("Redux: Fetch current user failed:", action.payload);
      });
  },
}); // This closes the createSlice call

export const { logout, clearError, setEmailForVerificationState, updateUserSubscription } = authSlice.actions;

// --- Selectors ---
export const selectCurrentUser = (state) => state.auth.user;
export const selectAuthToken = (state) => state.auth.token;
export const selectIsAuthenticated = (state) => state.auth.isAuthenticated;
export const selectAuthIsLoading = (state) => state.auth.isLoading;
export const selectAuthError = (state) => state.auth.error;
export const selectEmailForVerification = (state) => state.auth.emailForVerification;

export default authSlice.reducer;