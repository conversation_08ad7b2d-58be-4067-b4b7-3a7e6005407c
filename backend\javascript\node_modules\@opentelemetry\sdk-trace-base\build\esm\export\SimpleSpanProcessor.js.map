{"version": 3, "file": "SimpleSpanProcessor.js", "sourceRoot": "", "sources": ["../../../src/export/SimpleSpanProcessor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,EAAW,UAAU,EAAE,MAAM,oBAAoB,CAAC;AACzD,OAAO,EACL,QAAQ,EACR,gBAAgB,EAChB,kBAAkB,EAClB,cAAc,GAEf,MAAM,qBAAqB,CAAC;AAO7B;;;;;;;GAOG;AACH;IAIE,6BAA6B,SAAuB;QAAvB,cAAS,GAAT,SAAS,CAAc;QAClD,IAAI,CAAC,aAAa,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAC9D,IAAI,CAAC,kBAAkB,GAAG,IAAI,GAAG,EAAiB,CAAC;IACrD,CAAC;IAEK,wCAAU,GAAhB;;;;;oBACE,8CAA8C;oBAC9C,qBAAM,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAA;;wBADtD,8CAA8C;wBAC9C,SAAsD,CAAC;6BACnD,IAAI,CAAC,SAAS,CAAC,UAAU,EAAzB,wBAAyB;wBAC3B,qBAAM,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,EAAA;;wBAAjC,SAAiC,CAAC;;;;;;KAErC;IAED,qCAAO,GAAP,UAAQ,KAAW,EAAE,cAAuB,IAAS,CAAC;IAEtD,mCAAK,GAAL,UAAM,IAAkB;QAAxB,iBA+CC;;QA9CC,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,OAAO;SACR;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC9D,OAAO;SACR;QAED,IAAM,QAAQ,GAAG;YACf,OAAA,QAAQ;iBACL,OAAO,CAAC,KAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC;iBAC/B,IAAI,CAAC,UAAC,MAAoB;;gBACzB,IAAI,MAAM,CAAC,IAAI,KAAK,gBAAgB,CAAC,OAAO,EAAE;oBAC5C,kBAAkB,CAChB,MAAA,MAAM,CAAC,KAAK,mCACV,IAAI,KAAK,CACP,qDAAmD,MAAM,MAAG,CAC7D,CACJ,CAAC;iBACH;YACH,CAAC,CAAC;iBACD,KAAK,CAAC,UAAA,KAAK;gBACV,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC5B,CAAC,CAAC;QAdJ,CAcI,CAAC;QAEP,sFAAsF;QACtF,IAAI,IAAI,CAAC,QAAQ,CAAC,sBAAsB,EAAE;YACxC,IAAM,eAAa,GAAG,MAAA,MAAC,IAAI,CAAC,QAAqB,EAC9C,sBAAsB,mDACtB,IAAI,CACH;gBACE,IAAI,eAAa,IAAI,IAAI,EAAE;oBACzB,KAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,eAAa,CAAC,CAAC;iBAC/C;gBACD,OAAO,QAAQ,EAAE,CAAC;YACpB,CAAC,EACD,UAAA,GAAG,IAAI,OAAA,kBAAkB,CAAC,GAAG,CAAC,EAAvB,CAAuB,CAC/B,CAAC;YAEJ,+BAA+B;YAC/B,IAAI,eAAa,IAAI,IAAI,EAAE;gBACzB,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,eAAa,CAAC,CAAC;aAC5C;SACF;aAAM;YACL,KAAK,QAAQ,EAAE,CAAC;SACjB;IACH,CAAC;IAED,sCAAQ,GAAR;QACE,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAEO,uCAAS,GAAjB;QACE,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;IACnC,CAAC;IACH,0BAAC;AAAD,CAAC,AA3ED,IA2EC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, TraceFlags } from '@opentelemetry/api';\nimport {\n  internal,\n  ExportResultCode,\n  globalErrorHandler,\n  BindOnceFuture,\n  ExportResult,\n} from '@opentelemetry/core';\nimport { Span } from '../Span';\nimport { SpanProcessor } from '../SpanProcessor';\nimport { ReadableSpan } from './ReadableSpan';\nimport { SpanExporter } from './SpanExporter';\nimport { Resource } from '@opentelemetry/resources';\n\n/**\n * An implementation of the {@link SpanProcessor} that converts the {@link Span}\n * to {@link ReadableSpan} and passes it to the configured exporter.\n *\n * Only spans that are sampled are converted.\n *\n * NOTE: This {@link SpanProcessor} exports every ended span individually instead of batching spans together, which causes significant performance overhead with most exporters. For production use, please consider using the {@link BatchSpanProcessor} instead.\n */\nexport class SimpleSpanProcessor implements SpanProcessor {\n  private _shutdownOnce: BindOnceFuture<void>;\n  private _unresolvedExports: Set<Promise<void>>;\n\n  constructor(private readonly _exporter: SpanExporter) {\n    this._shutdownOnce = new BindOnceFuture(this._shutdown, this);\n    this._unresolvedExports = new Set<Promise<void>>();\n  }\n\n  async forceFlush(): Promise<void> {\n    // await unresolved resources before resolving\n    await Promise.all(Array.from(this._unresolvedExports));\n    if (this._exporter.forceFlush) {\n      await this._exporter.forceFlush();\n    }\n  }\n\n  onStart(_span: Span, _parentContext: Context): void {}\n\n  onEnd(span: ReadableSpan): void {\n    if (this._shutdownOnce.isCalled) {\n      return;\n    }\n\n    if ((span.spanContext().traceFlags & TraceFlags.SAMPLED) === 0) {\n      return;\n    }\n\n    const doExport = () =>\n      internal\n        ._export(this._exporter, [span])\n        .then((result: ExportResult) => {\n          if (result.code !== ExportResultCode.SUCCESS) {\n            globalErrorHandler(\n              result.error ??\n                new Error(\n                  `SimpleSpanProcessor: span export failed (status ${result})`\n                )\n            );\n          }\n        })\n        .catch(error => {\n          globalErrorHandler(error);\n        });\n\n    // Avoid scheduling a promise to make the behavior more predictable and easier to test\n    if (span.resource.asyncAttributesPending) {\n      const exportPromise = (span.resource as Resource)\n        .waitForAsyncAttributes?.()\n        .then(\n          () => {\n            if (exportPromise != null) {\n              this._unresolvedExports.delete(exportPromise);\n            }\n            return doExport();\n          },\n          err => globalErrorHandler(err)\n        );\n\n      // store the unresolved exports\n      if (exportPromise != null) {\n        this._unresolvedExports.add(exportPromise);\n      }\n    } else {\n      void doExport();\n    }\n  }\n\n  shutdown(): Promise<void> {\n    return this._shutdownOnce.call();\n  }\n\n  private _shutdown(): Promise<void> {\n    return this._exporter.shutdown();\n  }\n}\n"]}