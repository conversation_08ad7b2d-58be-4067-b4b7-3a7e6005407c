import User from '../../../../models/User.js';
import {
    ensureSubscription,
    FREE_TIER_PLAN_NAME_BACKEND,
    PRO_PLAN_NAME_BACKEND,
} from './planConfig.js';

/**
 * @desc    Increment message count for applicable plans
 * @route   POST /api/users/me/increment-message-count
 * @access  Private
 */
export const incrementMessageCount = async (req, res) => {
  try {
    let user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    user = ensureSubscription(user);

    let message = 'Message count incremented.';
    let countIncremented = false;

    if (user.subscription.planName === FREE_TIER_PLAN_NAME_BACKEND) {
      user.subscription.freeTierMessageCount = (user.subscription.freeTierMessageCount || 0) + 1;
      countIncremented = true;
    } else if (user.subscription.planName === PRO_PLAN_NAME_BACKEND) {
      user.subscription.proTierMessageCount = (user.subscription.proTierMessageCount || 0) + 1;
      countIncremented = true;
    }

    if (countIncremented) {
      await user.save();
    }

    res.json({
      success: true,
      message: message,
      subscription: user.subscription,
    });

  } catch (error) {
    console.error('Increment Message Count Error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Server error while incrementing message count.' 
    });
  }
};