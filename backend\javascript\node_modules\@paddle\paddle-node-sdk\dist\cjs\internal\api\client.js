"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Client = void 0;
const environment_js_1 = require("./environment.js");
const constants_js_1 = require("./constants.js");
const logger_js_1 = require("../base/logger.js");
const log_level_js_1 = require("./log-level.js");
const runtime_provider_js_1 = require("../providers/runtime-provider.js");
const version_js_1 = require("../../version.js");
const case_helpers_js_1 = require("./case-helpers.js");
class Client {
    constructor(apiKey, options) {
        var _a;
        this.apiKey = apiKey;
        this.options = options;
        this.baseUrl = this.getBaseUrl(this.options.environment);
        logger_js_1.Logger.logLevel = (_a = this.options.logLevel) !== null && _a !== void 0 ? _a : log_level_js_1.LogLevel.verbose;
    }
    getBaseUrl(environment) {
        const urlBasedOnEnv = constants_js_1.API_ENVIRONMENT_TO_BASE_URL_MAP[environment !== null && environment !== void 0 ? environment : environment_js_1.Environment.production];
        return urlBasedOnEnv || environment;
    }
    getHeaders() {
        var _a;
        let uuid;
        const cryptoProvider = (_a = runtime_provider_js_1.RuntimeProvider.getProvider()) === null || _a === void 0 ? void 0 : _a.crypto;
        if (cryptoProvider) {
            uuid = cryptoProvider.randomUUID();
        }
        else {
            logger_js_1.Logger.error('Unknown runtime. Cannot generate uuid');
        }
        return Object.assign({ Authorization: `bearer ${this.apiKey}`, 'Content-Type': 'application/json', 'user-agent': `PaddleSDK/node ${version_js_1.SDK_VERSION}`, 'X-Transaction-ID': uuid !== null && uuid !== void 0 ? uuid : '' }, this.options.customHeaders);
    }
    get(url, queryParams) {
        return __awaiter(this, void 0, void 0, function* () {
            let finalUrl = url.includes(this.baseUrl) ? url : `${this.baseUrl}${url}`;
            if (!finalUrl.includes('?') && queryParams) {
                finalUrl += queryParams.toQueryString();
            }
            const logUrl = finalUrl.split('?')[0];
            const headers = this.getHeaders();
            logger_js_1.Logger.logRequest('GET', logUrl, headers);
            const rawResponse = yield fetch(finalUrl, {
                headers,
            });
            logger_js_1.Logger.logResponse('GET', logUrl, headers, rawResponse);
            return rawResponse.json();
        });
    }
    post(url, requestBody) {
        return __awaiter(this, void 0, void 0, function* () {
            const logUrl = url.split('?')[0];
            const headers = this.getHeaders();
            logger_js_1.Logger.logRequest('POST', logUrl, headers);
            const rawResponse = yield fetch(`${this.baseUrl}${url}`, {
                method: 'POST',
                body: JSON.stringify((0, case_helpers_js_1.convertToSnakeCase)(requestBody)),
                headers,
            });
            logger_js_1.Logger.logResponse('POST', logUrl, headers, rawResponse);
            return rawResponse.json();
        });
    }
    patch(url, requestBody) {
        return __awaiter(this, void 0, void 0, function* () {
            const logUrl = url.split('?')[0];
            const headers = this.getHeaders();
            logger_js_1.Logger.logRequest('PATCH', logUrl, headers);
            const rawResponse = yield fetch(`${this.baseUrl}${url}`, {
                method: 'PATCH',
                body: JSON.stringify((0, case_helpers_js_1.convertToSnakeCase)(requestBody)),
                headers,
            });
            logger_js_1.Logger.logResponse('PATCH', logUrl, headers, rawResponse);
            return rawResponse.json();
        });
    }
    delete(url) {
        return __awaiter(this, void 0, void 0, function* () {
            const logUrl = url.split('?')[0];
            const headers = this.getHeaders();
            logger_js_1.Logger.logRequest('DELETE', logUrl, headers);
            const rawResponse = yield fetch(`${this.baseUrl}${url}`, {
                method: 'DELETE',
                headers,
            });
            logger_js_1.Logger.logResponse('DELETE', logUrl, headers, rawResponse);
            return rawResponse;
        });
    }
}
exports.Client = Client;
