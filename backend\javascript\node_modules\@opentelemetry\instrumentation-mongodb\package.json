{"name": "@opentelemetry/instrumentation-mongodb", "version": "0.52.0", "description": "OpenTelemetry instrumentation for `mongodb` database client for MongoDB", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"docker:start": "docker run -e MONGODB_DB=opentelemetry-tests -e MONGODB_PORT=27017 -e MONGODB_HOST=127.0.0.1 -p 27017:27017 --rm mongo", "test": "npm run test-v3 && npm run test-v4 && npm run test-v5-v6 && nyc merge .nyc_output ./coverage/coverage-final.json", "test-v3": "tav mongodb 3.7.4 npm run test-v3-run", "test-v4": "tav mongodb 4.17.0 npm run test-v4-run", "test-v5-v6": "npm run test-v5-v6-run", "test-v3-run": "nyc --no-clean mocha --require '@opentelemetry/contrib-test-utils' 'test/**/mongodb-v3.test.ts'", "test-v4-run": "nyc --no-clean mocha --require '@opentelemetry/contrib-test-utils' 'test/mongodb-v4-v5-v6.metrics.test.ts' 'test/**/mongodb-v4.test.ts'", "test-v5-v6-run": "nyc --no-clean mocha --require '@opentelemetry/contrib-test-utils' 'test/mongodb-v4-v5-v6.metrics.test.ts' 'test/**/mongodb-v5-v6.test.ts'", "test-all-versions": "tav", "tdd": "npm run test-v5-v6-run -- --watch-extensions ts --watch", "clean": "rimraf build/*", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "lint:readme": "node ../../../scripts/lint-readme.js", "prewatch": "npm run precompile", "version:update": "node ../../../scripts/version-update.js", "compile": "tsc -p .", "prepublishOnly": "npm run compile", "watch": "tsc -w"}, "keywords": ["mongodb", "nodejs", "opentelemetry", "plugin", "profiling", "tracing"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "devDependencies": {"@opentelemetry/api": "^1.3.0", "@opentelemetry/context-async-hooks": "^1.8.0", "@opentelemetry/contrib-test-utils": "^0.45.1", "@opentelemetry/sdk-metrics": "^1.9.1", "@opentelemetry/sdk-trace-base": "^1.8.0", "@opentelemetry/sdk-trace-node": "^1.8.0", "@types/bson": "4.0.5", "@types/mocha": "7.0.2", "@types/node": "18.18.14", "mongodb": "6.8.0", "nyc": "15.1.0", "rimraf": "5.0.10", "test-all-versions": "6.1.0", "typescript": "4.4.4"}, "dependencies": {"@opentelemetry/instrumentation": "^0.57.1", "@opentelemetry/semantic-conventions": "^1.27.0"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/opentelemetry-instrumentation-mongodb#readme", "gitHead": "1eb77007669bae87fe5664d68ba6533b95275d52"}