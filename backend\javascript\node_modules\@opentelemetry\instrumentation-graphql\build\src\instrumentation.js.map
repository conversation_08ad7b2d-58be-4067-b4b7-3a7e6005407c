{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAAoD;AACpD,oEAMwC;AAExC,iCAAmC;AACnC,2DAAwD;AACxD,uCAAqD;AAErD,qDAU0B;AAC1B,mCAQiB;AAEjB,kBAAkB;AAClB,uCAA0D;AAQ1D,MAAM,cAAc,GAAuC;IACzD,UAAU,EAAE,KAAK;IACjB,KAAK,EAAE,CAAC,CAAC;IACT,WAAW,EAAE,KAAK;IAClB,kBAAkB,EAAE,KAAK;CAC1B,CAAC;AAEF,MAAM,iBAAiB,GAAG,CAAC,cAAc,CAAC,CAAC;AAE3C,MAAa,sBAAuB,SAAQ,qCAAuD;IACjG,YAAY,SAAuC,EAAE;QACnD,KAAK,CAAC,sBAAY,EAAE,yBAAe,kCAAO,cAAc,GAAK,MAAM,EAAG,CAAC;IACzE,CAAC;IAEQ,SAAS,CAAC,SAAuC,EAAE;QAC1D,KAAK,CAAC,SAAS,iCAAM,cAAc,GAAK,MAAM,EAAG,CAAC;IACpD,CAAC;IAES,IAAI;QACZ,MAAM,MAAM,GAAG,IAAI,qDAAmC,CACpD,SAAS,EACT,iBAAiB,CAClB,CAAC;QACF,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC;QAC9C,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAC7C,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC;QAE/C,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,mBAAmB;QACzB,OAAO,IAAI,+CAA6B,CACtC,8BAA8B,EAC9B,iBAAiB;QACjB,sEAAsE;QACtE,iDAAiD;QACjD,CAAC,aAAkB,EAAE,EAAE;YACrB,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,EAAE;gBACpC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;aACxC;YACD,IAAI,CAAC,KAAK,CACR,aAAa,EACb,SAAS,EACT,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,oBAAoB,CAAC,CACvD,CAAC;YACF,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,aAAa,CAAC,EAAE;YACd,IAAI,aAAa,EAAE;gBACjB,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;aACxC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAEO,kBAAkB;QACxB,OAAO,IAAI,+CAA6B,CACtC,4BAA4B,EAC5B,iBAAiB,EACjB,CAAC,aAAkC,EAAE,EAAE;YACrC,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,KAAK,CAAC,EAAE;gBAClC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;aACtC;YACD,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;YACvD,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,CAAC,aAAkC,EAAE,EAAE;YACrC,IAAI,aAAa,EAAE;gBACjB,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;aACtC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAEO,oBAAoB;QAC1B,OAAO,IAAI,+CAA6B,CACtC,gCAAgC,EAChC,iBAAiB,EACjB,aAAa,CAAC,EAAE;YACd,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;gBACrC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;aACzC;YACD,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;YAC7D,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,aAAa,CAAC,EAAE;YACd,IAAI,aAAa,EAAE;gBACjB,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;aACzC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAEO,aAAa,CACnB,oBAAiE;QAEjE,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,SAAS,OAAO,CAAC,QAAQ;YAC9B,OAAO,SAAS,YAAY;gBAG1B,IAAI,aAAgC,CAAC;gBAErC,8CAA8C;gBAC9C,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE;oBACzB,MAAM,IAAI,GAAG,SAA6C,CAAC;oBAC3D,aAAa,GAAG,eAAe,CAAC,gBAAgB,CAC9C,IAAI,CAAC,CAAC,CAAC,EACP,IAAI,CAAC,CAAC,CAAC,EACP,IAAI,CAAC,CAAC,CAAC,EACP,IAAI,CAAC,CAAC,CAAC,EACP,IAAI,CAAC,CAAC,CAAC,EACP,IAAI,CAAC,CAAC,CAAC,EACP,IAAI,CAAC,CAAC,CAAC,EACP,IAAI,CAAC,CAAC,CAAC,EACP,oBAAoB,CACrB,CAAC;iBACH;qBAAM;oBACL,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAA+B,CAAC;oBACxD,aAAa,GAAG,eAAe,CAAC,gBAAgB,CAC9C,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,YAAY,EACjB,oBAAoB,CACrB,CAAC;iBACH;gBAED,MAAM,SAAS,GAAG,IAAA,oBAAY,EAC5B,aAAa,CAAC,QAAQ,EACtB,aAAa,CAAC,aAAa,CAC5B,CAAC;gBAEF,MAAM,IAAI,GAAG,eAAe,CAAC,kBAAkB,CAC7C,SAAS,EACT,aAAa,CACd,CAAC;gBAEF,aAAa,CAAC,YAAY,CAAC,kCAAwB,CAAC,GAAG;oBACrD,MAAM,EAAE,aAAa,CAAC,QAAQ;wBAC5B,CAAC,CAAC,aAAa,CAAC,QAAQ;4BACrB,aAAa,CAAC,QAAkC,CAC/C,kCAAwB,CACzB;wBACH,CAAC,CAAC,SAAS;oBACb,IAAI;oBACJ,MAAM,EAAE,EAAE;iBACX,CAAC;gBAEF,OAAO,aAAO,CAAC,IAAI,CAAC,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE;oBAC9D,OAAO,IAAA,wCAAsB,EAG3B,GAAG,EAAE;wBACH,OAAQ,QAAmC,CAAC,KAAK,CAAC,IAAI,EAAE;4BACtD,aAAa;yBACd,CAAC,CAAC;oBACL,CAAC,EACD,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;wBACd,eAAe,CAAC,sBAAsB,CAAC,IAAI,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;oBAC5D,CAAC,CACF,CAAC;gBACJ,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,sBAAsB,CAC5B,IAAc,EACd,GAAW,EACX,MAAqD;QAErD,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,IAAI,MAAM,KAAK,SAAS,IAAI,GAAG,EAAE;YAC/B,IAAA,eAAO,EAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YACnB,OAAO;SACR;QAED,IAAI,IAAA,iBAAS,EAAC,MAAM,CAAC,EAAE;YACpB,MAAgD,CAAC,IAAI,CACpD,UAAU,CAAC,EAAE;gBACX,IAAI,OAAO,MAAM,CAAC,YAAY,KAAK,UAAU,EAAE;oBAC7C,IAAA,eAAO,EAAC,IAAI,CAAC,CAAC;oBACd,OAAO;iBACR;gBACD,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YAC9C,CAAC,EACD,KAAK,CAAC,EAAE;gBACN,IAAA,eAAO,EAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YACvB,CAAC,CACF,CAAC;SACH;aAAM;YACL,IAAI,OAAO,MAAM,CAAC,YAAY,KAAK,UAAU,EAAE;gBAC7C,IAAA,eAAO,EAAC,IAAI,CAAC,CAAC;gBACd,OAAO;aACR;YACD,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,MAAsC,CAAC,CAAC;SACzE;IACH,CAAC;IAEO,oBAAoB,CAC1B,IAAc,EACd,MAAoC;QAEpC,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC1C,IAAI,CAAC,YAAY,EAAE;YACjB,OAAO;SACR;QAED,IAAA,wCAAsB,EACpB,GAAG,EAAE;YACH,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAC7B,CAAC,EACD,GAAG,CAAC,EAAE;YACJ,IAAI,GAAG,EAAE;gBACP,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC;aACtD;YAED,IAAA,eAAO,EAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAC3B,CAAC,EACD,IAAI,CACL,CAAC;IACJ,CAAC;IAEO,WAAW;QACjB,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,SAAS,KAAK,CAAC,QAAQ;YAC5B,OAAO,SAAS,UAAU,CAExB,MAAoC,EACpC,OAAmC;gBAEnC,OAAO,eAAe,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;YACjE,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,cAAc;QACpB,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,SAAS,QAAQ,CAAC,QAAsB;YAC7C,OAAO,SAAS,aAAa,CAE3B,MAAkC,EAClC,WAAsC,EACtC,KAAkD,EAClD,OAAgC,EAChC,QAAgC;gBAEhC,OAAO,eAAe,CAAC,SAAS,CAC9B,IAAI,EACJ,QAAQ,EACR,MAAM,EACN,WAAW,EACX,KAAK,EACL,QAAQ,EACR,OAAO,CACR,CAAC;YACJ,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,MAAM,CACZ,GAAc,EACd,QAAmB,EACnB,MAAoC,EACpC,OAAmC;QAEnC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAS,CAAC,KAAK,CAAC,CAAC;QAEpD,OAAO,aAAO,CAAC,IAAI,CAAC,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE;YAC9D,OAAO,IAAA,wCAAsB,EAG3B,GAAG,EAAE;gBACH,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;YAC7C,CAAC,EACD,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBACd,IAAI,MAAM,EAAE;oBACV,MAAM,SAAS,GAAG,IAAA,oBAAY,EAAC,MAAM,CAAC,CAAC;oBACvC,IAAI,CAAC,SAAS,EAAE;wBACd,IAAI,CAAC,UAAU,CAAC,gBAAS,CAAC,YAAY,CAAC,CAAC;qBACzC;yBAAM,IAAI,MAAM,CAAC,GAAG,EAAE;wBACrB,IAAA,qBAAa,EAAC,IAAI,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;qBACrD;iBACF;gBACD,IAAA,eAAO,EAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YACrB,CAAC,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,SAAS,CACf,GAAiB,EACjB,QAAsB,EACtB,MAAkC,EAClC,WAAsC,EACtC,KAAkD,EAClD,QAAgC,EAChC,OAAgC;QAEhC,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAS,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QAE3D,OAAO,aAAO,CAAC,IAAI,CAAC,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE;YAC9D,OAAO,IAAA,wCAAsB,EAC3B,GAAG,EAAE;gBACH,OAAO,QAAQ,CAAC,IAAI,CAClB,GAAG,EACH,MAAM,EACN,WAAW,EACX,KAAK,EACL,OAAO,EACP,QAAQ,CACT,CAAC;YACJ,CAAC,EACD,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBACd,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE;oBACpB,IAAI,CAAC,UAAU,CAAC,gBAAS,CAAC,eAAe,CAAC,CAAC;iBAC5C;gBACD,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;oBAC3B,IAAI,CAAC,eAAe,CAAC;wBACnB,IAAI,EAAE,+BAAc,CAAC,qBAAqB;wBAC1C,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;qBAChC,CAAC,CAAC;iBACJ;gBACD,IAAA,eAAO,EAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YACrB,CAAC,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,kBAAkB,CACxB,SAAkD,EAClD,aAAyC;;QAEzC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAEhC,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAS,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAC1D,IAAI,SAAS,EAAE;YACb,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,IAAI,EAAE,QAAQ,EAAE,GAChD,SAAiD,CAAC;YAEpD,IAAI,CAAC,YAAY,CAAC,+BAAc,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;YAEhE,MAAM,aAAa,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,CAAC;YAEtC,4GAA4G;YAC5G,yKAAyK;YACzK,mGAAmG;YACnG,IAAI,aAAa,EAAE;gBACjB,IAAI,CAAC,YAAY,CAAC,+BAAc,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;gBAChE,IAAI,CAAC,UAAU,CAAC,GAAG,aAAa,IAAI,aAAa,EAAE,CAAC,CAAC;aACtD;iBAAM;gBACL,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;aAChC;SACF;aAAM;YACL,IAAI,aAAa,GAAG,GAAG,CAAC;YACxB,IAAI,aAAa,CAAC,aAAa,EAAE;gBAC/B,aAAa,GAAG,KAAK,aAAa,CAAC,aAAa,IAAI,CAAC;aACtD;YACD,aAAa,GAAG,wCAAuB,CAAC,OAAO,CAC7C,iBAAiB,EACjB,aAAa,CACd,CAAC;YACF,IAAI,CAAC,YAAY,CAAC,+BAAc,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;SACjE;QAED,IAAI,MAAA,aAAa,CAAC,QAAQ,0CAAE,GAAG,EAAE;YAC/B,IAAA,qBAAa,EAAC,IAAI,EAAE,aAAa,CAAC,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;SACrE;QAED,IAAI,aAAa,CAAC,cAAc,IAAI,MAAM,CAAC,WAAW,EAAE;YACtD,IAAA,kCAA0B,EAAC,IAAI,EAAE,aAAa,CAAC,cAAc,CAAC,CAAC;SAChE;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,gBAAgB,CACtB,MAAkC,EAClC,QAAmC,EACnC,SAAc,EACd,YAAiB,EACjB,cAA6C,EAC7C,aAA4B,EAC5B,aAAiE,EACjE,YAA+D,EAC/D,oBAAiE;QAEjE,IAAI,CAAC,YAAY,EAAE;YACjB,YAAY,GAAG,EAAE,CAAC;SACnB;QAED,IACE,YAAY,CAAC,kCAAwB,CAAC;YACtC,IAAI,CAAC,SAAS,EAAE,CAAC,kBAAkB,EACnC;YACA,OAAO;gBACL,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,YAAY;gBACZ,cAAc;gBACd,aAAa;gBACb,aAAa;gBACb,YAAY;aACb,CAAC;SACH;QAED,MAAM,sBAAsB,GAAG,aAAa,IAAI,IAAI,CAAC;QACrD,uCAAuC;QACvC,oHAAoH;QACpH,MAAM,uBAAuB,GAAG,aAAa,aAAb,aAAa,cAAb,aAAa,GAAI,oBAAoB,CAAC;QACtE,aAAa,GAAG,IAAA,yBAAiB,EAC/B,IAAI,CAAC,MAAM,EACX,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,EACtB,uBAAuB,EACvB,sBAAsB,CACvB,CAAC;QAEF,IAAI,MAAM,EAAE;YACV,IAAA,kBAAU,EAAC,MAAM,CAAC,YAAY,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;YACvE,IAAA,kBAAU,EAAC,MAAM,CAAC,eAAe,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;SAC3E;QAED,OAAO;YACL,MAAM;YACN,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,cAAc;YACd,aAAa;YACb,aAAa;YACb,YAAY;SACb,CAAC;IACJ,CAAC;CACF;AA/aD,wDA+aC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { context, trace } from '@opentelemetry/api';\nimport {\n  isWrapped,\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  InstrumentationNodeModuleFile,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\nimport type * as graphqlTypes from 'graphql';\nimport { SpanNames } from './enum';\nimport { AttributeNames } from './enums/AttributeNames';\nimport { OTEL_GRAPHQL_DATA_SYMBOL } from './symbols';\n\nimport {\n  executeFunctionWithObj,\n  executeArgumentsArray,\n  executeType,\n  parseType,\n  validateType,\n  OtelExecutionArgs,\n  ObjectWithGraphQLData,\n  OPERATION_NOT_SUPPORTED,\n  Maybe,\n} from './internal-types';\nimport {\n  addInputVariableAttributes,\n  addSpanSource,\n  endSpan,\n  getOperation,\n  isPromise,\n  wrapFieldResolver,\n  wrapFields,\n} from './utils';\n\n/** @knipignore */\nimport { PACKAGE_NAME, PACKAGE_VERSION } from './version';\nimport * as api from '@opentelemetry/api';\nimport type { PromiseOrValue } from 'graphql/jsutils/PromiseOrValue';\nimport {\n  GraphQLInstrumentationConfig,\n  GraphQLInstrumentationParsedConfig,\n} from './types';\n\nconst DEFAULT_CONFIG: GraphQLInstrumentationParsedConfig = {\n  mergeItems: false,\n  depth: -1,\n  allowValues: false,\n  ignoreResolveSpans: false,\n};\n\nconst supportedVersions = ['>=14.0.0 <17'];\n\nexport class GraphQLInstrumentation extends InstrumentationBase<GraphQLInstrumentationParsedConfig> {\n  constructor(config: GraphQLInstrumentationConfig = {}) {\n    super(PACKAGE_NAME, PACKAGE_VERSION, { ...DEFAULT_CONFIG, ...config });\n  }\n\n  override setConfig(config: GraphQLInstrumentationConfig = {}) {\n    super.setConfig({ ...DEFAULT_CONFIG, ...config });\n  }\n\n  protected init() {\n    const module = new InstrumentationNodeModuleDefinition(\n      'graphql',\n      supportedVersions\n    );\n    module.files.push(this._addPatchingExecute());\n    module.files.push(this._addPatchingParser());\n    module.files.push(this._addPatchingValidate());\n\n    return module;\n  }\n\n  private _addPatchingExecute(): InstrumentationNodeModuleFile {\n    return new InstrumentationNodeModuleFile(\n      'graphql/execution/execute.js',\n      supportedVersions,\n      // cannot make it work with appropriate type as execute function has 2\n      //types and/cannot import function but only types\n      (moduleExports: any) => {\n        if (isWrapped(moduleExports.execute)) {\n          this._unwrap(moduleExports, 'execute');\n        }\n        this._wrap(\n          moduleExports,\n          'execute',\n          this._patchExecute(moduleExports.defaultFieldResolver)\n        );\n        return moduleExports;\n      },\n      moduleExports => {\n        if (moduleExports) {\n          this._unwrap(moduleExports, 'execute');\n        }\n      }\n    );\n  }\n\n  private _addPatchingParser(): InstrumentationNodeModuleFile {\n    return new InstrumentationNodeModuleFile(\n      'graphql/language/parser.js',\n      supportedVersions,\n      (moduleExports: typeof graphqlTypes) => {\n        if (isWrapped(moduleExports.parse)) {\n          this._unwrap(moduleExports, 'parse');\n        }\n        this._wrap(moduleExports, 'parse', this._patchParse());\n        return moduleExports;\n      },\n      (moduleExports: typeof graphqlTypes) => {\n        if (moduleExports) {\n          this._unwrap(moduleExports, 'parse');\n        }\n      }\n    );\n  }\n\n  private _addPatchingValidate(): InstrumentationNodeModuleFile {\n    return new InstrumentationNodeModuleFile(\n      'graphql/validation/validate.js',\n      supportedVersions,\n      moduleExports => {\n        if (isWrapped(moduleExports.validate)) {\n          this._unwrap(moduleExports, 'validate');\n        }\n        this._wrap(moduleExports, 'validate', this._patchValidate());\n        return moduleExports;\n      },\n      moduleExports => {\n        if (moduleExports) {\n          this._unwrap(moduleExports, 'validate');\n        }\n      }\n    );\n  }\n\n  private _patchExecute(\n    defaultFieldResolved: graphqlTypes.GraphQLFieldResolver<any, any>\n  ): (original: executeType) => executeType {\n    const instrumentation = this;\n    return function execute(original) {\n      return function patchExecute(\n        this: executeType\n      ): PromiseOrValue<graphqlTypes.ExecutionResult> {\n        let processedArgs: OtelExecutionArgs;\n\n        // case when apollo server is used for example\n        if (arguments.length >= 2) {\n          const args = arguments as unknown as executeArgumentsArray;\n          processedArgs = instrumentation._wrapExecuteArgs(\n            args[0],\n            args[1],\n            args[2],\n            args[3],\n            args[4],\n            args[5],\n            args[6],\n            args[7],\n            defaultFieldResolved\n          );\n        } else {\n          const args = arguments[0] as graphqlTypes.ExecutionArgs;\n          processedArgs = instrumentation._wrapExecuteArgs(\n            args.schema,\n            args.document,\n            args.rootValue,\n            args.contextValue,\n            args.variableValues,\n            args.operationName,\n            args.fieldResolver,\n            args.typeResolver,\n            defaultFieldResolved\n          );\n        }\n\n        const operation = getOperation(\n          processedArgs.document,\n          processedArgs.operationName\n        );\n\n        const span = instrumentation._createExecuteSpan(\n          operation,\n          processedArgs\n        );\n\n        processedArgs.contextValue[OTEL_GRAPHQL_DATA_SYMBOL] = {\n          source: processedArgs.document\n            ? processedArgs.document ||\n              (processedArgs.document as ObjectWithGraphQLData)[\n                OTEL_GRAPHQL_DATA_SYMBOL\n              ]\n            : undefined,\n          span,\n          fields: {},\n        };\n\n        return context.with(trace.setSpan(context.active(), span), () => {\n          return safeExecuteInTheMiddle<\n            PromiseOrValue<graphqlTypes.ExecutionResult>\n          >(\n            () => {\n              return (original as executeFunctionWithObj).apply(this, [\n                processedArgs,\n              ]);\n            },\n            (err, result) => {\n              instrumentation._handleExecutionResult(span, err, result);\n            }\n          );\n        });\n      };\n    };\n  }\n\n  private _handleExecutionResult(\n    span: api.Span,\n    err?: Error,\n    result?: PromiseOrValue<graphqlTypes.ExecutionResult>\n  ) {\n    const config = this.getConfig();\n    if (result === undefined || err) {\n      endSpan(span, err);\n      return;\n    }\n\n    if (isPromise(result)) {\n      (result as Promise<graphqlTypes.ExecutionResult>).then(\n        resultData => {\n          if (typeof config.responseHook !== 'function') {\n            endSpan(span);\n            return;\n          }\n          this._executeResponseHook(span, resultData);\n        },\n        error => {\n          endSpan(span, error);\n        }\n      );\n    } else {\n      if (typeof config.responseHook !== 'function') {\n        endSpan(span);\n        return;\n      }\n      this._executeResponseHook(span, result as graphqlTypes.ExecutionResult);\n    }\n  }\n\n  private _executeResponseHook(\n    span: api.Span,\n    result: graphqlTypes.ExecutionResult\n  ) {\n    const { responseHook } = this.getConfig();\n    if (!responseHook) {\n      return;\n    }\n\n    safeExecuteInTheMiddle(\n      () => {\n        responseHook(span, result);\n      },\n      err => {\n        if (err) {\n          this._diag.error('Error running response hook', err);\n        }\n\n        endSpan(span, undefined);\n      },\n      true\n    );\n  }\n\n  private _patchParse(): (original: parseType) => parseType {\n    const instrumentation = this;\n    return function parse(original) {\n      return function patchParse(\n        this: parseType,\n        source: string | graphqlTypes.Source,\n        options?: graphqlTypes.ParseOptions\n      ): graphqlTypes.DocumentNode {\n        return instrumentation._parse(this, original, source, options);\n      };\n    };\n  }\n\n  private _patchValidate(): (original: validateType) => validateType {\n    const instrumentation = this;\n    return function validate(original: validateType) {\n      return function patchValidate(\n        this: validateType,\n        schema: graphqlTypes.GraphQLSchema,\n        documentAST: graphqlTypes.DocumentNode,\n        rules?: ReadonlyArray<graphqlTypes.ValidationRule>,\n        options?: { maxErrors?: number },\n        typeInfo?: graphqlTypes.TypeInfo\n      ): ReadonlyArray<graphqlTypes.GraphQLError> {\n        return instrumentation._validate(\n          this,\n          original,\n          schema,\n          documentAST,\n          rules,\n          typeInfo,\n          options\n        );\n      };\n    };\n  }\n\n  private _parse(\n    obj: parseType,\n    original: parseType,\n    source: string | graphqlTypes.Source,\n    options?: graphqlTypes.ParseOptions\n  ): graphqlTypes.DocumentNode {\n    const config = this.getConfig();\n    const span = this.tracer.startSpan(SpanNames.PARSE);\n\n    return context.with(trace.setSpan(context.active(), span), () => {\n      return safeExecuteInTheMiddle<\n        graphqlTypes.DocumentNode & ObjectWithGraphQLData\n      >(\n        () => {\n          return original.call(obj, source, options);\n        },\n        (err, result) => {\n          if (result) {\n            const operation = getOperation(result);\n            if (!operation) {\n              span.updateName(SpanNames.SCHEMA_PARSE);\n            } else if (result.loc) {\n              addSpanSource(span, result.loc, config.allowValues);\n            }\n          }\n          endSpan(span, err);\n        }\n      );\n    });\n  }\n\n  private _validate(\n    obj: validateType,\n    original: validateType,\n    schema: graphqlTypes.GraphQLSchema,\n    documentAST: graphqlTypes.DocumentNode,\n    rules?: ReadonlyArray<graphqlTypes.ValidationRule>,\n    typeInfo?: graphqlTypes.TypeInfo,\n    options?: { maxErrors?: number }\n  ): ReadonlyArray<graphqlTypes.GraphQLError> {\n    const span = this.tracer.startSpan(SpanNames.VALIDATE, {});\n\n    return context.with(trace.setSpan(context.active(), span), () => {\n      return safeExecuteInTheMiddle<ReadonlyArray<graphqlTypes.GraphQLError>>(\n        () => {\n          return original.call(\n            obj,\n            schema,\n            documentAST,\n            rules,\n            options,\n            typeInfo\n          );\n        },\n        (err, errors) => {\n          if (!documentAST.loc) {\n            span.updateName(SpanNames.SCHEMA_VALIDATE);\n          }\n          if (errors && errors.length) {\n            span.recordException({\n              name: AttributeNames.ERROR_VALIDATION_NAME,\n              message: JSON.stringify(errors),\n            });\n          }\n          endSpan(span, err);\n        }\n      );\n    });\n  }\n\n  private _createExecuteSpan(\n    operation: graphqlTypes.DefinitionNode | undefined,\n    processedArgs: graphqlTypes.ExecutionArgs\n  ): api.Span {\n    const config = this.getConfig();\n\n    const span = this.tracer.startSpan(SpanNames.EXECUTE, {});\n    if (operation) {\n      const { operation: operationType, name: nameNode } =\n        operation as graphqlTypes.OperationDefinitionNode;\n\n      span.setAttribute(AttributeNames.OPERATION_TYPE, operationType);\n\n      const operationName = nameNode?.value;\n\n      // https://opentelemetry.io/docs/reference/specification/trace/semantic_conventions/instrumentation/graphql/\n      // > The span name MUST be of the format <graphql.operation.type> <graphql.operation.name> provided that graphql.operation.type and graphql.operation.name are available.\n      // > If graphql.operation.name is not available, the span SHOULD be named <graphql.operation.type>.\n      if (operationName) {\n        span.setAttribute(AttributeNames.OPERATION_NAME, operationName);\n        span.updateName(`${operationType} ${operationName}`);\n      } else {\n        span.updateName(operationType);\n      }\n    } else {\n      let operationName = ' ';\n      if (processedArgs.operationName) {\n        operationName = ` \"${processedArgs.operationName}\" `;\n      }\n      operationName = OPERATION_NOT_SUPPORTED.replace(\n        '$operationName$',\n        operationName\n      );\n      span.setAttribute(AttributeNames.OPERATION_NAME, operationName);\n    }\n\n    if (processedArgs.document?.loc) {\n      addSpanSource(span, processedArgs.document.loc, config.allowValues);\n    }\n\n    if (processedArgs.variableValues && config.allowValues) {\n      addInputVariableAttributes(span, processedArgs.variableValues);\n    }\n\n    return span;\n  }\n\n  private _wrapExecuteArgs(\n    schema: graphqlTypes.GraphQLSchema,\n    document: graphqlTypes.DocumentNode,\n    rootValue: any,\n    contextValue: any,\n    variableValues: Maybe<{ [key: string]: any }>,\n    operationName: Maybe<string>,\n    fieldResolver: Maybe<graphqlTypes.GraphQLFieldResolver<any, any>>,\n    typeResolver: Maybe<graphqlTypes.GraphQLTypeResolver<any, any>>,\n    defaultFieldResolved: graphqlTypes.GraphQLFieldResolver<any, any>\n  ): OtelExecutionArgs {\n    if (!contextValue) {\n      contextValue = {};\n    }\n\n    if (\n      contextValue[OTEL_GRAPHQL_DATA_SYMBOL] ||\n      this.getConfig().ignoreResolveSpans\n    ) {\n      return {\n        schema,\n        document,\n        rootValue,\n        contextValue,\n        variableValues,\n        operationName,\n        fieldResolver,\n        typeResolver,\n      };\n    }\n\n    const isUsingDefaultResolver = fieldResolver == null;\n    // follows graphql implementation here:\n    // https://github.com/graphql/graphql-js/blob/0b7daed9811731362c71900e12e5ea0d1ecc7f1f/src/execution/execute.ts#L494\n    const fieldResolverForExecute = fieldResolver ?? defaultFieldResolved;\n    fieldResolver = wrapFieldResolver(\n      this.tracer,\n      () => this.getConfig(),\n      fieldResolverForExecute,\n      isUsingDefaultResolver\n    );\n\n    if (schema) {\n      wrapFields(schema.getQueryType(), this.tracer, () => this.getConfig());\n      wrapFields(schema.getMutationType(), this.tracer, () => this.getConfig());\n    }\n\n    return {\n      schema,\n      document,\n      rootValue,\n      contextValue,\n      variableValues,\n      operationName,\n      fieldResolver,\n      typeResolver,\n    };\n  }\n}\n"]}