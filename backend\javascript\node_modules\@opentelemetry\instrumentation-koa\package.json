{"name": "@opentelemetry/instrumentation-koa", "version": "0.47.1", "description": "OpenTelemetry instrumentation for `koa` http web application framework", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"test": "nyc mocha 'test/**/*.ts'", "test-all-versions": "tav", "tdd": "yarn test -- --watch-extensions ts --watch", "clean": "rimraf build/*", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "lint:readme": "node ../../../scripts/lint-readme.js", "prewatch": "npm run precompile", "version:update": "node ../../../scripts/version-update.js", "compile": "tsc -p .", "prepublishOnly": "npm run compile", "watch": "tsc -w"}, "keywords": ["instrumentation", "koa", "nodejs", "opentelemetry", "plugin", "profiling", "tracing"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "devDependencies": {"@koa/router": "13.1.0", "@opentelemetry/api": "^1.3.0", "@opentelemetry/context-async-hooks": "^1.8.0", "@opentelemetry/contrib-test-utils": "^0.45.1", "@opentelemetry/instrumentation-http": "^0.57.1", "@opentelemetry/sdk-trace-base": "^1.8.0", "@opentelemetry/sdk-trace-node": "^1.8.0", "@types/koa": "2.15.0", "@types/koa__router": "12.0.4", "@types/mocha": "7.0.2", "@types/node": "18.18.14", "@types/sinon": "10.0.20", "koa": "2.13.1", "nyc": "15.1.0", "rimraf": "5.0.10", "semver": "7.7.1", "sinon": "15.2.0", "test-all-versions": "6.1.0", "typescript": "4.4.4"}, "dependencies": {"@opentelemetry/core": "^1.8.0", "@opentelemetry/instrumentation": "^0.57.1", "@opentelemetry/semantic-conventions": "^1.27.0"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/opentelemetry-instrumentation-koa#readme", "gitHead": "1eb77007669bae87fe5664d68ba6533b95275d52"}