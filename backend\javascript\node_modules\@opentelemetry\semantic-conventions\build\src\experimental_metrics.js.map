{"version": 3, "file": "experimental_metrics.js", "sourceRoot": "", "sources": ["../../src/experimental_metrics.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;AAEH,4GAA4G;AAC5G,2GAA2G;AAC3G,4GAA4G;AAE5G;;;;GAIG;AACU,QAAA,kDAAkD,GAAG,6CAAsD,CAAC;AAEzH;;;;GAIG;AACU,QAAA,qDAAqD,GAAG,gDAAyD,CAAC;AAE/H;;;;GAIG;AACU,QAAA,+BAA+B,GAAG,0BAAmC,CAAC;AAEnF;;;;GAIG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;;GAOG;AACU,QAAA,+BAA+B,GAAG,0BAAmC,CAAC;AAEnF;;;;;GAKG;AACU,QAAA,yBAAyB,GAAG,oBAA6B,CAAC;AAEvE;;;;GAIG;AACU,QAAA,wBAAwB,GAAG,mBAA4B,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,oBAA6B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,mBAA4B,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,6BAA6B,GAAG,wBAAiC,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;;;;;;GAOG;AACU,QAAA,uBAAuB,GAAG,kBAA2B,CAAC;AAEnE;;;;GAIG;AACU,QAAA,oBAAoB,GAAG,eAAwB,CAAC;AAE7D;;;;GAIG;AACU,QAAA,eAAe,GAAG,UAAmB,CAAC;AAEnD;;;;GAIG;AACU,QAAA,sBAAsB,GAAG,iBAA0B,CAAC;AAEjE;;;;;;GAMG;AACU,QAAA,mCAAmC,GAAG,8BAAuC,CAAC;AAE3F;;;;;;GAMG;AACU,QAAA,6BAA6B,GAAG,wBAAiC,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,uCAAuC,GAAG,kCAA2C,CAAC;AAEnG;;;;GAIG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;GAIG;AACU,QAAA,uCAAuC,GAAG,kCAA2C,CAAC;AAEnG;;;;GAIG;AACU,QAAA,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;GAIG;AACU,QAAA,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;GAIG;AACU,QAAA,+BAA+B,GAAG,0BAAmC,CAAC;AAEnF;;;;GAIG;AACU,QAAA,4CAA4C,GAAG,uCAAgD,CAAC;AAE7G;;;;GAIG;AACU,QAAA,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;GAIG;AACU,QAAA,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;GAIG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,wCAAwC,GAAG,mCAA4C,CAAC;AAErG;;;;;;GAMG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,gCAAgC,GAAG,2BAAoC,CAAC;AAErF;;;;;;GAMG;AACU,QAAA,6CAA6C,GAAG,wCAAiD,CAAC;AAE/G;;;;;;GAMG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;;;GAMG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,sCAAsC,GAAG,iCAA0C,CAAC;AAEjG;;;;;;GAMG;AACU,QAAA,+CAA+C,GAAG,0CAAmD,CAAC;AAEnH;;;;;;GAMG;AACU,QAAA,kDAAkD,GAAG,6CAAsD,CAAC;AAEzH;;;;GAIG;AACU,QAAA,uCAAuC,GAAG,kCAA2C,CAAC;AAEnG;;;;GAIG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;;;GAIG;AACU,QAAA,sBAAsB,GAAG,iBAA0B,CAAC;AAEjE;;;;GAIG;AACU,QAAA,qBAAqB,GAAG,gBAAyB,CAAC;AAE/D;;;;GAIG;AACU,QAAA,kBAAkB,GAAG,aAAsB,CAAC;AAEzD;;;;GAIG;AACU,QAAA,yBAAyB,GAAG,oBAA6B,CAAC;AAEvE;;;;GAIG;AACU,QAAA,uBAAuB,GAAG,kBAA2B,CAAC;AAEnE;;;;GAIG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;;;GAIG;AACU,QAAA,qBAAqB,GAAG,gBAAyB,CAAC;AAE/D;;;;GAIG;AACU,QAAA,kBAAkB,GAAG,aAAsB,CAAC;AAEzD;;;;GAIG;AACU,QAAA,oBAAoB,GAAG,eAAwB,CAAC;AAE7D;;;;GAIG;AACU,QAAA,uCAAuC,GAAG,kCAA2C,CAAC;AAEnG;;;;GAIG;AACU,QAAA,gCAAgC,GAAG,2BAAoC,CAAC;AAErF;;;;GAIG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;GAIG;AACU,QAAA,0CAA0C,GAAG,qCAA8C,CAAC;AAEzG;;;;GAIG;AACU,QAAA,wCAAwC,GAAG,mCAA4C,CAAC;AAErG;;;;;;GAMG;AACU,QAAA,qBAAqB,GAAG,gBAAyB,CAAC;AAE/D;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,oBAA6B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,uBAAgC,CAAC;AAE7E;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,mBAA4B,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,sBAAsB,GAAG,iBAA0B,CAAC;AAEjE;;;;;;GAMG;AACU,QAAA,qBAAqB,GAAG,gBAAyB,CAAC;AAE/D;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,oBAA6B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;;;GAIG;AACU,QAAA,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;GAIG;AACU,QAAA,sCAAsC,GAAG,iCAA0C,CAAC;AAEjG;;;;GAIG;AACU,QAAA,mCAAmC,GAAG,8BAAuC,CAAC;AAE3F;;;;;;GAMG;AACU,QAAA,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;;;GAMG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;GAIG;AACU,QAAA,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;;;GAMG;AACU,QAAA,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;;;GAMG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;GAIG;AACU,QAAA,gBAAgB,GAAG,WAAoB,CAAC;AAErD;;;;GAIG;AACU,QAAA,gBAAgB,GAAG,WAAoB,CAAC;AAErD;;;;GAIG;AACU,QAAA,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;;;GAMG;AACU,QAAA,qBAAqB,GAAG,gBAAyB,CAAC;AAE/D;;;;GAIG;AACU,QAAA,6BAA6B,GAAG,wBAAiC,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,oBAAoB,GAAG,eAAwB,CAAC;AAE7D;;;;;;GAMG;AACU,QAAA,eAAe,GAAG,UAAmB,CAAC;AAEnD;;;;;;GAMG;AACU,QAAA,gBAAgB,GAAG,WAAoB,CAAC;AAErD;;;;GAIG;AACU,QAAA,uBAAuB,GAAG,kBAA2B,CAAC;AAEnE;;;;GAIG;AACU,QAAA,8BAA8B,GAAG,yBAAkC,CAAC;AAEjF;;;;;;GAMG;AACU,QAAA,8BAA8B,GAAG,yBAAkC,CAAC;AAEjF;;;;GAIG;AACU,QAAA,6BAA6B,GAAG,wBAAiC,CAAC;AAE/E;;;;GAIG;AACU,QAAA,gCAAgC,GAAG,2BAAoC,CAAC;AAErF;;;;GAIG;AACU,QAAA,sBAAsB,GAAG,iBAA0B,CAAC;AAEjE;;;;;;GAMG;AACU,QAAA,6BAA6B,GAAG,wBAAiC,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;;;;;GAUG;AACU,QAAA,8BAA8B,GAAG,yBAAkC,CAAC;AAEjF;;;;;;;;;;GAUG;AACU,QAAA,4CAA4C,GAAG,uCAAgD,CAAC;AAE7G;;;;;;;;;;GAUG;AACU,QAAA,4CAA4C,GAAG,uCAAgD,CAAC;AAE7G;;;;;;;;;;GAUG;AACU,QAAA,uCAAuC,GAAG,kCAA2C,CAAC;AAEnG;;;;;;;;;;GAUG;AACU,QAAA,gCAAgC,GAAG,2BAAoC,CAAC;AAErF;;;;;;;;;;GAUG;AACU,QAAA,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;;;;;;;GAUG;AACU,QAAA,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;;;;;;;GAUG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;;;;;;;;;GAUG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;;;;;;;;;GAUG;AACU,QAAA,uBAAuB,GAAG,kBAA2B,CAAC;AAEnE;;;;;;;;;;GAUG;AACU,QAAA,uBAAuB,GAAG,kBAA2B,CAAC;AAEnE;;;;;;;;;;GAUG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;;;;;;;;;GAUG;AACU,QAAA,sCAAsC,GAAG,iCAA0C,CAAC;AAEjG;;;;;;;;;;GAUG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;;;;;;;;;GAUG;AACU,QAAA,gCAAgC,GAAG,2BAAoC,CAAC;AAErF;;;;;;;;;;GAUG;AACU,QAAA,8BAA8B,GAAG,yBAAkC,CAAC;AAEjF;;;;;;;GAOG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,mBAA4B,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,oBAA6B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,uBAAgC,CAAC;AAE7E;;;;GAIG;AACU,QAAA,8BAA8B,GAAG,yBAAkC,CAAC;AAEjF;;;;GAIG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;;;;;;GAOG;AACU,QAAA,sBAAsB,GAAG,iBAA0B,CAAC;AAEjE;;;;;;GAMG;AACU,QAAA,uBAAuB,GAAG,kBAA2B,CAAC;AAEnE;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,mBAA4B,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;;;GAIG;AACU,QAAA,6BAA6B,GAAG,wBAAiC,CAAC;AAE/E;;;;GAIG;AACU,QAAA,yBAAyB,GAAG,oBAA6B,CAAC;AAEvE;;;;;;;GAOG;AACU,QAAA,qBAAqB,GAAG,gBAAyB,CAAC;AAE/D;;;;;;;;;;GAUG;AACU,QAAA,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;;;;;;;GAUG;AACU,QAAA,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;;;GAMG;AACU,QAAA,gDAAgD,GAAG,2CAAoD,CAAC;AAErH;;;;;;GAMG;AACU,QAAA,8CAA8C,GAAG,yCAAkD,CAAC;AAEjH;;;;;;;;;;GAUG;AACU,QAAA,+CAA+C,GAAG,0CAAmD,CAAC;AAEnH;;;;;;;;;;GAUG;AACU,QAAA,6CAA6C,GAAG,wCAAiD,CAAC;AAE/G;;;;;;;;;;GAUG;AACU,QAAA,mCAAmC,GAAG,8BAAuC,CAAC;AAE3F;;;;;;;;;;GAUG;AACU,QAAA,mCAAmC,GAAG,8BAAuC,CAAC;AAE3F;;;;;;;;;;GAUG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;;;;;GAUG;AACU,QAAA,mCAAmC,GAAG,8BAAuC,CAAC;AAE3F;;;;;;;GAOG;AACU,QAAA,yCAAyC,GAAG,oCAA6C,CAAC;AAEvG;;;;;;GAMG;AACU,QAAA,0CAA0C,GAAG,qCAA8C,CAAC;AAEzG;;;;;;GAMG;AACU,QAAA,0CAA0C,GAAG,qCAA8C,CAAC;AAEzG;;;;;;GAMG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,uBAAgC,CAAC;AAE7E;;;;;;GAMG;AACU,QAAA,mCAAmC,GAAG,8BAAuC,CAAC;AAE3F;;;;;;;;GAQG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;;;;;GAQG;AACU,QAAA,mDAAmD,GAAG,8CAAuD,CAAC;AAE3H;;;;;;GAMG;AACU,QAAA,mDAAmD,GAAG,8CAAuD,CAAC;AAE3H;;;;;;;;GAQG;AACU,QAAA,2CAA2C,GAAG,sCAA+C,CAAC;AAE3G;;;;;;;;GAQG;AACU,QAAA,sCAAsC,GAAG,iCAA0C,CAAC;AAEjG;;;;;;GAMG;AACU,QAAA,4CAA4C,GAAG,uCAAgD,CAAC;AAE7G;;;;;;GAMG;AACU,QAAA,sCAAsC,GAAG,iCAA0C,CAAC;AAEjG;;;;;;GAMG;AACU,QAAA,4CAA4C,GAAG,uCAAgD,CAAC;AAE7G;;;;GAIG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;;;;;;GAOG;AACU,QAAA,iDAAiD,GAAG,4CAAqD,CAAC;AAEvH;;;;;;;;GAQG;AACU,QAAA,uCAAuC,GAAG,kCAA2C,CAAC;AAEnG;;;;;;GAMG;AACU,QAAA,4CAA4C,GAAG,uCAAgD,CAAC;AAE7G;;;;;;GAMG;AACU,QAAA,wCAAwC,GAAG,mCAA4C,CAAC;AAErG;;;;;;;GAOG;AACU,QAAA,wCAAwC,GAAG,mCAA4C,CAAC;AAErG;;;;;;GAMG;AACU,QAAA,8CAA8C,GAAG,yCAAkD,CAAC;AAEjH;;;;;;GAMG;AACU,QAAA,6CAA6C,GAAG,wCAAiD,CAAC;AAE/G;;;;;;GAMG;AACU,QAAA,yCAAyC,GAAG,oCAA6C,CAAC;AAEvG;;;;;;;GAOG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,gCAAgC,GAAG,2BAAoC,CAAC;AAErF;;;;;;;GAOG;AACU,QAAA,yBAAyB,GAAG,oBAA6B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,+BAA+B,GAAG,0BAAmC,CAAC;AAEnF;;;;GAIG;AACU,QAAA,+BAA+B,GAAG,0BAAmC,CAAC;AAEnF;;;;GAIG;AACU,QAAA,uBAAuB,GAAG,kBAA2B,CAAC;AAEnE;;;;GAIG;AACU,QAAA,8BAA8B,GAAG,yBAAkC,CAAC;AAEjF;;;;GAIG;AACU,QAAA,sBAAsB,GAAG,iBAA0B,CAAC;AAEjE;;;;GAIG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;;;GAIG;AACU,QAAA,6BAA6B,GAAG,wBAAiC,CAAC;AAE/E;;;;GAIG;AACU,QAAA,yBAAyB,GAAG,oBAA6B,CAAC;AAEvE;;;;GAIG;AACU,QAAA,yCAAyC,GAAG,oCAA6C,CAAC;AAEvG;;;;GAIG;AACU,QAAA,4BAA4B,GAAG,uBAAgC,CAAC;AAE7E;;;;GAIG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;;;;;;GAOG;AACU,QAAA,qBAAqB,GAAG,gBAAyB,CAAC;AAE/D;;;;;;;;;GASG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,8BAA8B,GAAG,yBAAkC,CAAC;AAEjF;;;;;;;;GAQG;AACU,QAAA,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;;;GAMG;AACU,QAAA,+BAA+B,GAAG,0BAAmC,CAAC;AAEnF;;;;;;;;GAQG;AACU,QAAA,mCAAmC,GAAG,8BAAuC,CAAC;AAE3F;;;;;;;;;GASG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,8BAA8B,GAAG,yBAAkC,CAAC;AAEjF;;;;;;;;GAQG;AACU,QAAA,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;;;GAMG;AACU,QAAA,+BAA+B,GAAG,0BAAmC,CAAC;AAEnF;;;;;;;;GAQG;AACU,QAAA,mCAAmC,GAAG,8BAAuC,CAAC;AAE3F;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;;;;GAKG;AACU,QAAA,+BAA+B,GAAG,0BAAmC,CAAC;AAEnF;;;;;GAKG;AACU,QAAA,gCAAgC,GAAG,2BAAoC,CAAC;AAErF;;;;;;GAMG;AACU,QAAA,sBAAsB,GAAG,iBAA0B,CAAC;AAEjE;;;;;;GAMG;AACU,QAAA,6BAA6B,GAAG,wBAAiC,CAAC;AAE/E;;GAEG;AACU,QAAA,qBAAqB,GAAG,gBAAyB,CAAC;AAE/D;;;;;;;;;;;GAWG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;;;GAIG;AACU,QAAA,wBAAwB,GAAG,mBAA4B,CAAC;AAErE;;GAEG;AACU,QAAA,yBAAyB,GAAG,oBAA6B,CAAC;AAEvE;;;;;;;;;GASG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;GAEG;AACU,QAAA,6BAA6B,GAAG,wBAAiC,CAAC;AAE/E;;;;GAIG;AACU,QAAA,8BAA8B,GAAG,yBAAkC,CAAC;AAEjF;;;;;;;GAOG;AACU,QAAA,8BAA8B,GAAG,yBAAkC,CAAC;AAEjF;;GAEG;AACU,QAAA,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;;;;;;;GAUG;AACU,QAAA,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;;;;;GAQG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;;;;;;GAOG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;;;;;;GAOG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;GAEG;AACU,QAAA,gCAAgC,GAAG,2BAAoC,CAAC;AAErF;;GAEG;AACU,QAAA,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;;;;;;;GAUG;AACU,QAAA,6BAA6B,GAAG,wBAAiC,CAAC;AAE/E;;;;;;;;;;GAUG;AACU,QAAA,4BAA4B,GAAG,uBAAgC,CAAC;AAE7E;;GAEG;AACU,QAAA,wBAAwB,GAAG,mBAA4B,CAAC;AAErE;;GAEG;AACU,QAAA,6BAA6B,GAAG,wBAAiC,CAAC;AAE/E;;GAEG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;GAEG;AACU,QAAA,+BAA+B,GAAG,0BAAmC,CAAC;AAEnF;;;;GAIG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;GAEG;AACU,QAAA,gCAAgC,GAAG,2BAAoC,CAAC;AAErF;;;;GAIG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;;;GAIG;AACU,QAAA,6BAA6B,GAAG,wBAAiC,CAAC;AAE/E;;;;;;;GAOG;AACU,QAAA,oBAAoB,GAAG,eAAwB,CAAC;AAE7D;;;;;;GAMG;AACU,QAAA,uBAAuB,GAAG,kBAA2B,CAAC;AAEnE;;;;;;GAMG;AACU,QAAA,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;;;GAMG;AACU,QAAA,6BAA6B,GAAG,wBAAiC,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,uBAAgC,CAAC;AAE7E;;;;GAIG;AACU,QAAA,uBAAuB,GAAG,kBAA2B,CAAC;AAEnE;;;;GAIG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;;;GAIG;AACU,QAAA,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;GAIG;AACU,QAAA,+BAA+B,GAAG,0BAAmC,CAAC;AAEnF;;;;GAIG;AACU,QAAA,4BAA4B,GAAG,uBAAgC,CAAC;AAE7E;;;;GAIG;AACU,QAAA,oBAAoB,GAAG,eAAwB,CAAC;AAE7D;;;;;;;;GAQG;AACU,QAAA,0BAA0B,GAAG,qBAA8B,CAAC;AAEzE;;;;;;;GAOG;AACU,QAAA,8BAA8B,GAAG,yBAAkC,CAAC;AAEjF;;;;GAIG;AACU,QAAA,mBAAmB,GAAG,cAAuB,CAAC;AAE3D;;;;GAIG;AACU,QAAA,2BAA2B,GAAG,sBAA+B,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n//----------------------------------------------------------------------------------------------------------\n// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates/register/stable/metrics.ts.j2\n//----------------------------------------------------------------------------------------------------------\n\n/**\n * Number of active client instances\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_AZURE_COSMOSDB_CLIENT_ACTIVE_INSTANCE_COUNT = 'azure.cosmosdb.client.active_instance.count' as const;\n\n/**\n * [Request units](https://learn.microsoft.com/azure/cosmos-db/request-units) consumed by the operation\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_AZURE_COSMOSDB_CLIENT_OPERATION_REQUEST_CHARGE = 'azure.cosmosdb.client.operation.request_charge' as const;\n\n/**\n * The number of pipeline runs currently active in the system by state.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_CICD_PIPELINE_RUN_ACTIVE = 'cicd.pipeline.run.active' as const;\n\n/**\n * Duration of a pipeline run grouped by pipeline, state and result.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_CICD_PIPELINE_RUN_DURATION = 'cicd.pipeline.run.duration' as const;\n\n/**\n * The number of errors encountered in pipeline runs (eg. compile, test failures).\n *\n * @note There might be errors in a pipeline run that are non fatal (eg. they are suppressed) or in a parallel stage multiple stages could have a fatal error.\n * This means that this error count might not be the same as the count of metric `cicd.pipeline.run.duration` with run result `failure`.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_CICD_PIPELINE_RUN_ERRORS = 'cicd.pipeline.run.errors' as const;\n\n/**\n * The number of errors in a component of the CICD system (eg. controller, scheduler, agent).\n *\n * @note Errors in pipeline run execution are explicitly excluded. Ie a test failure is not counted in this metric.\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_CICD_SYSTEM_ERRORS = 'cicd.system.errors' as const;\n\n/**\n * The number of workers on the CICD system by state.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_CICD_WORKER_COUNT = 'cicd.worker.count' as const;\n\n/**\n * Total CPU time consumed\n *\n * @note Total CPU time consumed by the specific container on all available CPU cores\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_CONTAINER_CPU_TIME = 'container.cpu.time' as const;\n\n/**\n * Container's CPU usage, measured in cpus. Range from 0 to the number of allocatable CPUs\n *\n * @note CPU usage of the specific container on all available CPU cores, averaged over the sample window\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_CONTAINER_CPU_USAGE = 'container.cpu.usage' as const;\n\n/**\n * Disk bytes for the container.\n *\n * @note The total number of bytes read/written successfully (aggregated from all disks).\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_CONTAINER_DISK_IO = 'container.disk.io' as const;\n\n/**\n * Memory usage of the container.\n *\n * @note Memory usage of the container.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_CONTAINER_MEMORY_USAGE = 'container.memory.usage' as const;\n\n/**\n * Network bytes for the container.\n *\n * @note The number of bytes sent/received on all network interfaces by the container.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_CONTAINER_NETWORK_IO = 'container.network.io' as const;\n\n/**\n * The time the container has been running\n *\n * @note Instrumentations **SHOULD** use a gauge with type `double` and measure uptime in seconds as a floating point number with the highest precision available.\n * The actual accuracy would depend on the instrumentation and operating system.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_CONTAINER_UPTIME = 'container.uptime' as const;\n\n/**\n * Operating frequency of the logical CPU in Hertz.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_CPU_FREQUENCY = 'cpu.frequency' as const;\n\n/**\n * Seconds each logical CPU spent on each mode\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_CPU_TIME = 'cpu.time' as const;\n\n/**\n * For each logical CPU, the utilization is calculated as the change in cumulative CPU time (cpu.time) over a measurement interval, divided by the elapsed time.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_CPU_UTILIZATION = 'cpu.utilization' as const;\n\n/**\n * The total number of objects collected inside a generation since interpreter start.\n *\n * @note This metric reports data from [`gc.stats()`](https://docs.python.org/3/library/gc.html#gc.get_stats).\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_CPYTHON_GC_COLLECTED_OBJECTS = 'cpython.gc.collected_objects' as const;\n\n/**\n * The number of times a generation was collected since interpreter start.\n *\n * @note This metric reports data from [`gc.stats()`](https://docs.python.org/3/library/gc.html#gc.get_stats).\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_CPYTHON_GC_COLLECTIONS = 'cpython.gc.collections' as const;\n\n/**\n * The total number of objects which were found to be uncollectable inside a generation since interpreter start.\n *\n * @note This metric reports data from [`gc.stats()`](https://docs.python.org/3/library/gc.html#gc.get_stats).\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_CPYTHON_GC_UNCOLLECTABLE_OBJECTS = 'cpython.gc.uncollectable_objects' as const;\n\n/**\n * The number of connections that are currently in state described by the `state` attribute\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DB_CLIENT_CONNECTION_COUNT = 'db.client.connection.count' as const;\n\n/**\n * The time it took to create a new connection\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DB_CLIENT_CONNECTION_CREATE_TIME = 'db.client.connection.create_time' as const;\n\n/**\n * The maximum number of idle open connections allowed\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DB_CLIENT_CONNECTION_IDLE_MAX = 'db.client.connection.idle.max' as const;\n\n/**\n * The minimum number of idle open connections allowed\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DB_CLIENT_CONNECTION_IDLE_MIN = 'db.client.connection.idle.min' as const;\n\n/**\n * The maximum number of open connections allowed\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DB_CLIENT_CONNECTION_MAX = 'db.client.connection.max' as const;\n\n/**\n * The number of current pending requests for an open connection\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS = 'db.client.connection.pending_requests' as const;\n\n/**\n * The number of connection timeouts that have occurred trying to obtain a connection from the pool\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DB_CLIENT_CONNECTION_TIMEOUTS = 'db.client.connection.timeouts' as const;\n\n/**\n * The time between borrowing a connection and returning it to the pool\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DB_CLIENT_CONNECTION_USE_TIME = 'db.client.connection.use_time' as const;\n\n/**\n * The time it took to obtain an open connection from the pool\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DB_CLIENT_CONNECTION_WAIT_TIME = 'db.client.connection.wait_time' as const;\n\n/**\n * Deprecated, use `db.client.connection.create_time` instead. Note: the unit also changed from `ms` to `s`.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.client.connection.create_time` with unit `s`.\n */\nexport const METRIC_DB_CLIENT_CONNECTIONS_CREATE_TIME = 'db.client.connections.create_time' as const;\n\n/**\n * Deprecated, use `db.client.connection.idle.max` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.client.connection.idle.max`.\n */\nexport const METRIC_DB_CLIENT_CONNECTIONS_IDLE_MAX = 'db.client.connections.idle.max' as const;\n\n/**\n * Deprecated, use `db.client.connection.idle.min` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.client.connection.idle.min`.\n */\nexport const METRIC_DB_CLIENT_CONNECTIONS_IDLE_MIN = 'db.client.connections.idle.min' as const;\n\n/**\n * Deprecated, use `db.client.connection.max` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.client.connection.max`.\n */\nexport const METRIC_DB_CLIENT_CONNECTIONS_MAX = 'db.client.connections.max' as const;\n\n/**\n * Deprecated, use `db.client.connection.pending_requests` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.client.connection.pending_requests`.\n */\nexport const METRIC_DB_CLIENT_CONNECTIONS_PENDING_REQUESTS = 'db.client.connections.pending_requests' as const;\n\n/**\n * Deprecated, use `db.client.connection.timeouts` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.client.connection.timeouts`.\n */\nexport const METRIC_DB_CLIENT_CONNECTIONS_TIMEOUTS = 'db.client.connections.timeouts' as const;\n\n/**\n * Deprecated, use `db.client.connection.count` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.client.connection.count`.\n */\nexport const METRIC_DB_CLIENT_CONNECTIONS_USAGE = 'db.client.connections.usage' as const;\n\n/**\n * Deprecated, use `db.client.connection.use_time` instead. Note: the unit also changed from `ms` to `s`.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.client.connection.use_time` with unit `s`.\n */\nexport const METRIC_DB_CLIENT_CONNECTIONS_USE_TIME = 'db.client.connections.use_time' as const;\n\n/**\n * Deprecated, use `db.client.connection.wait_time` instead. Note: the unit also changed from `ms` to `s`.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.client.connection.wait_time` with unit `s`.\n */\nexport const METRIC_DB_CLIENT_CONNECTIONS_WAIT_TIME = 'db.client.connections.wait_time' as const;\n\n/**\n * Deprecated, use `azure.cosmosdb.client.active_instance.count` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `azure.cosmosdb.client.active_instance.count`.\n */\nexport const METRIC_DB_CLIENT_COSMOSDB_ACTIVE_INSTANCE_COUNT = 'db.client.cosmosdb.active_instance.count' as const;\n\n/**\n * Deprecated, use `azure.cosmosdb.client.operation.request_charge` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `azure.cosmosdb.client.operation.request_charge`.\n */\nexport const METRIC_DB_CLIENT_COSMOSDB_OPERATION_REQUEST_CHARGE = 'db.client.cosmosdb.operation.request_charge' as const;\n\n/**\n * The actual number of records returned by the database operation.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DB_CLIENT_RESPONSE_RETURNED_ROWS = 'db.client.response.returned_rows' as const;\n\n/**\n * Measures the time taken to perform a DNS lookup.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DNS_LOOKUP_DURATION = 'dns.lookup.duration' as const;\n\n/**\n * Number of invocation cold starts\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_FAAS_COLDSTARTS = 'faas.coldstarts' as const;\n\n/**\n * Distribution of CPU usage per invocation\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_FAAS_CPU_USAGE = 'faas.cpu_usage' as const;\n\n/**\n * Number of invocation errors\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_FAAS_ERRORS = 'faas.errors' as const;\n\n/**\n * Measures the duration of the function's initialization, such as a cold start\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_FAAS_INIT_DURATION = 'faas.init_duration' as const;\n\n/**\n * Number of successful invocations\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_FAAS_INVOCATIONS = 'faas.invocations' as const;\n\n/**\n * Measures the duration of the function's logic execution\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_FAAS_INVOKE_DURATION = 'faas.invoke_duration' as const;\n\n/**\n * Distribution of max memory usage per invocation\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_FAAS_MEM_USAGE = 'faas.mem_usage' as const;\n\n/**\n * Distribution of net I/O usage per invocation\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_FAAS_NET_IO = 'faas.net_io' as const;\n\n/**\n * Number of invocation timeouts\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_FAAS_TIMEOUTS = 'faas.timeouts' as const;\n\n/**\n * GenAI operation duration\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GEN_AI_CLIENT_OPERATION_DURATION = 'gen_ai.client.operation.duration' as const;\n\n/**\n * Measures number of input and output tokens used\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GEN_AI_CLIENT_TOKEN_USAGE = 'gen_ai.client.token.usage' as const;\n\n/**\n * Generative AI server request duration such as time-to-last byte or last output token\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GEN_AI_SERVER_REQUEST_DURATION = 'gen_ai.server.request.duration' as const;\n\n/**\n * Time per output token generated after the first token for successful responses\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GEN_AI_SERVER_TIME_PER_OUTPUT_TOKEN = 'gen_ai.server.time_per_output_token' as const;\n\n/**\n * Time to generate first token for successful responses\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GEN_AI_SERVER_TIME_TO_FIRST_TOKEN = 'gen_ai.server.time_to_first_token' as const;\n\n/**\n * Heap size target percentage configured by the user, otherwise 100.\n *\n * @note The value range is [0.0,100.0]. Computed from `/gc/gogc:percent`.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GO_CONFIG_GOGC = 'go.config.gogc' as const;\n\n/**\n * Count of live goroutines.\n *\n * @note Computed from `/sched/goroutines:goroutines`.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GO_GOROUTINE_COUNT = 'go.goroutine.count' as const;\n\n/**\n * Memory allocated to the heap by the application.\n *\n * @note Computed from `/gc/heap/allocs:bytes`.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GO_MEMORY_ALLOCATED = 'go.memory.allocated' as const;\n\n/**\n * Count of allocations to the heap by the application.\n *\n * @note Computed from `/gc/heap/allocs:objects`.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GO_MEMORY_ALLOCATIONS = 'go.memory.allocations' as const;\n\n/**\n * Heap size target for the end of the GC cycle.\n *\n * @note Computed from `/gc/heap/goal:bytes`.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GO_MEMORY_GC_GOAL = 'go.memory.gc.goal' as const;\n\n/**\n * Go runtime memory limit configured by the user, if a limit exists.\n *\n * @note Computed from `/gc/gomemlimit:bytes`. This metric is excluded if the limit obtained from the Go runtime is math.MaxInt64.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GO_MEMORY_LIMIT = 'go.memory.limit' as const;\n\n/**\n * Memory used by the Go runtime.\n *\n * @note Computed from `(/memory/classes/total:bytes - /memory/classes/heap/released:bytes)`.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GO_MEMORY_USED = 'go.memory.used' as const;\n\n/**\n * The number of OS threads that can execute user-level Go code simultaneously.\n *\n * @note Computed from `/sched/gomaxprocs:threads`.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GO_PROCESSOR_LIMIT = 'go.processor.limit' as const;\n\n/**\n * The time goroutines have spent in the scheduler in a runnable state before actually running.\n *\n * @note Computed from `/sched/latencies:seconds`. Bucket boundaries are provided by the runtime, and are subject to change.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_GO_SCHEDULE_DURATION = 'go.schedule.duration' as const;\n\n/**\n * Number of active HTTP requests.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HTTP_CLIENT_ACTIVE_REQUESTS = 'http.client.active_requests' as const;\n\n/**\n * The duration of the successfully established outbound HTTP connections.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HTTP_CLIENT_CONNECTION_DURATION = 'http.client.connection.duration' as const;\n\n/**\n * Number of outbound HTTP connections that are currently active or idle on the client.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HTTP_CLIENT_OPEN_CONNECTIONS = 'http.client.open_connections' as const;\n\n/**\n * Size of HTTP client request bodies.\n *\n * @note The size of the request payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HTTP_CLIENT_REQUEST_BODY_SIZE = 'http.client.request.body.size' as const;\n\n/**\n * Size of HTTP client response bodies.\n *\n * @note The size of the response payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HTTP_CLIENT_RESPONSE_BODY_SIZE = 'http.client.response.body.size' as const;\n\n/**\n * Number of active HTTP server requests.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HTTP_SERVER_ACTIVE_REQUESTS = 'http.server.active_requests' as const;\n\n/**\n * Size of HTTP server request bodies.\n *\n * @note The size of the request payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HTTP_SERVER_REQUEST_BODY_SIZE = 'http.server.request.body.size' as const;\n\n/**\n * Size of HTTP server response bodies.\n *\n * @note The size of the response payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HTTP_SERVER_RESPONSE_BODY_SIZE = 'http.server.response.body.size' as const;\n\n/**\n * Energy consumed by the component\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HW_ENERGY = 'hw.energy' as const;\n\n/**\n * Number of errors encountered by the component\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HW_ERRORS = 'hw.errors' as const;\n\n/**\n * Ambient (external) temperature of the physical host\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HW_HOST_AMBIENT_TEMPERATURE = 'hw.host.ambient_temperature' as const;\n\n/**\n * Total energy consumed by the entire physical host, in joules\n *\n * @note The overall energy usage of a host **MUST** be reported using the specific `hw.host.energy` and `hw.host.power` metrics **only**, instead of the generic `hw.energy` and `hw.power` described in the previous section, to prevent summing up overlapping values.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HW_HOST_ENERGY = 'hw.host.energy' as const;\n\n/**\n * By how many degrees Celsius the temperature of the physical host can be increased, before reaching a warning threshold on one of the internal sensors\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HW_HOST_HEATING_MARGIN = 'hw.host.heating_margin' as const;\n\n/**\n * Instantaneous power consumed by the entire physical host in Watts (`hw.host.energy` is preferred)\n *\n * @note The overall energy usage of a host **MUST** be reported using the specific `hw.host.energy` and `hw.host.power` metrics **only**, instead of the generic `hw.energy` and `hw.power` described in the previous section, to prevent summing up overlapping values.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HW_HOST_POWER = 'hw.host.power' as const;\n\n/**\n * Instantaneous power consumed by the component\n *\n * @note It is recommended to report `hw.energy` instead of `hw.power` when possible.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HW_POWER = 'hw.power' as const;\n\n/**\n * Operational status: `1` (true) or `0` (false) for each of the possible states\n *\n * @note `hw.status` is currently specified as an *UpDownCounter* but would ideally be represented using a [*StateSet* as defined in OpenMetrics](https://github.com/prometheus/OpenMetrics/blob/v1.0.0/specification/OpenMetrics.md#stateset). This semantic convention will be updated once *StateSet* is specified in OpenTelemetry. This planned change is not expected to have any consequence on the way users query their timeseries backend to retrieve the values of `hw.status` over time.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_HW_STATUS = 'hw.status' as const;\n\n/**\n * Number of buffers in the pool.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_JVM_BUFFER_COUNT = 'jvm.buffer.count' as const;\n\n/**\n * Measure of total memory capacity of buffers.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_JVM_BUFFER_MEMORY_LIMIT = 'jvm.buffer.memory.limit' as const;\n\n/**\n * Deprecated, use `jvm.buffer.memory.used` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `jvm.buffer.memory.used`.\n */\nexport const METRIC_JVM_BUFFER_MEMORY_USAGE = 'jvm.buffer.memory.usage' as const;\n\n/**\n * Measure of memory used by buffers.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_JVM_BUFFER_MEMORY_USED = 'jvm.buffer.memory.used' as const;\n\n/**\n * Number of open file descriptors as reported by the JVM.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_JVM_FILE_DESCRIPTOR_COUNT = 'jvm.file_descriptor.count' as const;\n\n/**\n * Measure of initial memory requested.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_JVM_MEMORY_INIT = 'jvm.memory.init' as const;\n\n/**\n * Average CPU load of the whole system for the last minute as reported by the JVM.\n *\n * @note The value range is [0,n], where n is the number of CPU cores - or a negative number if the value is not available. This utilization is not defined as being for the specific interval since last measurement (unlike `system.cpu.utilization`). [Reference](https://docs.oracle.com/en/java/javase/17/docs/api/java.management/java/lang/management/OperatingSystemMXBean.html#getSystemLoadAverage()).\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_JVM_SYSTEM_CPU_LOAD_1M = 'jvm.system.cpu.load_1m' as const;\n\n/**\n * Recent CPU utilization for the whole system as reported by the JVM.\n *\n * @note The value range is [0.0,1.0]. This utilization is not defined as being for the specific interval since last measurement (unlike `system.cpu.utilization`). [Reference](https://docs.oracle.com/en/java/javase/17/docs/api/jdk.management/com/sun/management/OperatingSystemMXBean.html#getCpuLoad()).\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_JVM_SYSTEM_CPU_UTILIZATION = 'jvm.system.cpu.utilization' as const;\n\n/**\n * The number of actively running jobs for a cronjob\n *\n * @note This metric aligns with the `active` field of the\n * [K8s CronJobStatus](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#cronjobstatus-v1-batch).\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.cronjob`](../resource/k8s.md#cronjob) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_CRONJOB_ACTIVE_JOBS = 'k8s.cronjob.active_jobs' as const;\n\n/**\n * Number of nodes that are running at least 1 daemon pod and are supposed to run the daemon pod\n *\n * @note This metric aligns with the `currentNumberScheduled` field of the\n * [K8s DaemonSetStatus](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#daemonsetstatus-v1-apps).\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.daemonset`](../resource/k8s.md#daemonset) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_DAEMONSET_CURRENT_SCHEDULED_NODES = 'k8s.daemonset.current_scheduled_nodes' as const;\n\n/**\n * Number of nodes that should be running the daemon pod (including nodes currently running the daemon pod)\n *\n * @note This metric aligns with the `desiredNumberScheduled` field of the\n * [K8s DaemonSetStatus](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#daemonsetstatus-v1-apps).\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.daemonset`](../resource/k8s.md#daemonset) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_DAEMONSET_DESIRED_SCHEDULED_NODES = 'k8s.daemonset.desired_scheduled_nodes' as const;\n\n/**\n * Number of nodes that are running the daemon pod, but are not supposed to run the daemon pod\n *\n * @note This metric aligns with the `numberMisscheduled` field of the\n * [K8s DaemonSetStatus](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#daemonsetstatus-v1-apps).\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.daemonset`](../resource/k8s.md#daemonset) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_DAEMONSET_MISSCHEDULED_NODES = 'k8s.daemonset.misscheduled_nodes' as const;\n\n/**\n * Number of nodes that should be running the daemon pod and have one or more of the daemon pod running and ready\n *\n * @note This metric aligns with the `numberReady` field of the\n * [K8s DaemonSetStatus](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#daemonsetstatus-v1-apps).\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.daemonset`](../resource/k8s.md#daemonset) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_DAEMONSET_READY_NODES = 'k8s.daemonset.ready_nodes' as const;\n\n/**\n * Total number of available replica pods (ready for at least minReadySeconds) targeted by this deployment\n *\n * @note This metric aligns with the `availableReplicas` field of the\n * [K8s DeploymentStatus](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#deploymentstatus-v1-apps).\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.deployment`](../resource/k8s.md#deployment) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_DEPLOYMENT_AVAILABLE_PODS = 'k8s.deployment.available_pods' as const;\n\n/**\n * Number of desired replica pods in this deployment\n *\n * @note This metric aligns with the `replicas` field of the\n * [K8s DeploymentSpec](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#deploymentspec-v1-apps).\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.deployment`](../resource/k8s.md#deployment) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_DEPLOYMENT_DESIRED_PODS = 'k8s.deployment.desired_pods' as const;\n\n/**\n * Current number of replica pods managed by this horizontal pod autoscaler, as last seen by the autoscaler\n *\n * @note This metric aligns with the `currentReplicas` field of the\n * [K8s HorizontalPodAutoscalerStatus](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#horizontalpodautoscalerstatus-v2-autoscaling)\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.hpa`](../resource/k8s.md#horizontalpodautoscaler) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_HPA_CURRENT_PODS = 'k8s.hpa.current_pods' as const;\n\n/**\n * Desired number of replica pods managed by this horizontal pod autoscaler, as last calculated by the autoscaler\n *\n * @note This metric aligns with the `desiredReplicas` field of the\n * [K8s HorizontalPodAutoscalerStatus](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#horizontalpodautoscalerstatus-v2-autoscaling)\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.hpa`](../resource/k8s.md#horizontalpodautoscaler) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_HPA_DESIRED_PODS = 'k8s.hpa.desired_pods' as const;\n\n/**\n * The upper limit for the number of replica pods to which the autoscaler can scale up\n *\n * @note This metric aligns with the `maxReplicas` field of the\n * [K8s HorizontalPodAutoscalerSpec](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#horizontalpodautoscalerspec-v2-autoscaling)\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.hpa`](../resource/k8s.md#horizontalpodautoscaler) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_HPA_MAX_PODS = 'k8s.hpa.max_pods' as const;\n\n/**\n * The lower limit for the number of replica pods to which the autoscaler can scale down\n *\n * @note This metric aligns with the `minReplicas` field of the\n * [K8s HorizontalPodAutoscalerSpec](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#horizontalpodautoscalerspec-v2-autoscaling)\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.hpa`](../resource/k8s.md#horizontalpodautoscaler) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_HPA_MIN_PODS = 'k8s.hpa.min_pods' as const;\n\n/**\n * The number of pending and actively running pods for a job\n *\n * @note This metric aligns with the `active` field of the\n * [K8s JobStatus](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#jobstatus-v1-batch).\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.job`](../resource/k8s.md#job) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_JOB_ACTIVE_PODS = 'k8s.job.active_pods' as const;\n\n/**\n * The desired number of successfully finished pods the job should be run with\n *\n * @note This metric aligns with the `completions` field of the\n * [K8s JobSpec](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#jobspec-v1-batch).\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.job`](../resource/k8s.md#job) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_JOB_DESIRED_SUCCESSFUL_PODS = 'k8s.job.desired_successful_pods' as const;\n\n/**\n * The number of pods which reached phase Failed for a job\n *\n * @note This metric aligns with the `failed` field of the\n * [K8s JobStatus](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#jobstatus-v1-batch).\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.job`](../resource/k8s.md#job) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_JOB_FAILED_PODS = 'k8s.job.failed_pods' as const;\n\n/**\n * The max desired number of pods the job should run at any given time\n *\n * @note This metric aligns with the `parallelism` field of the\n * [K8s JobSpec](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#jobspec-v1-batch).\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.job`](../resource/k8s.md#job) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_JOB_MAX_PARALLEL_PODS = 'k8s.job.max_parallel_pods' as const;\n\n/**\n * The number of pods which reached phase Succeeded for a job\n *\n * @note This metric aligns with the `succeeded` field of the\n * [K8s JobStatus](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#jobstatus-v1-batch).\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.job`](../resource/k8s.md#job) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_JOB_SUCCESSFUL_PODS = 'k8s.job.successful_pods' as const;\n\n/**\n * Describes number of K8s namespaces that are currently in a given phase.\n *\n * @note This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.namespace`](../resource/k8s.md#namespace) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_NAMESPACE_PHASE = 'k8s.namespace.phase' as const;\n\n/**\n * Total CPU time consumed\n *\n * @note Total CPU time consumed by the specific Node on all available CPU cores\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_NODE_CPU_TIME = 'k8s.node.cpu.time' as const;\n\n/**\n * Node's CPU usage, measured in cpus. Range from 0 to the number of allocatable CPUs\n *\n * @note CPU usage of the specific Node on all available CPU cores, averaged over the sample window\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_NODE_CPU_USAGE = 'k8s.node.cpu.usage' as const;\n\n/**\n * Memory usage of the Node\n *\n * @note Total memory usage of the Node\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_NODE_MEMORY_USAGE = 'k8s.node.memory.usage' as const;\n\n/**\n * Node network errors\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_NODE_NETWORK_ERRORS = 'k8s.node.network.errors' as const;\n\n/**\n * Network bytes for the Node\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_NODE_NETWORK_IO = 'k8s.node.network.io' as const;\n\n/**\n * The time the Node has been running\n *\n * @note Instrumentations **SHOULD** use a gauge with type `double` and measure uptime in seconds as a floating point number with the highest precision available.\n * The actual accuracy would depend on the instrumentation and operating system.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_NODE_UPTIME = 'k8s.node.uptime' as const;\n\n/**\n * Total CPU time consumed\n *\n * @note Total CPU time consumed by the specific Pod on all available CPU cores\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_POD_CPU_TIME = 'k8s.pod.cpu.time' as const;\n\n/**\n * Pod's CPU usage, measured in cpus. Range from 0 to the number of allocatable CPUs\n *\n * @note CPU usage of the specific Pod on all available CPU cores, averaged over the sample window\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_POD_CPU_USAGE = 'k8s.pod.cpu.usage' as const;\n\n/**\n * Memory usage of the Pod\n *\n * @note Total memory usage of the Pod\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_POD_MEMORY_USAGE = 'k8s.pod.memory.usage' as const;\n\n/**\n * Pod network errors\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_POD_NETWORK_ERRORS = 'k8s.pod.network.errors' as const;\n\n/**\n * Network bytes for the Pod\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_POD_NETWORK_IO = 'k8s.pod.network.io' as const;\n\n/**\n * The time the Pod has been running\n *\n * @note Instrumentations **SHOULD** use a gauge with type `double` and measure uptime in seconds as a floating point number with the highest precision available.\n * The actual accuracy would depend on the instrumentation and operating system.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_POD_UPTIME = 'k8s.pod.uptime' as const;\n\n/**\n * Total number of available replica pods (ready for at least minReadySeconds) targeted by this replicaset\n *\n * @note This metric aligns with the `availableReplicas` field of the\n * [K8s ReplicaSetStatus](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#replicasetstatus-v1-apps).\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.replicaset`](../resource/k8s.md#replicaset) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_REPLICASET_AVAILABLE_PODS = 'k8s.replicaset.available_pods' as const;\n\n/**\n * Number of desired replica pods in this replicaset\n *\n * @note This metric aligns with the `replicas` field of the\n * [K8s ReplicaSetSpec](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#replicasetspec-v1-apps).\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.replicaset`](../resource/k8s.md#replicaset) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_REPLICASET_DESIRED_PODS = 'k8s.replicaset.desired_pods' as const;\n\n/**\n * Deprecated, use `k8s.replicationcontroller.available_pods` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `k8s.replicationcontroller.available_pods`.\n */\nexport const METRIC_K8S_REPLICATION_CONTROLLER_AVAILABLE_PODS = 'k8s.replication_controller.available_pods' as const;\n\n/**\n * Deprecated, use `k8s.replicationcontroller.desired_pods` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `k8s.replicationcontroller.desired_pods`.\n */\nexport const METRIC_K8S_REPLICATION_CONTROLLER_DESIRED_PODS = 'k8s.replication_controller.desired_pods' as const;\n\n/**\n * Total number of available replica pods (ready for at least minReadySeconds) targeted by this replication controller\n *\n * @note This metric aligns with the `availableReplicas` field of the\n * [K8s ReplicationControllerStatus](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#replicationcontrollerstatus-v1-core)\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.replicationcontroller`](../resource/k8s.md#replicationcontroller) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_REPLICATIONCONTROLLER_AVAILABLE_PODS = 'k8s.replicationcontroller.available_pods' as const;\n\n/**\n * Number of desired replica pods in this replication controller\n *\n * @note This metric aligns with the `replicas` field of the\n * [K8s ReplicationControllerSpec](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#replicationcontrollerspec-v1-core)\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.replicationcontroller`](../resource/k8s.md#replicationcontroller) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_REPLICATIONCONTROLLER_DESIRED_PODS = 'k8s.replicationcontroller.desired_pods' as const;\n\n/**\n * The number of replica pods created by the statefulset controller from the statefulset version indicated by currentRevision\n *\n * @note This metric aligns with the `currentReplicas` field of the\n * [K8s StatefulSetStatus](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#statefulsetstatus-v1-apps).\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.statefulset`](../resource/k8s.md#statefulset) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_STATEFULSET_CURRENT_PODS = 'k8s.statefulset.current_pods' as const;\n\n/**\n * Number of desired replica pods in this statefulset\n *\n * @note This metric aligns with the `replicas` field of the\n * [K8s StatefulSetSpec](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#statefulsetspec-v1-apps).\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.statefulset`](../resource/k8s.md#statefulset) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_STATEFULSET_DESIRED_PODS = 'k8s.statefulset.desired_pods' as const;\n\n/**\n * The number of replica pods created for this statefulset with a Ready Condition\n *\n * @note This metric aligns with the `readyReplicas` field of the\n * [K8s StatefulSetStatus](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#statefulsetstatus-v1-apps).\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.statefulset`](../resource/k8s.md#statefulset) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_STATEFULSET_READY_PODS = 'k8s.statefulset.ready_pods' as const;\n\n/**\n * Number of replica pods created by the statefulset controller from the statefulset version indicated by updateRevision\n *\n * @note This metric aligns with the `updatedReplicas` field of the\n * [K8s StatefulSetStatus](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#statefulsetstatus-v1-apps).\n *\n * This metric **SHOULD**, at a minimum, be reported against a\n * [`k8s.statefulset`](../resource/k8s.md#statefulset) resource.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_K8S_STATEFULSET_UPDATED_PODS = 'k8s.statefulset.updated_pods' as const;\n\n/**\n * Number of messages that were delivered to the application.\n *\n * @note Records the number of messages pulled from the broker or number of messages dispatched to the application in push-based scenarios.\n * The metric **SHOULD** be reported once per message delivery. For example, if receiving and processing operations are both instrumented for a single message delivery, this counter is incremented when the message is received and not reported when it is processed.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_MESSAGING_CLIENT_CONSUMED_MESSAGES = 'messaging.client.consumed.messages' as const;\n\n/**\n * Duration of messaging operation initiated by a producer or consumer client.\n *\n * @note This metric **SHOULD NOT** be used to report processing duration - processing duration is reported in `messaging.process.duration` metric.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_MESSAGING_CLIENT_OPERATION_DURATION = 'messaging.client.operation.duration' as const;\n\n/**\n * Deprecated. Use `messaging.client.sent.messages` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `messaging.client.sent.messages`.\n */\nexport const METRIC_MESSAGING_CLIENT_PUBLISHED_MESSAGES = 'messaging.client.published.messages' as const;\n\n/**\n * Number of messages producer attempted to send to the broker.\n *\n * @note This metric **MUST NOT** count messages that were created but haven't yet been sent.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_MESSAGING_CLIENT_SENT_MESSAGES = 'messaging.client.sent.messages' as const;\n\n/**\n * Duration of processing operation.\n *\n * @note This metric **MUST** be reported for operations with `messaging.operation.type` that matches `process`.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_MESSAGING_PROCESS_DURATION = 'messaging.process.duration' as const;\n\n/**\n * Deprecated. Use `messaging.client.consumed.messages` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `messaging.client.consumed.messages`.\n */\nexport const METRIC_MESSAGING_PROCESS_MESSAGES = 'messaging.process.messages' as const;\n\n/**\n * Deprecated. Use `messaging.client.operation.duration` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `messaging.client.operation.duration`.\n */\nexport const METRIC_MESSAGING_PUBLISH_DURATION = 'messaging.publish.duration' as const;\n\n/**\n * Deprecated. Use `messaging.client.sent.messages` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `messaging.client.sent.messages`.\n */\nexport const METRIC_MESSAGING_PUBLISH_MESSAGES = 'messaging.publish.messages' as const;\n\n/**\n * Deprecated. Use `messaging.client.operation.duration` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `messaging.client.operation.duration`.\n */\nexport const METRIC_MESSAGING_RECEIVE_DURATION = 'messaging.receive.duration' as const;\n\n/**\n * Deprecated. Use `messaging.client.consumed.messages` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `messaging.client.consumed.messages`.\n */\nexport const METRIC_MESSAGING_RECEIVE_MESSAGES = 'messaging.receive.messages' as const;\n\n/**\n * Event loop maximum delay.\n *\n * @note Value can be retrieved from value `histogram.max` of [`perf_hooks.monitorEventLoopDelay([options])`](https://nodejs.org/api/perf_hooks.html#perf_hooksmonitoreventloopdelayoptions)\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_NODEJS_EVENTLOOP_DELAY_MAX = 'nodejs.eventloop.delay.max' as const;\n\n/**\n * Event loop mean delay.\n *\n * @note Value can be retrieved from value `histogram.mean` of [`perf_hooks.monitorEventLoopDelay([options])`](https://nodejs.org/api/perf_hooks.html#perf_hooksmonitoreventloopdelayoptions)\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_NODEJS_EVENTLOOP_DELAY_MEAN = 'nodejs.eventloop.delay.mean' as const;\n\n/**\n * Event loop minimum delay.\n *\n * @note Value can be retrieved from value `histogram.min` of [`perf_hooks.monitorEventLoopDelay([options])`](https://nodejs.org/api/perf_hooks.html#perf_hooksmonitoreventloopdelayoptions)\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_NODEJS_EVENTLOOP_DELAY_MIN = 'nodejs.eventloop.delay.min' as const;\n\n/**\n * Event loop 50 percentile delay.\n *\n * @note Value can be retrieved from value `histogram.percentile(50)` of [`perf_hooks.monitorEventLoopDelay([options])`](https://nodejs.org/api/perf_hooks.html#perf_hooksmonitoreventloopdelayoptions)\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_NODEJS_EVENTLOOP_DELAY_P50 = 'nodejs.eventloop.delay.p50' as const;\n\n/**\n * Event loop 90 percentile delay.\n *\n * @note Value can be retrieved from value `histogram.percentile(90)` of [`perf_hooks.monitorEventLoopDelay([options])`](https://nodejs.org/api/perf_hooks.html#perf_hooksmonitoreventloopdelayoptions)\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_NODEJS_EVENTLOOP_DELAY_P90 = 'nodejs.eventloop.delay.p90' as const;\n\n/**\n * Event loop 99 percentile delay.\n *\n * @note Value can be retrieved from value `histogram.percentile(99)` of [`perf_hooks.monitorEventLoopDelay([options])`](https://nodejs.org/api/perf_hooks.html#perf_hooksmonitoreventloopdelayoptions)\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_NODEJS_EVENTLOOP_DELAY_P99 = 'nodejs.eventloop.delay.p99' as const;\n\n/**\n * Event loop standard deviation delay.\n *\n * @note Value can be retrieved from value `histogram.stddev` of [`perf_hooks.monitorEventLoopDelay([options])`](https://nodejs.org/api/perf_hooks.html#perf_hooksmonitoreventloopdelayoptions)\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_NODEJS_EVENTLOOP_DELAY_STDDEV = 'nodejs.eventloop.delay.stddev' as const;\n\n/**\n * Cumulative duration of time the event loop has been in each state.\n *\n * @note Value can be retrieved from [`performance.eventLoopUtilization([utilization1[, utilization2]])`](https://nodejs.org/api/perf_hooks.html#performanceeventlooputilizationutilization1-utilization2)\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_NODEJS_EVENTLOOP_TIME = 'nodejs.eventloop.time' as const;\n\n/**\n * Event loop utilization.\n *\n * @note The value range is [0.0, 1.0] and can be retrieved from [`performance.eventLoopUtilization([utilization1[, utilization2]])`](https://nodejs.org/api/perf_hooks.html#performanceeventlooputilizationutilization1-utilization2)\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_NODEJS_EVENTLOOP_UTILIZATION = 'nodejs.eventloop.utilization' as const;\n\n/**\n * The number of log records for which the export has finished, either successful or failed\n *\n * @note For successful exports, `error.type` **MUST NOT** be set. For failed exports, `error.type` **MUST** contain the failure cause.\n * For exporters with partial success semantics (e.g. OTLP with `rejected_log_records`), rejected log records **MUST** count as failed and only non-rejected log records count as success.\n * If no rejection reason is available, `rejected` **SHOULD** be used as value for `error.type`.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_OTEL_SDK_EXPORTER_LOG_EXPORTED = 'otel.sdk.exporter.log.exported' as const;\n\n/**\n * The number of log records which were passed to the exporter, but that have not been exported yet (neither successful, nor failed)\n *\n * @note For successful exports, `error.type` **MUST NOT** be set. For failed exports, `error.type` **MUST** contain the failure cause.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_OTEL_SDK_EXPORTER_LOG_INFLIGHT = 'otel.sdk.exporter.log.inflight' as const;\n\n/**\n * The number of metric data points for which the export has finished, either successful or failed\n *\n * @note For successful exports, `error.type` **MUST NOT** be set. For failed exports, `error.type` **MUST** contain the failure cause.\n * For exporters with partial success semantics (e.g. OTLP with `rejected_data_points`), rejected data points **MUST** count as failed and only non-rejected data points count as success.\n * If no rejection reason is available, `rejected` **SHOULD** be used as value for `error.type`.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_OTEL_SDK_EXPORTER_METRIC_DATA_POINT_EXPORTED = 'otel.sdk.exporter.metric_data_point.exported' as const;\n\n/**\n * The number of metric data points which were passed to the exporter, but that have not been exported yet (neither successful, nor failed)\n *\n * @note For successful exports, `error.type` **MUST NOT** be set. For failed exports, `error.type` **MUST** contain the failure cause.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_OTEL_SDK_EXPORTER_METRIC_DATA_POINT_INFLIGHT = 'otel.sdk.exporter.metric_data_point.inflight' as const;\n\n/**\n * The duration of exporting a batch of telemetry records.\n *\n * @note This metric defines successful operations using the full success definitions for [http](https://github.com/open-telemetry/opentelemetry-proto/blob/v1.5.0/docs/specification.md#full-success-1)\n * and [grpc](https://github.com/open-telemetry/opentelemetry-proto/blob/v1.5.0/docs/specification.md#full-success). Anything else is defined as an unsuccessful operation. For successful\n * operations, `error.type` **MUST NOT** be set. For unsuccessful export operations, `error.type` **MUST** contain a relevant failure cause.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_OTEL_SDK_EXPORTER_OPERATION_DURATION = 'otel.sdk.exporter.operation.duration' as const;\n\n/**\n * The number of spans for which the export has finished, either successful or failed\n *\n * @note For successful exports, `error.type` **MUST NOT** be set. For failed exports, `error.type` **MUST** contain the failure cause.\n * For exporters with partial success semantics (e.g. OTLP with `rejected_spans`), rejected spans **MUST** count as failed and only non-rejected spans count as success.\n * If no rejection reason is available, `rejected` **SHOULD** be used as value for `error.type`.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_OTEL_SDK_EXPORTER_SPAN_EXPORTED = 'otel.sdk.exporter.span.exported' as const;\n\n/**\n * Deprecated, use `otel.sdk.exporter.span.exported` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `otel.sdk.exporter.span.exported`.\n */\nexport const METRIC_OTEL_SDK_EXPORTER_SPAN_EXPORTED_COUNT = 'otel.sdk.exporter.span.exported.count' as const;\n\n/**\n * The number of spans which were passed to the exporter, but that have not been exported yet (neither successful, nor failed)\n *\n * @note For successful exports, `error.type` **MUST NOT** be set. For failed exports, `error.type` **MUST** contain the failure cause.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_OTEL_SDK_EXPORTER_SPAN_INFLIGHT = 'otel.sdk.exporter.span.inflight' as const;\n\n/**\n * Deprecated, use `otel.sdk.exporter.span.inflight` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `otel.sdk.exporter.span.inflight`.\n */\nexport const METRIC_OTEL_SDK_EXPORTER_SPAN_INFLIGHT_COUNT = 'otel.sdk.exporter.span.inflight.count' as const;\n\n/**\n * The number of logs submitted to enabled SDK Loggers\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_OTEL_SDK_LOG_CREATED = 'otel.sdk.log.created' as const;\n\n/**\n * The duration of the collect operation of the metric reader.\n *\n * @note For successful collections, `error.type` **MUST NOT** be set. For failed collections, `error.type` **SHOULD** contain the failure cause.\n * It can happen that metrics collection is successful for some MetricProducers, while others fail. In that case `error.type` **SHOULD** be set to any of the failure causes.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_OTEL_SDK_METRIC_READER_COLLECTION_DURATION = 'otel.sdk.metric_reader.collection.duration' as const;\n\n/**\n * The number of log records for which the processing has finished, either successful or failed\n *\n * @note For successful processing, `error.type` **MUST NOT** be set. For failed processing, `error.type` **MUST** contain the failure cause.\n * For the SDK Simple and Batching Log Record Processor a log record is considered to be processed already when it has been submitted to the exporter,\n * not when the corresponding export call has finished.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_OTEL_SDK_PROCESSOR_LOG_PROCESSED = 'otel.sdk.processor.log.processed' as const;\n\n/**\n * The maximum number of log records the queue of a given instance of an SDK Log Record processor can hold\n *\n * @note Only applies to Log Record processors which use a queue, e.g. the SDK Batching Log Record Processor.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_OTEL_SDK_PROCESSOR_LOG_QUEUE_CAPACITY = 'otel.sdk.processor.log.queue.capacity' as const;\n\n/**\n * The number of log records in the queue of a given instance of an SDK log processor\n *\n * @note Only applies to log record processors which use a queue, e.g. the SDK Batching Log Record Processor.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_OTEL_SDK_PROCESSOR_LOG_QUEUE_SIZE = 'otel.sdk.processor.log.queue.size' as const;\n\n/**\n * The number of spans for which the processing has finished, either successful or failed\n *\n * @note For successful processing, `error.type` **MUST NOT** be set. For failed processing, `error.type` **MUST** contain the failure cause.\n * For the SDK Simple and Batching Span Processor a span is considered to be processed already when it has been submitted to the exporter, not when the corresponding export call has finished.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_OTEL_SDK_PROCESSOR_SPAN_PROCESSED = 'otel.sdk.processor.span.processed' as const;\n\n/**\n * Deprecated, use `otel.sdk.processor.span.processed` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `otel.sdk.processor.span.processed`.\n */\nexport const METRIC_OTEL_SDK_PROCESSOR_SPAN_PROCESSED_COUNT = 'otel.sdk.processor.span.processed.count' as const;\n\n/**\n * The maximum number of spans the queue of a given instance of an SDK span processor can hold\n *\n * @note Only applies to span processors which use a queue, e.g. the SDK Batching Span Processor.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_OTEL_SDK_PROCESSOR_SPAN_QUEUE_CAPACITY = 'otel.sdk.processor.span.queue.capacity' as const;\n\n/**\n * The number of spans in the queue of a given instance of an SDK span processor\n *\n * @note Only applies to span processors which use a queue, e.g. the SDK Batching Span Processor.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_OTEL_SDK_PROCESSOR_SPAN_QUEUE_SIZE = 'otel.sdk.processor.span.queue.size' as const;\n\n/**\n * The number of created spans for which the end operation was called\n *\n * @note For spans with `recording=true`: Implementations **MUST** record both `otel.sdk.span.live` and `otel.sdk.span.ended`.\n * For spans with `recording=false`: If implementations decide to record this metric, they **MUST** also record `otel.sdk.span.live`.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_OTEL_SDK_SPAN_ENDED = 'otel.sdk.span.ended' as const;\n\n/**\n * Deprecated, use `otel.sdk.span.ended` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `otel.sdk.span.ended`.\n */\nexport const METRIC_OTEL_SDK_SPAN_ENDED_COUNT = 'otel.sdk.span.ended.count' as const;\n\n/**\n * The number of created spans for which the end operation has not been called yet\n *\n * @note For spans with `recording=true`: Implementations **MUST** record both `otel.sdk.span.live` and `otel.sdk.span.ended`.\n * For spans with `recording=false`: If implementations decide to record this metric, they **MUST** also record `otel.sdk.span.ended`.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_OTEL_SDK_SPAN_LIVE = 'otel.sdk.span.live' as const;\n\n/**\n * Deprecated, use `otel.sdk.span.live` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `otel.sdk.span.live`.\n */\nexport const METRIC_OTEL_SDK_SPAN_LIVE_COUNT = 'otel.sdk.span.live.count' as const;\n\n/**\n * Number of times the process has been context switched.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_PROCESS_CONTEXT_SWITCHES = 'process.context_switches' as const;\n\n/**\n * Total CPU seconds broken down by different states.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_PROCESS_CPU_TIME = 'process.cpu.time' as const;\n\n/**\n * Difference in process.cpu.time since the last measurement, divided by the elapsed time and number of CPUs available to the process.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_PROCESS_CPU_UTILIZATION = 'process.cpu.utilization' as const;\n\n/**\n * Disk bytes transferred.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_PROCESS_DISK_IO = 'process.disk.io' as const;\n\n/**\n * The amount of physical memory in use.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_PROCESS_MEMORY_USAGE = 'process.memory.usage' as const;\n\n/**\n * The amount of committed virtual memory.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_PROCESS_MEMORY_VIRTUAL = 'process.memory.virtual' as const;\n\n/**\n * Network bytes transferred.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_PROCESS_NETWORK_IO = 'process.network.io' as const;\n\n/**\n * Number of file descriptors in use by the process.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_PROCESS_OPEN_FILE_DESCRIPTOR_COUNT = 'process.open_file_descriptor.count' as const;\n\n/**\n * Number of page faults the process has made.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_PROCESS_PAGING_FAULTS = 'process.paging.faults' as const;\n\n/**\n * Process threads count.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_PROCESS_THREAD_COUNT = 'process.thread.count' as const;\n\n/**\n * The time the process has been running.\n *\n * @note Instrumentations **SHOULD** use a gauge with type `double` and measure uptime in seconds as a floating point number with the highest precision available.\n * The actual accuracy would depend on the instrumentation and operating system.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_PROCESS_UPTIME = 'process.uptime' as const;\n\n/**\n * Measures the duration of outbound RPC.\n *\n * @note While streaming RPCs may record this metric as start-of-batch\n * to end-of-batch, it's hard to interpret in practice.\n *\n * **Streaming**: N/A.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_RPC_CLIENT_DURATION = 'rpc.client.duration' as const;\n\n/**\n * Measures the size of RPC request messages (uncompressed).\n *\n * @note **Streaming**: Recorded per message in a streaming batch\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_RPC_CLIENT_REQUEST_SIZE = 'rpc.client.request.size' as const;\n\n/**\n * Measures the number of messages received per RPC.\n *\n * @note Should be 1 for all non-streaming RPCs.\n *\n * **Streaming**: This metric is required for server and client streaming RPCs\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_RPC_CLIENT_REQUESTS_PER_RPC = 'rpc.client.requests_per_rpc' as const;\n\n/**\n * Measures the size of RPC response messages (uncompressed).\n *\n * @note **Streaming**: Recorded per response in a streaming batch\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_RPC_CLIENT_RESPONSE_SIZE = 'rpc.client.response.size' as const;\n\n/**\n * Measures the number of messages sent per RPC.\n *\n * @note Should be 1 for all non-streaming RPCs.\n *\n * **Streaming**: This metric is required for server and client streaming RPCs\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_RPC_CLIENT_RESPONSES_PER_RPC = 'rpc.client.responses_per_rpc' as const;\n\n/**\n * Measures the duration of inbound RPC.\n *\n * @note While streaming RPCs may record this metric as start-of-batch\n * to end-of-batch, it's hard to interpret in practice.\n *\n * **Streaming**: N/A.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_RPC_SERVER_DURATION = 'rpc.server.duration' as const;\n\n/**\n * Measures the size of RPC request messages (uncompressed).\n *\n * @note **Streaming**: Recorded per message in a streaming batch\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_RPC_SERVER_REQUEST_SIZE = 'rpc.server.request.size' as const;\n\n/**\n * Measures the number of messages received per RPC.\n *\n * @note Should be 1 for all non-streaming RPCs.\n *\n * **Streaming** : This metric is required for server and client streaming RPCs\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_RPC_SERVER_REQUESTS_PER_RPC = 'rpc.server.requests_per_rpc' as const;\n\n/**\n * Measures the size of RPC response messages (uncompressed).\n *\n * @note **Streaming**: Recorded per response in a streaming batch\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_RPC_SERVER_RESPONSE_SIZE = 'rpc.server.response.size' as const;\n\n/**\n * Measures the number of messages sent per RPC.\n *\n * @note Should be 1 for all non-streaming RPCs.\n *\n * **Streaming**: This metric is required for server and client streaming RPCs\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_RPC_SERVER_RESPONSES_PER_RPC = 'rpc.server.responses_per_rpc' as const;\n\n/**\n * Deprecated. Use `cpu.frequency` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `cpu.frequency`.\n */\nexport const METRIC_SYSTEM_CPU_FREQUENCY = 'system.cpu.frequency' as const;\n\n/**\n * Reports the number of logical (virtual) processor cores created by the operating system to manage multitasking\n *\n * @note Calculated by multiplying the number of sockets by the number of cores per socket, and then by the number of threads per core\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_CPU_LOGICAL_COUNT = 'system.cpu.logical.count' as const;\n\n/**\n * Reports the number of actual physical processor cores on the hardware\n *\n * @note Calculated by multiplying the number of sockets by the number of cores per socket\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_CPU_PHYSICAL_COUNT = 'system.cpu.physical.count' as const;\n\n/**\n * Deprecated. Use `cpu.time` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `cpu.time`.\n */\nexport const METRIC_SYSTEM_CPU_TIME = 'system.cpu.time' as const;\n\n/**\n * Deprecated. Use `cpu.utilization` instead.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `cpu.utilization`.\n */\nexport const METRIC_SYSTEM_CPU_UTILIZATION = 'system.cpu.utilization' as const;\n\n/**\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_DISK_IO = 'system.disk.io' as const;\n\n/**\n * Time disk spent activated\n *\n * @note The real elapsed time (\"wall clock\") used in the I/O path (time from operations running in parallel are not counted). Measured as:\n *\n *   - Linux: Field 13 from [procfs-diskstats](https://www.kernel.org/doc/Documentation/ABI/testing/procfs-diskstats)\n *   - Windows: The complement of\n *     [\"Disk% Idle Time\"](https://learn.microsoft.com/archive/blogs/askcore/windows-performance-monitor-disk-counters-explained#windows-performance-monitor-disk-counters-explained)\n *     performance counter: `uptime * (100 - \"Disk\\% Idle Time\") / 100`\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_DISK_IO_TIME = 'system.disk.io_time' as const;\n\n/**\n * The total storage capacity of the disk\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_DISK_LIMIT = 'system.disk.limit' as const;\n\n/**\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_DISK_MERGED = 'system.disk.merged' as const;\n\n/**\n * Sum of the time each operation took to complete\n *\n * @note Because it is the sum of time each request took, parallel-issued requests each contribute to make the count grow. Measured as:\n *\n *   - Linux: Fields 7 & 11 from [procfs-diskstats](https://www.kernel.org/doc/Documentation/ABI/testing/procfs-diskstats)\n *   - Windows: \"Avg. Disk sec/Read\" perf counter multiplied by \"Disk Reads/sec\" perf counter (similar for Writes)\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_DISK_OPERATION_TIME = 'system.disk.operation_time' as const;\n\n/**\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_DISK_OPERATIONS = 'system.disk.operations' as const;\n\n/**\n * The total storage capacity of the filesystem\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_FILESYSTEM_LIMIT = 'system.filesystem.limit' as const;\n\n/**\n * Reports a filesystem's space usage across different states.\n *\n * @note The sum of all `system.filesystem.usage` values over the different `system.filesystem.state` attributes\n * **SHOULD** equal the total storage capacity of the filesystem, that is `system.filesystem.limit`.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_FILESYSTEM_USAGE = 'system.filesystem.usage' as const;\n\n/**\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_FILESYSTEM_UTILIZATION = 'system.filesystem.utilization' as const;\n\n/**\n * An estimate of how much memory is available for starting new applications, without causing swapping\n *\n * @note This is an alternative to `system.memory.usage` metric with `state=free`.\n * Linux starting from 3.14 exports \"available\" memory. It takes \"free\" memory as a baseline, and then factors in kernel-specific values.\n * This is supposed to be more accurate than just \"free\" memory.\n * For reference, see the calculations [here](https://superuser.com/a/980821).\n * See also `MemAvailable` in [/proc/meminfo](https://man7.org/linux/man-pages/man5/proc.5.html).\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_LINUX_MEMORY_AVAILABLE = 'system.linux.memory.available' as const;\n\n/**\n * Reports the memory used by the Linux kernel for managing caches of frequently used objects.\n *\n * @note The sum over the `reclaimable` and `unreclaimable` state values in `linux.memory.slab.usage` **SHOULD** be equal to the total slab memory available on the system.\n * Note that the total slab memory is not constant and may vary over time.\n * See also the [Slab allocator](https://blogs.oracle.com/linux/post/understanding-linux-kernel-memory-statistics) and `Slab` in [/proc/meminfo](https://man7.org/linux/man-pages/man5/proc.5.html).\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_LINUX_MEMORY_SLAB_USAGE = 'system.linux.memory.slab.usage' as const;\n\n/**\n * Total memory available in the system.\n *\n * @note Its value **SHOULD** equal the sum of `system.memory.state` over all states.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_MEMORY_LIMIT = 'system.memory.limit' as const;\n\n/**\n * Shared memory used (mostly by tmpfs).\n *\n * @note Equivalent of `shared` from [`free` command](https://man7.org/linux/man-pages/man1/free.1.html) or\n * `Shmem` from [`/proc/meminfo`](https://man7.org/linux/man-pages/man5/proc.5.html)\"\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_MEMORY_SHARED = 'system.memory.shared' as const;\n\n/**\n * Reports memory in use by state.\n *\n * @note The sum over all `system.memory.state` values **SHOULD** equal the total memory\n * available on the system, that is `system.memory.limit`.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_MEMORY_USAGE = 'system.memory.usage' as const;\n\n/**\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_MEMORY_UTILIZATION = 'system.memory.utilization' as const;\n\n/**\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_NETWORK_CONNECTIONS = 'system.network.connections' as const;\n\n/**\n * Count of packets that are dropped or discarded even though there was no error\n *\n * @note Measured as:\n *\n *   - Linux: the `drop` column in `/proc/dev/net` ([source](https://web.archive.org/web/20180321091318/http://www.onlamp.com/pub/a/linux/2000/11/16/LinuxAdmin.html))\n *   - Windows: [`InDiscards`/`OutDiscards`](https://docs.microsoft.com/windows/win32/api/netioapi/ns-netioapi-mib_if_row2)\n *     from [`GetIfEntry2`](https://docs.microsoft.com/windows/win32/api/netioapi/nf-netioapi-getifentry2)\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_NETWORK_DROPPED = 'system.network.dropped' as const;\n\n/**\n * Count of network errors detected\n *\n * @note Measured as:\n *\n *   - Linux: the `errs` column in `/proc/dev/net` ([source](https://web.archive.org/web/20180321091318/http://www.onlamp.com/pub/a/linux/2000/11/16/LinuxAdmin.html)).\n *   - Windows: [`InErrors`/`OutErrors`](https://docs.microsoft.com/windows/win32/api/netioapi/ns-netioapi-mib_if_row2)\n *     from [`GetIfEntry2`](https://docs.microsoft.com/windows/win32/api/netioapi/nf-netioapi-getifentry2).\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_NETWORK_ERRORS = 'system.network.errors' as const;\n\n/**\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_NETWORK_IO = 'system.network.io' as const;\n\n/**\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_NETWORK_PACKETS = 'system.network.packets' as const;\n\n/**\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_PAGING_FAULTS = 'system.paging.faults' as const;\n\n/**\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_PAGING_OPERATIONS = 'system.paging.operations' as const;\n\n/**\n * Unix swap or windows pagefile usage\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_PAGING_USAGE = 'system.paging.usage' as const;\n\n/**\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_PAGING_UTILIZATION = 'system.paging.utilization' as const;\n\n/**\n * Total number of processes in each state\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_PROCESS_COUNT = 'system.process.count' as const;\n\n/**\n * Total number of processes created over uptime of the host\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_PROCESS_CREATED = 'system.process.created' as const;\n\n/**\n * The time the system has been running\n *\n * @note Instrumentations **SHOULD** use a gauge with type `double` and measure uptime in seconds as a floating point number with the highest precision available.\n * The actual accuracy would depend on the instrumentation and operating system.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_SYSTEM_UPTIME = 'system.uptime' as const;\n\n/**\n * Garbage collection duration.\n *\n * @note The values can be retrieved from [`perf_hooks.PerformanceObserver(...).observe({ entryTypes: ['gc'] })`](https://nodejs.org/api/perf_hooks.html#performanceobserverobserveoptions)\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_V8JS_GC_DURATION = 'v8js.gc.duration' as const;\n\n/**\n * Heap space available size.\n *\n * @note Value can be retrieved from value `space_available_size` of [`v8.getHeapSpaceStatistics()`](https://nodejs.org/api/v8.html#v8getheapspacestatistics)\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_V8JS_HEAP_SPACE_AVAILABLE_SIZE = 'v8js.heap.space.available_size' as const;\n\n/**\n * Committed size of a heap space.\n *\n * @note Value can be retrieved from value `physical_space_size` of [`v8.getHeapSpaceStatistics()`](https://nodejs.org/api/v8.html#v8getheapspacestatistics)\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_V8JS_HEAP_SPACE_PHYSICAL_SIZE = 'v8js.heap.space.physical_size' as const;\n\n/**\n * Total heap memory size pre-allocated.\n *\n * @note The value can be retrieved from value `space_size` of [`v8.getHeapSpaceStatistics()`](https://nodejs.org/api/v8.html#v8getheapspacestatistics)\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_V8JS_MEMORY_HEAP_LIMIT = 'v8js.memory.heap.limit' as const;\n\n/**\n * Heap Memory size allocated.\n *\n * @note The value can be retrieved from value `space_used_size` of [`v8.getHeapSpaceStatistics()`](https://nodejs.org/api/v8.html#v8getheapspacestatistics)\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_V8JS_MEMORY_HEAP_USED = 'v8js.memory.heap.used' as const;\n\n/**\n * The number of changes (pull requests/merge requests/changelists) in a repository, categorized by their state (e.g. open or merged)\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_VCS_CHANGE_COUNT = 'vcs.change.count' as const;\n\n/**\n * The time duration a change (pull request/merge request/changelist) has been in a given state.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_VCS_CHANGE_DURATION = 'vcs.change.duration' as const;\n\n/**\n * The amount of time since its creation it took a change (pull request/merge request/changelist) to get the first approval.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_VCS_CHANGE_TIME_TO_APPROVAL = 'vcs.change.time_to_approval' as const;\n\n/**\n * The amount of time since its creation it took a change (pull request/merge request/changelist) to get merged into the target(base) ref.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_VCS_CHANGE_TIME_TO_MERGE = 'vcs.change.time_to_merge' as const;\n\n/**\n * The number of unique contributors to a repository\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_VCS_CONTRIBUTOR_COUNT = 'vcs.contributor.count' as const;\n\n/**\n * The number of refs of type branch or tag in a repository.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_VCS_REF_COUNT = 'vcs.ref.count' as const;\n\n/**\n * The number of lines added/removed in a ref (branch) relative to the ref from the `vcs.ref.base.name` attribute.\n *\n * @note This metric should be reported for each `vcs.line_change.type` value. For example if a ref added 3 lines and removed 2 lines,\n * instrumentation **SHOULD** report two measurements: 3 and 2 (both positive numbers).\n * If number of lines added/removed should be calculated from the start of time, then `vcs.ref.base.name` **SHOULD** be set to an empty string.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_VCS_REF_LINES_DELTA = 'vcs.ref.lines_delta' as const;\n\n/**\n * The number of revisions (commits) a ref (branch) is ahead/behind the branch from the `vcs.ref.base.name` attribute\n *\n * @note This metric should be reported for each `vcs.revision_delta.direction` value. For example if branch `a` is 3 commits behind and 2 commits ahead of `trunk`,\n * instrumentation **SHOULD** report two measurements: 3 and 2 (both positive numbers) and `vcs.ref.base.name` is set to `trunk`.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_VCS_REF_REVISIONS_DELTA = 'vcs.ref.revisions_delta' as const;\n\n/**\n * Time a ref (branch) created from the default branch (trunk) has existed. The `ref.type` attribute will always be `branch`\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_VCS_REF_TIME = 'vcs.ref.time' as const;\n\n/**\n * The number of repositories in an organization.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_VCS_REPOSITORY_COUNT = 'vcs.repository.count' as const;\n\n"]}