/**
 * This file acts as a central hub for all user-related action controllers.
 * It imports individual action handlers from the '/actions' directory
 * and exports them for use in the main router file. This keeps the
 * routing logic clean and the action logic well-organized.
 */

import { incrementBusinessPlanCount } from './limit/incrementBusinessPlanCount.js';
import { incrementPdfUploadCount } from './limit/incrementPdfUploadCount.js';
import { incrementMessageCount } from './limit/incrementMessageCount.js';
import { incrementInvestorPitchCount } from './limit/incrementInvestorPitchCount.js';
import { incrementBusinessQACount } from './limit/incrementBusinessQACount.js';

// Export all the imported functions so they can be accessed from a single point.
export {
  incrementBusinessPlanCount,
  incrementPdfUploadCount,
  incrementMessageCount,
  incrementInvestorPitchCount,
  incrementBusinessQACount,
};