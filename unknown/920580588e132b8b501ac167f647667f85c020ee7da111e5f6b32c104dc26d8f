import React, { useState, useMemo } from 'react';
import {
    FiCopy,
    FiDownload,
    FiRefreshCw,
    FiCheckCircle,
    FiTarget,
    FiAlertTriangle,
    FiTrendingUp,
    FiList,
    FiGrid,
} from 'react-icons/fi';
import { GoLightBulb } from 'react-icons/go'; // Using a valid icon from Github Octicons
import './rtl-styles.css';

//==================================================================
//  1. Utility Functions
//==================================================================

const copyToClipboard = async (text) => {
    try {
        await navigator.clipboard.writeText(text);
        return true;
    } catch (err) {
        console.error('Failed to copy text: ', err);
        return false;
    }
};

const downloadAsText = (content, filename = 'business-qa-answer.txt') => {
    const element = document.createElement('a');
    const file = new Blob([content], { type: 'text/plain' });
    element.href = URL.createObjectURL(file);
    element.download = filename;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
};

//==================================================================
//  2. Enhanced Styled Components for Different Content Types
//==================================================================

const SectionHeader = ({ children }) => (
    <h2 className="text-2xl font-bold text-blue-400 mb-6 flex items-center border-b border-blue-500/30 pb-3">
        <FiTarget className="w-6 h-6 mr-3" />
        {children}
    </h2>
);

const SubSectionHeader = ({ children }) => (
    <h3 className="text-xl font-semibold text-slate-200 mb-4 border-l-4 border-blue-500 pl-4 py-2 bg-blue-500/5 rounded-r-lg">
        {children}
    </h3>
);

const Paragraph = ({ children }) => (
    <p className="text-slate-300 leading-relaxed mb-6 text-base">
        {children}
    </p>
);

// Enhanced Responsive Table Components
const ResponsiveTable = ({ children, headers }) => {
    const [isMobile, setIsMobile] = useState(false);

    React.useEffect(() => {
        const checkMobile = () => setIsMobile(window.innerWidth < 768);
        checkMobile();
        window.addEventListener('resize', checkMobile);
        return () => window.removeEventListener('resize', checkMobile);
    }, []);

    if (isMobile && headers) {
        // Mobile card layout
        return (
            <div className="bg-gradient-to-r from-slate-800/50 to-slate-700/50 border border-slate-600 rounded-xl p-6 mb-6 shadow-lg">
                <div className="flex items-center mb-4">
                    <div className="bg-blue-500/20 p-2 rounded-lg mr-3">
                        <FiGrid className="w-5 h-5 text-blue-400" />
                    </div>
                    <span className="font-bold text-blue-400 text-lg">Data Analysis</span>
                </div>
                <div className="space-y-4">
                    {React.Children.map(children, (row, rowIndex) => {
                        if (!row || !row.props.children) return null;
                        const cells = React.Children.toArray(row.props.children);
                        return (
                            <div key={rowIndex} className="bg-slate-900/30 rounded-lg p-4 border border-slate-600/50">
                                <div className="text-sm font-semibold text-slate-200 mb-3">Entry {rowIndex + 1}</div>
                                {cells.map((cell, cellIndex) => (
                                    <div key={cellIndex} className="flex justify-between items-start py-2 border-b border-slate-700/30 last:border-b-0">
                                        <span className="text-slate-400 text-sm font-medium mr-3">{headers[cellIndex]}:</span>
                                        <span className="text-slate-300 text-sm text-right flex-1">{cell.props.children}</span>
                                    </div>
                                ))}
                            </div>
                        );
                    })}
                </div>
            </div>
        );
    }

    // Desktop table layout
    return (
        <div className="bg-gradient-to-r from-slate-800/50 to-slate-700/50 border border-slate-600 rounded-xl p-6 mb-6 shadow-lg overflow-hidden">
            <div className="flex items-center mb-4">
                <div className="bg-blue-500/20 p-2 rounded-lg mr-3">
                    <FiGrid className="w-5 h-5 text-blue-400" />
                </div>
                <span className="font-bold text-blue-400 text-lg">Data Analysis</span>
            </div>
            <div className="overflow-x-auto">
                <table className="w-full text-sm">
                    {headers && (
                        <thead>
                            <tr className="border-b-2 border-blue-500/30">
                                {headers.map((header, index) => (
                                    <th key={index} className="text-left py-4 px-4 font-bold text-slate-200 bg-blue-500/10 first:rounded-l-lg last:rounded-r-lg">
                                        {header}
                                    </th>
                                ))}
                            </tr>
                        </thead>
                    )}
                    <tbody>
                        {children}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

const TableRow = ({ children, isHeader = false }) => (
    <tr className={`border-b border-slate-700/30 hover:bg-slate-700/20 transition-all duration-200 ${isHeader ? 'bg-slate-700/30' : ''}`}>
        {children}
    </tr>
);

const TableCell = ({ children, isHeader = false }) => (
    <td className={`py-4 px-4 text-slate-300 ${isHeader ? 'font-semibold text-slate-200 bg-slate-700/20' : ''} border-r border-slate-700/20 last:border-r-0`}>
        <div className="leading-relaxed">
            {children}
        </div>
    </td>
);

const RecommendationList = ({ children }) => (
    <div className="bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-500/30 rounded-xl p-6 mb-6 shadow-lg">
        <div className="flex items-center mb-4">
            <div className="bg-green-500/20 p-2 rounded-lg mr-3">
                <FiCheckCircle className="w-5 h-5 text-green-400" />
            </div>
            <span className="font-bold text-green-400 text-lg">Strategic Recommendations</span>
        </div>
        <ul className="space-y-3">
            {children}
        </ul>
    </div>
);

const StepList = ({ children }) => (
    <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-blue-500/30 rounded-xl p-6 mb-6 shadow-lg">
        <div className="flex items-center mb-4">
            <div className="bg-blue-500/20 p-2 rounded-lg mr-3">
                <FiList className="w-5 h-5 text-blue-400" />
            </div>
            <span className="font-bold text-blue-400 text-lg">Implementation Steps</span>
        </div>
        <ol className="space-y-3">
            {children}
        </ol>
    </div>
);

const RiskList = ({ children }) => (
    <div className="bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border border-yellow-500/30 rounded-xl p-6 mb-6 shadow-lg">
        <div className="flex items-center mb-4">
            <div className="bg-yellow-500/20 p-2 rounded-lg mr-3">
                <FiAlertTriangle className="w-5 h-5 text-yellow-400" />
            </div>
            <span className="font-bold text-yellow-400 text-lg">Key Considerations & Risks</span>
        </div>
        <ul className="space-y-3">
            {children}
        </ul>
    </div>
);

const InsightBox = ({ children }) => (
    <div className="bg-gradient-to-r from-purple-500/10 to-indigo-500/10 border border-purple-500/30 rounded-xl p-6 mb-6 shadow-lg">
        <div className="flex items-center mb-4">
            <div className="bg-purple-500/20 p-2 rounded-lg mr-3">
                <GoLightBulb className="w-5 h-5 text-purple-400" />
            </div>
            <span className="font-bold text-purple-400 text-lg">Expert Insight</span>
        </div>
        <div className="text-slate-300 italic text-base leading-relaxed border-l-4 border-purple-500/50 pl-4">
            {children}
        </div>
    </div>
);

const RecommendationItem = ({ children }) => (
    <li className="flex items-start p-3 bg-green-500/5 rounded-lg border border-green-500/20 hover:bg-green-500/10 transition-colors">
        <div className="bg-green-500/20 p-1.5 rounded-full mr-3 mt-0.5 flex-shrink-0">
            <FiTrendingUp className="w-4 h-4 text-green-400" />
        </div>
        <span className="text-slate-300 leading-relaxed">{children}</span>
    </li>
);

const StepItem = ({ children, stepNumber }) => (
    <li className="flex items-start p-3 bg-blue-500/5 rounded-lg border border-blue-500/20 hover:bg-blue-500/10 transition-colors">
        <span className="bg-gradient-to-br from-blue-500 to-blue-600 text-white text-sm font-bold rounded-full w-8 h-8 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0 shadow-lg">
            {stepNumber}
        </span>
        <span className="text-slate-300 leading-relaxed">{children}</span>
    </li>
);

const RiskItem = ({ children }) => (
    <li className="flex items-start p-3 bg-yellow-500/5 rounded-lg border border-yellow-500/20 hover:bg-yellow-500/10 transition-colors">
        <div className="bg-yellow-500/20 p-1.5 rounded-full mr-3 mt-0.5 flex-shrink-0">
            <FiAlertTriangle className="w-4 h-4 text-yellow-400" />
        </div>
        <span className="text-slate-300 leading-relaxed">{children}</span>
    </li>
);

// Simple response component for direct answers
const SimpleAnswer = ({ children }) => (
    <div className="bg-gradient-to-r from-slate-800/30 to-slate-700/30 border border-slate-600/50 rounded-xl p-6 mb-6">
        <div className="text-slate-300 leading-relaxed text-base space-y-4">
            {children}
        </div>
    </div>
);

// Compact insight box for simple responses
const CompactInsight = ({ children }) => (
    <div className="bg-gradient-to-r from-purple-900/20 to-blue-900/20 border border-purple-500/30 rounded-lg p-4 mt-4">
        <div className="flex items-start">
            <div className="bg-purple-500/20 p-2 rounded-lg mr-3 flex-shrink-0">
                <GoLightBulb className="w-4 h-4 text-purple-400" />
            </div>
            <div className="text-slate-300 text-sm leading-relaxed">
                {children}
            </div>
        </div>
    </div>
);

//==================================================================
//  3. Enhanced Parser Function with Intelligent Content Detection
//==================================================================

/**
 * Analyzes content to determine if it's a simple response or structured document
 * @param {string} content - The AI response content
 * @returns {object} Analysis result with content type and formatting needs
 */
const analyzeContentStructure = (content) => {
    const lines = content.split('\n').filter(line => line.trim() !== '');

    // Count different types of structural elements
    const titleCount = lines.filter(line => line.trim().startsWith('~H~')).length;
    const sectionCount = lines.filter(line => line.trim().startsWith('~S_SUB~')).length;
    const listCount = lines.filter(line =>
        line.includes('_LIST_START~') || line.includes('_LIST_END~')
    ).length;
    const tableCount = lines.filter(line =>
        line.includes('~TABLE_START~') || line.includes('~TABLE_END~')
    ).length;

    // Determine content complexity
    const isSimple = titleCount === 0 && sectionCount <= 1 && listCount === 0 && tableCount === 0;
    const isStructured = sectionCount > 2 || listCount > 2 || tableCount > 0;
    const hasTitle = titleCount > 0;

    return {
        isSimple,
        isStructured,
        hasTitle,
        complexity: isSimple ? 'low' : isStructured ? 'high' : 'medium'
    };
};

/**
 * Parses simple content with minimal formatting
 * @param {Array} lines - Array of content lines
 * @returns {Array} Parsed React elements
 */
const parseSimpleContent = (lines) => {
    const elements = [];
    let currentParagraphs = [];
    let currentRecommendations = [];
    let inRecommendationList = false;

    lines.forEach((line, index) => {
        const trimmedLine = line.trim();

        // Handle recommendation lists
        if (trimmedLine === '~REC_LIST_START~') {
            // Add any accumulated paragraphs first
            if (currentParagraphs.length > 0) {
                elements.push(
                    <SimpleAnswer key={`simple-answer-${index}`}>
                        {currentParagraphs.map((para, pIndex) => (
                            <p key={pIndex}>{para}</p>
                        ))}
                    </SimpleAnswer>
                );
                currentParagraphs = [];
            }
            inRecommendationList = true;
            return;
        }

        if (trimmedLine === '~REC_LIST_END~') {
            if (currentRecommendations.length > 0) {
                elements.push(
                    <div key={`simple-recs-${index}`} className="mt-4">
                        <h4 className="text-lg font-semibold text-slate-200 mb-3">Key Recommendations:</h4>
                        <RecommendationList>
                            {currentRecommendations}
                        </RecommendationList>
                    </div>
                );
                currentRecommendations = [];
            }
            inRecommendationList = false;
            return;
        }

        if (inRecommendationList && trimmedLine.startsWith('~REC~')) {
            currentRecommendations.push(
                <RecommendationItem key={`simple-rec-${index}`}>
                    {trimmedLine.substring(5).trim()}
                </RecommendationItem>
            );
            return;
        }

        // Handle insights
        if (trimmedLine.startsWith('~INSIGHT~')) {
            // Add any accumulated paragraphs first
            if (currentParagraphs.length > 0) {
                elements.push(
                    <SimpleAnswer key={`simple-answer-${index}`}>
                        {currentParagraphs.map((para, pIndex) => (
                            <p key={pIndex}>{para}</p>
                        ))}
                    </SimpleAnswer>
                );
                currentParagraphs = [];
            }

            elements.push(
                <CompactInsight key={`simple-insight-${index}`}>
                    {trimmedLine.substring(9).trim()}
                </CompactInsight>
            );
            return;
        }

        // Handle paragraphs (both tagged and untagged)
        if (trimmedLine.startsWith('~P~')) {
            currentParagraphs.push(trimmedLine.substring(3).trim());
        } else if (!trimmedLine.startsWith('~') && trimmedLine.length > 0) {
            currentParagraphs.push(trimmedLine);
        }
    });

    // Add any remaining paragraphs
    if (currentParagraphs.length > 0) {
        elements.push(
            <SimpleAnswer key="final-simple-answer">
                {currentParagraphs.map((para, pIndex) => (
                    <p key={pIndex}>{para}</p>
                ))}
            </SimpleAnswer>
        );
    }

    return elements;
};

const parseQAContent = (content) => {
    const lines = content.split('\n').filter(line => line.trim() !== '');
    const elements = [];
    let currentRecommendations = [];
    let currentSteps = [];
    let currentRisks = [];
    let currentTableRows = [];
    let tableHeaders = [];
    let stepCounter = 1;
    let inRecommendationList = false;
    let inStepList = false;
    let inRiskList = false;
    let inTable = false;

    // Analyze content structure for intelligent rendering
    const contentAnalysis = analyzeContentStructure(content);

    // Handle simple responses with minimal formatting
    if (contentAnalysis.isSimple) {
        return parseSimpleContent(lines);
    }

    lines.forEach((line, index) => {
        const trimmedLine = line.trim();

        // Handle list and table endings
        if (trimmedLine === '~REC_LIST_END~') {
            if (currentRecommendations.length > 0) {
                elements.push(
                    <RecommendationList key={`rec-list-${index}`}>
                        {currentRecommendations}
                    </RecommendationList>
                );
                currentRecommendations = [];
            }
            inRecommendationList = false;
            return;
        }

        if (trimmedLine === '~STEP_LIST_END~') {
            if (currentSteps.length > 0) {
                elements.push(
                    <StepList key={`step-list-${index}`}>
                        {currentSteps}
                    </StepList>
                );
                currentSteps = [];
            }
            inStepList = false;
            stepCounter = 1;
            return;
        }

        if (trimmedLine === '~RISK_LIST_END~') {
            if (currentRisks.length > 0) {
                elements.push(
                    <RiskList key={`risk-list-${index}`}>
                        {currentRisks}
                    </RiskList>
                );
                currentRisks = [];
            }
            inRiskList = false;
            return;
        }

        if (trimmedLine === '~TABLE_END~') {
            if (currentTableRows.length > 0) {
                elements.push(
                    <ResponsiveTable key={`table-${index}`} headers={tableHeaders}>
                        {currentTableRows}
                    </ResponsiveTable>
                );
                currentTableRows = [];
                tableHeaders = [];
            }
            inTable = false;
            return;
        }

        // Handle list and table beginnings
        if (trimmedLine === '~REC_LIST_START~') {
            inRecommendationList = true;
            return;
        }

        if (trimmedLine === '~STEP_LIST_START~') {
            inStepList = true;
            return;
        }

        if (trimmedLine === '~RISK_LIST_START~') {
            inRiskList = true;
            return;
        }

        if (trimmedLine === '~TABLE_START~') {
            inTable = true;
            return;
        }

        if (trimmedLine.startsWith('~TABLE_HEADER~')) {
            const headerContent = trimmedLine.substring(14).trim();
            tableHeaders = headerContent.split('|').map(h => h.trim());
            return;
        }

        // Handle content within lists and tables
        if (inRecommendationList && trimmedLine.startsWith('~REC~')) {
            currentRecommendations.push(
                <RecommendationItem key={`rec-${index}`}>
                    {trimmedLine.substring(5).trim()}
                </RecommendationItem>
            );
            return;
        }

        if (inStepList && trimmedLine.startsWith('~STEP~')) {
            currentSteps.push(
                <StepItem key={`step-${index}`} stepNumber={stepCounter}>
                    {trimmedLine.substring(6).trim()}
                </StepItem>
            );
            stepCounter++;
            return;
        }

        if (inRiskList && trimmedLine.startsWith('~RISK~')) {
            currentRisks.push(
                <RiskItem key={`risk-${index}`}>
                    {trimmedLine.substring(6).trim()}
                </RiskItem>
            );
            return;
        }

        if (inTable && trimmedLine.startsWith('~TABLE_ROW~')) {
            const rowContent = trimmedLine.substring(11).trim();
            const cells = rowContent.split('|').map(cell => cell.trim());
            currentTableRows.push(
                <TableRow key={`row-${index}`}>
                    {cells.map((cell, cellIndex) => (
                        <TableCell key={cellIndex}>{cell}</TableCell>
                    ))}
                </TableRow>
            );
            return;
        }

        // Handle other content types with intelligent formatting
        if (trimmedLine.startsWith('~H~')) {
            // Only render main title if content analysis suggests it's needed
            if (contentAnalysis.hasTitle && contentAnalysis.complexity !== 'low') {
                elements.push(
                    <SectionHeader key={index}>
                        {trimmedLine.substring(3).trim()}
                    </SectionHeader>
                );
            }
        } else if (trimmedLine.startsWith('~S_SUB~')) {
            // Render subsection headers based on content complexity
            if (contentAnalysis.complexity === 'high') {
                elements.push(
                    <SubSectionHeader key={index}>
                        {trimmedLine.substring(7).trim()}
                    </SubSectionHeader>
                );
            } else if (contentAnalysis.complexity === 'medium') {
                // Use a simpler header for medium complexity
                elements.push(
                    <h4 key={index} className="text-lg font-semibold text-slate-200 mb-3 mt-6">
                        {trimmedLine.substring(7).trim()}
                    </h4>
                );
            }
            // Skip headers entirely for low complexity content
        } else if (trimmedLine.startsWith('~P~')) {
            elements.push(
                <Paragraph key={index}>
                    {trimmedLine.substring(3).trim()}
                </Paragraph>
            );
        } else if (trimmedLine.startsWith('~INSIGHT~')) {
            // Use compact insight for simpler content
            if (contentAnalysis.complexity === 'low') {
                elements.push(
                    <CompactInsight key={index}>
                        {trimmedLine.substring(9).trim()}
                    </CompactInsight>
                );
            } else {
                elements.push(
                    <InsightBox key={index}>
                        {trimmedLine.substring(9).trim()}
                    </InsightBox>
                );
            }
        } else if (!trimmedLine.startsWith('~') && trimmedLine.length > 0) {
            // Handle untagged content as regular paragraphs
            elements.push(
                <Paragraph key={index}>
                    {trimmedLine}
                </Paragraph>
            );
        }
    });

    return elements;
};

//==================================================================
//  4. Language Detection and RTL Support
//==================================================================

const detectLanguageDirection = (text) => {
    // RTL language detection patterns
    const rtlPatterns = [
        /[\u0590-\u05FF]/, // Hebrew
        /[\u0600-\u06FF]/, // Arabic
        /[\u0750-\u077F]/, // Arabic Supplement
        /[\u08A0-\u08FF]/, // Arabic Extended-A
        /[\uFB50-\uFDFF]/, // Arabic Presentation Forms-A
        /[\uFE70-\uFEFF]/, // Arabic Presentation Forms-B
    ];

    return rtlPatterns.some(pattern => pattern.test(text)) ? 'rtl' : 'ltr';
};

const getLanguageClasses = (language) => {
    const languageMap = {
        'Arabic': 'lang-arabic',
        'Hebrew': 'lang-hebrew',
        'Chinese': 'lang-chinese',
        'Japanese': 'lang-japanese',
        'Korean': 'lang-korean',
        'Russian': 'lang-russian'
    };

    return languageMap[language] || '';
};

//==================================================================
//  5. Enhanced Main Component
//==================================================================

const FormattedQADisplay = ({ answerText, onReset, language = 'English' }) => {
    const [copySuccess, setCopySuccess] = useState(false);

    // Detect text direction based on content
    const textDirection = useMemo(() => detectLanguageDirection(answerText), [answerText]);

    // Get language-specific CSS classes
    const languageClasses = useMemo(() => getLanguageClasses(language), [language]);

    const handleCopy = async () => {
        const success = await copyToClipboard(answerText);
        if (success) {
            setCopySuccess(true);
            setTimeout(() => setCopySuccess(false), 2000);
        }
    };

    const handleDownload = () => {
        const filename = `business-qa-answer-${language}.txt`;
        downloadAsText(answerText, filename);
    };

    const parsedContent = parseQAContent(answerText);

    return (
        <div
            className={`w-full bg-gradient-to-br from-slate-900/60 to-slate-800/60 border border-slate-700/50 rounded-2xl p-6 md:p-8 animate-fade-in shadow-2xl backdrop-blur-sm ${languageClasses}`}
            dir={textDirection}
        >
            {/* Enhanced Header with action buttons */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4 border-b border-slate-700/50 pb-6">
                <div className="flex items-center">
                    <div className="bg-blue-500/20 p-3 rounded-xl mr-4">
                        <FiTarget className="w-6 h-6 text-blue-400" />
                    </div>
                    <div>
                        <h2 className="text-2xl font-bold text-white mb-1">Business Answer</h2>
                        <p className="text-slate-400 text-sm">Expert insights and strategic guidance</p>
                    </div>
                </div>
                <div className="flex gap-2">
                    <button
                        onClick={handleCopy}
                        className="flex items-center px-4 py-2.5 bg-slate-700/80 hover:bg-slate-600 text-slate-300 hover:text-white rounded-xl transition-all duration-200 text-sm font-medium shadow-lg hover:shadow-xl transform hover:scale-105"
                    >
                        {copySuccess ? <FiCheckCircle className="w-4 h-4 mr-2" /> : <FiCopy className="w-4 h-4 mr-2" />}
                        {copySuccess ? 'Copied!' : 'Copy'}
                    </button>
                    <button
                        onClick={handleDownload}
                        className="flex items-center px-4 py-2.5 bg-slate-700/80 hover:bg-slate-600 text-slate-300 hover:text-white rounded-xl transition-all duration-200 text-sm font-medium shadow-lg hover:shadow-xl transform hover:scale-105"
                    >
                        <FiDownload className="w-4 h-4 mr-2" />
                        Download
                    </button>
                    <button
                        onClick={onReset}
                        className="flex items-center px-4 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl transition-all duration-200 text-sm font-medium shadow-lg hover:shadow-xl transform hover:scale-105"
                    >
                        <FiRefreshCw className="w-4 h-4 mr-2" />
                        Ask Another
                    </button>
                </div>
            </div>

            {/* Enhanced formatted content with proper typography */}
            <div className={`prose prose-invert max-w-none ${textDirection === 'rtl' ? 'prose-rtl' : ''} ${languageClasses}`}>
                <div className="space-y-6">
                    {parsedContent}
                </div>
            </div>
        </div>
    );
};

export default FormattedQADisplay;