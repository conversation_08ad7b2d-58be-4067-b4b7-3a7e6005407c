import React, { Suspense, lazy } from 'react';
import { FiHelpCircle, FiLoader } from 'react-icons/fi';

// Lazily import the form component
const BusinessQAForm = lazy(() => import('./components/businessQA/form/BusinessQAForm'));

// Loading component for the form
const FormLoader = () => (
  <div className="w-full bg-slate-900/50 border border-slate-700 rounded-2xl p-8 flex flex-col items-center text-center animate-fade-in">
    <FiLoader className="w-8 h-8 text-yellow-400 animate-spin mb-4" />
    <h3 className="text-xl font-bold text-yellow-400 mb-2">Loading Business Q&A</h3>
    <p className="text-slate-400">Preparing your business consultation interface...</p>
  </div>
);

const BusinessQAPage = () => {
  return (
    // Use padding to prevent content from touching screen edges on mobile
    <div className="container justify-center items-center mx-auto text-white w-full min-h-full flex flex-col px-4">
      <main className="w-full flex-grow flex flex-col max-w-4xl justify-center py-10 md:py-20">
        <div className="bg-slate-800/50 rounded-2xl shadow-2xl border border-slate-700 p-6 md:p-10 flex flex-col items-center">
            <div className="flex items-center justify-center w-16 h-16 rounded-full bg-blue-500/10 border border-blue-400/30 mb-6">
                <FiHelpCircle className="w-8 h-8 text-blue-400" />
            </div>
            <h1 className="text-3xl md:text-4xl font-bold text-white mb-3 text-center">
              AI Business Q&A
            </h1>
            <p className="text-slate-400 text-center max-w-xl mb-10">
              Get expert business advice and strategic insights. Ask any business question and receive comprehensive, 
              actionable answers from our AI business consultant.
            </p>

            {/*
              The Suspense component shows a fallback UI (our FormLoader)
              while it waits for the lazy-loaded BusinessQAForm to be fetched.
            */}
            <Suspense fallback={<FormLoader />}>
              <BusinessQAForm />
            </Suspense>
        </div>
      </main>
    </div>
  );
};

export default BusinessQAPage;
