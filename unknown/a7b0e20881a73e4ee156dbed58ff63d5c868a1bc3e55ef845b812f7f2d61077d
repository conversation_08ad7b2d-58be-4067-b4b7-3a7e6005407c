// src/controllers/Tools/Business/businessQAPrompts.js

/**
 * Analyzes the question intent to determine appropriate response structure
 * @param {string} question - The user's question
 * @returns {object} Analysis result with response type and complexity
 */
const analyzeQuestionIntent = (question) => {
    const lowerQuestion = question.toLowerCase();

    // Simple question patterns - direct answers without extensive formatting
    const simplePatterns = [
        /^what is\s/,
        /^what are\s/,
        /^how much\s/,
        /^how many\s/,
        /^when should\s/,
        /^when is\s/,
        /^where can\s/,
        /^where should\s/,
        /^who is\s/,
        /^who should\s/,
        /^define\s/,
        /^explain\s/,
        /\?$/,
        /^is it\s/,
        /^can i\s/,
        /^should i\s/,
        /^do i need\s/,
        /^which\s/,
        /^why\s/,
        /^how do i\s/,
        /^what does\s/,
        /^how long\s/,
        /^how often\s/
    ];

    // Complex document patterns - require full structure
    const documentPatterns = [
        /business plan/,
        /strategic plan/,
        /marketing strategy/,
        /financial plan/,
        /investment proposal/,
        /market analysis/,
        /competitive analysis/,
        /swot analysis/,
        /business model/,
        /revenue model/,
        /growth strategy/,
        /expansion plan/,
        /risk assessment/,
        /feasibility study/
    ];

    // Structured analysis patterns - need organized sections
    const analysisPatterns = [
        /analyze/,
        /compare/,
        /evaluate/,
        /assess/,
        /review/,
        /breakdown/,
        /framework/,
        /methodology/,
        /process/,
        /steps to/,
        /how to implement/,
        /strategy for/
    ];

    const isSimple = simplePatterns.some(pattern => pattern.test(lowerQuestion));
    const isDocument = documentPatterns.some(pattern => pattern.test(lowerQuestion));
    const isAnalysis = analysisPatterns.some(pattern => pattern.test(lowerQuestion));

    // Additional intelligence: Check question length and complexity indicators
    const questionLength = question.length;
    const hasMultipleConcepts = (question.match(/and|&|,/g) || []).length > 2;
    const hasSpecificTerms = /detailed|comprehensive|complete|full|thorough|in-depth/.test(lowerQuestion);

    // Determine response type with enhanced logic
    if (isDocument || (hasSpecificTerms && questionLength > 50)) {
        return { type: 'document', complexity: 'high', needsTitle: true, needsStructure: true };
    } else if (isAnalysis || (hasMultipleConcepts && questionLength > 30)) {
        return { type: 'analysis', complexity: 'medium', needsTitle: true, needsStructure: true };
    } else if (isSimple || questionLength < 30) {
        return { type: 'simple', complexity: 'low', needsTitle: false, needsStructure: false };
    } else {
        // Default to medium complexity for unclear questions
        return { type: 'standard', complexity: 'medium', needsTitle: true, needsStructure: true };
    }
};

/**
 * Constructs an intelligent, context-aware prompt for generating business Q&A responses,
 * with conditional formatting based on question intent and complexity.
 * @param {object} formData - The validated data from the frontend form.
 * @returns {string} The complete prompt to be sent to the Gemini AI.
 */
export const buildBusinessQAPrompt = (formData) => {
    const {
        question,
        businessContext,
        industry,
        businessStage,
        specificArea,
        language = 'English'
    } = formData;

    // Analyze question intent to determine response structure
    const questionAnalysis = analyzeQuestionIntent(question);

    // Build context information
    const contextInfo = [];
    if (businessContext && businessContext.trim()) {
        contextInfo.push(`Business Context: ${businessContext}`);
    }
    if (industry && industry.trim()) {
        contextInfo.push(`Industry: ${industry}`);
    }
    if (businessStage && businessStage.trim()) {
        contextInfo.push(`Business Stage: ${businessStage}`);
    }
    if (specificArea && specificArea.trim()) {
        contextInfo.push(`Focus Area: ${specificArea}`);
    }

    const contextSection = contextInfo.length > 0
        ? `\n**Context Information:**\n${contextInfo.map(info => `- ${info}`).join('\n')}\n`
        : '';

    // Base role and language requirements
    const basePrompt = `
        **Role and Goal:**
        You are an expert business consultant, strategist, and advisor with over 20 years of experience helping businesses across all industries. Your task is to provide comprehensive, actionable, and insightful answers to business questions with intelligent formatting based on the question's complexity and intent.

        **IMPORTANT LANGUAGE REQUIREMENT:**
        Your ENTIRE response must be written in ${language}. Every single word, sentence, and explanation must be in ${language}. Do not mix languages.

        **Question to Answer:**
        "${question}"
        ${contextSection}

        **INTELLIGENT RESPONSE GUIDELINES:**
        Question Type: ${questionAnalysis.type}
        Complexity Level: ${questionAnalysis.complexity}
        Needs Title: ${questionAnalysis.needsTitle}
        Needs Structure: ${questionAnalysis.needsStructure}
    `;

    // Generate appropriate response structure based on question analysis
    return generateResponseStructure(basePrompt, questionAnalysis, question, language);
};

/**
 * Generates the appropriate response structure based on question analysis
 * @param {string} basePrompt - Base prompt with role and context
 * @param {object} questionAnalysis - Analysis of question intent
 * @param {string} question - Original question
 * @param {string} language - Response language
 * @returns {string} Complete prompt with appropriate structure
 */
const generateResponseStructure = (basePrompt, questionAnalysis, question, language) => {
    const { type, complexity, needsTitle, needsStructure } = questionAnalysis;

    if (type === 'simple') {
        return generateSimpleResponsePrompt(basePrompt, question, language);
    } else if (type === 'document') {
        return generateDocumentResponsePrompt(basePrompt, question, language);
    } else if (type === 'analysis') {
        return generateAnalysisResponsePrompt(basePrompt, question, language);
    } else {
        return generateStandardResponsePrompt(basePrompt, question, language);
    }
};

/**
 * Generates prompt for simple, direct questions
 */
const generateSimpleResponsePrompt = (basePrompt, question, language) => {
    return `${basePrompt}

        **Response Instructions for Simple Question:**
        This appears to be a straightforward question requiring a direct, concise answer. Provide a clear, actionable response without unnecessary formatting or extensive structure.

        **CRITICAL FORMATTING RULES:**
        - Do NOT use any titles or headers (~H~ or ~S_SUB~)
        - Keep the response conversational and direct
        - Use minimal structural elements
        - Focus on answering the question efficiently

        **Required Output Format:**
        Respond using ONLY these tags when absolutely necessary:
        - Use ~P~ for main answer paragraphs (or just write plain text)
        - Use ~REC_LIST_START~ and ~REC_LIST_END~ with ~REC~ items ONLY if you need to provide 2-3 specific recommendations
        - Use ~INSIGHT~ for ONE valuable expert insight ONLY if it adds significant value
        - NEVER use titles, headers, or complex structures

        **Example Simple Response:**
        ~P~ [Direct answer to the question in 1-2 sentences]
        ~P~ [Additional explanation or context if needed]
        ~REC_LIST_START~
        ~REC~ [First practical recommendation]
        ~REC~ [Second practical recommendation]
        ~REC_LIST_END~
        ~INSIGHT~ [One valuable insight that adds expert value]

        **Guidelines:**
        1. Answer the question directly in the first sentence
        2. Provide practical, actionable information
        3. Include specific examples when helpful
        4. Keep the response under 200 words when possible
        5. Focus on delivering immediate value
        6. Use conversational, professional language

        Generate your direct response now in ${language}, following the minimal format above.`;
};

/**
 * Generates prompt for document-type questions requiring comprehensive structure
 */
const generateDocumentResponsePrompt = (basePrompt, question, language) => {
    return `${basePrompt}

        **Response Instructions for Document/Plan Generation:**
        This question requires a comprehensive, well-structured document with full formatting and organization.

        **Required Output Structure & Format Instructions:**
        Generate a comprehensive business document following this EXACT structure. You MUST use the specified ~TAGS~ for each part. Each tagged element must be on a new line.

        --- START OF REQUIRED FORMAT ---
        ~H~ ${question.includes('plan') || question.includes('strategy') ? 'Business Document: ' : 'Business Analysis: '}${question.length > 50 ? question.substring(0, 50) + '...' : question}

        ~S_SUB~ Executive Summary
        ~P~ [Provide a comprehensive overview of the document's key findings and recommendations in 3-4 sentences.]

        ~S_SUB~ Detailed Analysis
        ~P~ [Provide thorough analysis with multiple paragraphs covering all relevant aspects.]

        ~S_SUB~ Strategic Recommendations
        ~REC_LIST_START~
        ~REC~ [First detailed, actionable recommendation with implementation guidance]
        ~REC~ [Second detailed, actionable recommendation with implementation guidance]
        ~REC~ [Third detailed, actionable recommendation with implementation guidance]
        ~REC~ [Fourth detailed, actionable recommendation with implementation guidance]
        ~REC_LIST_END~

        ~S_SUB~ Implementation Roadmap
        ~STEP_LIST_START~
        ~STEP~ Phase 1: [First major implementation phase with timeline]
        ~STEP~ Phase 2: [Second major implementation phase with timeline]
        ~STEP~ Phase 3: [Third major implementation phase with timeline]
        ~STEP~ Phase 4: [Fourth major implementation phase with timeline]
        ~STEP_LIST_END~

        ~S_SUB~ Risk Assessment & Mitigation
        ~RISK_LIST_START~
        ~RISK~ [Critical risk with mitigation strategy]
        ~RISK~ [Important risk with mitigation strategy]
        ~RISK~ [Operational risk with mitigation strategy]
        ~RISK~ [Financial risk with mitigation strategy]
        ~RISK_LIST_END~

        ~S_SUB~ Success Metrics & KPIs
        ~TABLE_START~
        ~TABLE_HEADER~ Metric | Target | Timeline | Measurement Method
        ~TABLE_ROW~ [Key metric] | [Specific target] | [Timeframe] | [How to measure]
        ~TABLE_ROW~ [Key metric] | [Specific target] | [Timeframe] | [How to measure]
        ~TABLE_ROW~ [Key metric] | [Specific target] | [Timeframe] | [How to measure]
        ~TABLE_END~

        ~S_SUB~ Financial Considerations
        ~P~ [Detailed financial analysis including costs, revenue projections, and ROI considerations.]

        ~S_SUB~ Next Steps & Action Items
        ~P~ [Specific next steps with clear ownership and timelines.]

        ~S_SUB~ Expert Insight
        ~INSIGHT~ [High-value industry insight that demonstrates deep expertise and adds strategic value.]

        --- END OF REQUIRED FORMAT ---

        Generate the comprehensive business document now in ${language}.`;
};

/**
 * Generates prompt for analysis-type questions requiring structured evaluation
 */
const generateAnalysisResponsePrompt = (basePrompt, question, language) => {
    return `${basePrompt}

        **Response Instructions for Analysis Question:**
        This question requires structured analysis with organized sections but not full document formatting.

        **Required Output Structure & Format Instructions:**
        Generate a structured business analysis following this format. Use the specified ~TAGS~ for organization.

        --- START OF REQUIRED FORMAT ---
        ~H~ Analysis: ${question.length > 50 ? question.substring(0, 50) + '...' : question}

        ~S_SUB~ Key Findings
        ~P~ [Summarize the main findings of your analysis in 2-3 sentences.]

        ~S_SUB~ Detailed Evaluation
        ~P~ [Provide comprehensive analysis breaking down the key components and factors.]

        ~S_SUB~ Strategic Recommendations
        ~REC_LIST_START~
        ~REC~ [First actionable recommendation based on analysis]
        ~REC~ [Second actionable recommendation based on analysis]
        ~REC~ [Third actionable recommendation based on analysis]
        ~REC_LIST_END~

        ~S_SUB~ Implementation Approach
        ~STEP_LIST_START~
        ~STEP~ [First key implementation step]
        ~STEP~ [Second key implementation step]
        ~STEP~ [Third key implementation step]
        ~STEP_LIST_END~

        ~S_SUB~ Considerations & Risks
        ~RISK_LIST_START~
        ~RISK~ [Primary risk or consideration]
        ~RISK~ [Secondary risk or consideration]
        ~RISK_LIST_END~

        ~S_SUB~ Success Indicators
        ~P~ [Explain how to measure success and key performance indicators.]

        ~S_SUB~ Expert Insight
        ~INSIGHT~ [Provide valuable insight that adds strategic depth to the analysis.]

        --- END OF REQUIRED FORMAT ---

        Generate the structured analysis now in ${language}.`;
};

/**
 * Generates prompt for standard questions requiring moderate structure
 */
const generateStandardResponsePrompt = (basePrompt, question, language) => {
    return `${basePrompt}

        **Response Instructions for Standard Business Question:**
        This question requires a balanced approach with moderate structure and comprehensive content.

        **Required Output Structure & Format Instructions:**
        Generate a business answer with appropriate structure. Use the specified ~TAGS~ for organization.

        --- START OF REQUIRED FORMAT ---
        ~H~ Business Answer: ${question.length > 50 ? question.substring(0, 50) + '...' : question}

        ~S_SUB~ Overview
        ~P~ [Provide a clear, comprehensive answer to the question in 2-3 sentences.]

        ~S_SUB~ Key Considerations
        ~P~ [Explain the important factors and considerations related to this question.]

        ~S_SUB~ Recommendations
        ~REC_LIST_START~
        ~REC~ [First practical recommendation]
        ~REC~ [Second practical recommendation]
        ~REC~ [Third practical recommendation]
        ~REC_LIST_END~

        ~S_SUB~ Action Steps
        ~STEP_LIST_START~
        ~STEP~ [First actionable step]
        ~STEP~ [Second actionable step]
        ~STEP~ [Third actionable step]
        ~STEP_LIST_END~

        ~S_SUB~ Important Notes
        ~RISK_LIST_START~
        ~RISK~ [Key consideration or potential challenge]
        ~RISK~ [Additional important consideration]
        ~RISK_LIST_END~

        ~S_SUB~ Expert Insight
        ~INSIGHT~ [Provide valuable business insight that adds depth to the answer.]

        --- END OF REQUIRED FORMAT ---

        **Guidelines:**
        1. Provide practical, actionable advice
        2. Include specific examples when helpful
        3. Consider the business context provided
        4. Focus on value creation and problem-solving
        5. Use professional business language
        6. Address both opportunities and challenges

        Generate the business answer now in ${language}.`;
};
