{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";;;AAmBA,SAAgB,cAAc,CAC5B,YAAqB;IAIrB,MAAM,WAAW,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC5C,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;QAC1B,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC;YAC1B,MAAM,KAAK,CAAC,gCAAgC,YAAY,EAAE,CAAC,CAAC;QAC9D,OAAO,WAAiE,CAAC;KAC1E;SAAM;QACL,OAAO,CAAC,YAAwD,CAAC,CAAC;KACnE;AACH,CAAC;AAbD,wCAaC;AAED,SAAgB,OAAO,CACrB,EAAY,EACZ,MAAe;IAEf,IAAI,CAAC,MAAM;QAAE,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IACzD,MAAM,WAAW,GAAG,cAAc,CAAW,MAAM,CAAC,CAAC;IACrD,MAAM,CAAC,aAAa,EAAE,aAAa,CAAC,GAAG,WAAW,CAAC;IACnD,IAAI,aAAa,EAAE;QACjB,OAAO;YACL,aAAa,EAAE,EAAE,CAAC,aAAa,CAAC;YAChC,mBAAmB,EAAE,aAAa;SACnC,CAAC;KACH;SAAM;QACL,OAAO;YACL,aAAa,EAAE,EAAE;YACjB,mBAAmB,EAAE,aAAa;SACnC,CAAC;KACH;AACH,CAAC;AAlBD,0BAkBC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport type { FunctionPropertyNames, FMember } from './types';\nimport type * as fs from 'fs';\ntype FS = typeof fs;\n\nexport function splitTwoLevels<FSObject>(\n  functionName: FMember\n):\n  | [FunctionPropertyNames<FSObject> & string]\n  | [FunctionPropertyNames<FSObject> & string, string] {\n  const memberParts = functionName.split('.');\n  if (memberParts.length > 1) {\n    if (memberParts.length !== 2)\n      throw Error(`Invalid member function name ${functionName}`);\n    return memberParts as [FunctionPropertyNames<FSObject> & string, string];\n  } else {\n    return [functionName as FunctionPropertyNames<FSObject> & string];\n  }\n}\n\nexport function indexFs<FSObject extends FS | FS['promises']>(\n  fs: FSObject,\n  member: FMember\n): { objectToPatch: any; functionNameToPatch: string } {\n  if (!member) throw new Error(JSON.stringify({ member }));\n  const splitResult = splitTwoLevels<FSObject>(member);\n  const [functionName1, functionName2] = splitResult;\n  if (functionName2) {\n    return {\n      objectToPatch: fs[functionName1],\n      functionNameToPatch: functionName2,\n    };\n  } else {\n    return {\n      objectToPatch: fs,\n      functionNameToPatch: functionName1,\n    };\n  }\n}\n"]}