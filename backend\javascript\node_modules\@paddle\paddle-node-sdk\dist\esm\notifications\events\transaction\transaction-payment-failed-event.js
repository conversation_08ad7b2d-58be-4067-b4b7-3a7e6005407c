import { Event } from '../../../entities/events/event.js';
import { EventName } from '../../helpers/index.js';
import { TransactionNotification } from '../../entities/index.js';
export class TransactionPaymentFailedEvent extends Event {
    constructor(response) {
        super(response);
        this.eventType = EventName.TransactionPaymentFailed;
        this.data = new TransactionNotification(response.data);
    }
}
