{"version": 3, "file": "ProxyLoggerProvider.js", "sourceRoot": "", "sources": ["../../src/ProxyLoggerProvider.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,6DAA4D;AAC5D,+CAA4C;AAE5C,MAAa,mBAAmB;IAG9B,SAAS,CACP,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,CACL,MAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,mCAC9C,IAAI,yBAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,WAAW;;QACT,OAAO,MAAA,IAAI,CAAC,SAAS,mCAAI,yCAAoB,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,iBAAiB,CACf,IAAY,EACZ,OAA4B,EAC5B,OAAmC;;QAEnC,OAAO,MAAA,IAAI,CAAC,SAAS,0CAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;CACF;AAhCD,kDAgCC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LoggerProvider } from './types/LoggerProvider';\nimport { Logger } from './types/Logger';\nimport { LoggerOptions } from './types/LoggerOptions';\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\n\nexport class ProxyLoggerProvider implements LoggerProvider {\n  private _delegate?: LoggerProvider;\n\n  getLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger {\n    return (\n      this.getDelegateLogger(name, version, options) ??\n      new ProxyLogger(this, name, version, options)\n    );\n  }\n\n  getDelegate(): LoggerProvider {\n    return this._delegate ?? NOOP_LOGGER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate logger provider\n   */\n  setDelegate(delegate: LoggerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateLogger(\n    name: string,\n    version?: string | undefined,\n    options?: LoggerOptions | undefined\n  ): Logger | undefined {\n    return this._delegate?.getLogger(name, version, options);\n  }\n}\n"]}