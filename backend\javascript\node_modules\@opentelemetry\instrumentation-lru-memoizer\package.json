{"name": "@opentelemetry/instrumentation-lru-memoizer", "version": "0.44.1", "description": "OpenTelemetry instrumentation for `lru-memoizer` function memoization using lru-cache", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"test": "mocha --require '@opentelemetry/contrib-test-utils' 'test/**/*.test.ts'", "test-all-versions": "tav", "tdd": "npm run test -- --watch-extensions ts --watch", "clean": "rimraf build/*", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "lint:readme": "node ../../../scripts/lint-readme.js", "prewatch": "npm run precompile", "prepublishOnly": "npm run compile", "version:update": "node ../../../scripts/version-update.js", "compile": "tsc -p ."}, "keywords": ["lru-memoizer", "instrumentation", "nodejs", "opentelemetry", "tracing"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "devDependencies": {"@opentelemetry/api": "^1.3.0", "@opentelemetry/contrib-test-utils": "^0.45.1", "@types/lru-cache": "7.10.10", "@types/mocha": "8.2.3", "@types/node": "18.18.14", "expect": "29.2.0", "lru-memoizer": "2.1.4", "nyc": "15.1.0", "rimraf": "5.0.10", "test-all-versions": "6.1.0", "typescript": "4.4.4"}, "dependencies": {"@opentelemetry/instrumentation": "^0.57.1"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/instrumentation-lru-memoizer#readme", "gitHead": "1eb77007669bae87fe5664d68ba6533b95275d52"}