"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubscriptionCancellationDetails = void 0;
const subscription_cancellation_entities_js_1 = require("./subscription-cancellation-entities.js");
const subscription_cancellation_options_js_1 = require("./subscription-cancellation-options.js");
class SubscriptionCancellationDetails {
    constructor(config) {
        this.entities = config.entities ? new subscription_cancellation_entities_js_1.SubscriptionCancellationEntities(config.entities) : null;
        this.options = config.options ? new subscription_cancellation_options_js_1.SubscriptionCancellationOptions(config.options) : null;
    }
}
exports.SubscriptionCancellationDetails = SubscriptionCancellationDetails;
