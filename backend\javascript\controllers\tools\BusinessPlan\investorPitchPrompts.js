// src/controllers/Tools/Business/investorPitchPrompts.js

/**
 * Constructs an advanced, detailed prompt for generating an investor pitch,
 * now with structured tags for rich component-based rendering on the frontend.
 * @param {object} formData - The validated data from the frontend form.
 * @returns {string} The complete prompt to be sent to the Gemini AI.
 */
export const buildInvestorPitchPrompt = (formData) => {
    const {
        projectName,
        industry,
        projectDescription,
        problemStatement,
        solution,
        targetAudience,
        competition,
        pitchObjective,
        fundingAmount,
        growthPlan,
        toneOfVoice,
        language = 'English',
        businessPlanContent
    } = formData;

    // The content for the ~ASK~ tag is generated dynamically.
    const theAskContent = fundingAmount
        ? `Our ask is for ${fundingAmount} to be primarily allocated towards product development, aggressive marketing, and key team hires. This investment will accelerate our growth and capture a significant market share.`
        : `Our primary objective is to ${pitchObjective}. We are seeking strategic partners who align with our vision and can help us achieve this goal.`;

    // Build business plan context if available
    const businessPlanContext = businessPlanContent
        ? `\n        **Business Plan Document Analysis:**\n        ${businessPlanContent}\n        \n        **IMPORTANT:** Use the information from the uploaded business plan document to enhance and validate the pitch content. Extract specific data points, financial projections, market research, and strategic insights from the document to create a more comprehensive and accurate investor pitch.`
        : '';

    // The main prompt structure
    return `
        **Role and Goal:**
        You are an expert startup consultant, investment advisor, and world-class copywriter with 20+ years of experience in venture capital and startup ecosystems. Your task is to write a compelling, persuasive, and professional investor pitch that meets institutional investment standards. The output MUST strictly follow the specified tagged format for frontend rendering.

        **Language and Direction:**
        Generate the entire pitch in **${language}**. Ensure proper grammar, cultural context, and professional terminology appropriate for ${language}-speaking investors.

        **Tone of Voice:**
        The overall tone of the pitch MUST be: **${toneOfVoice}** while maintaining professional credibility and investment-grade quality.

        **Input Data:**
        - **Project Name:** ${projectName}
        - **Industry:** ${industry}
        - **Core Idea:** ${projectDescription}
        - **Problem to Solve:** ${problemStatement}
        - **Our Solution:** ${solution}
        - **Target Audience:** ${targetAudience}
        - **Competition & Our Advantage:** ${competition}
        - **Primary Objective of this Pitch:** ${pitchObjective}
        - **Funding Request:** ${fundingAmount || 'Not specified'}
        - **Growth Plan:** ${growthPlan}${businessPlanContext}

        **Required Output Structure & Format Instructions:**
        Generate a comprehensive, investment-grade pitch following this EXACT structure. You MUST use the specified ~TAGS~ for each part. Each tagged element must be on a new line. Do NOT add any extra markdown, titles, or explanations. The entire output must be only the tagged text. Include specific data points, metrics, and professional insights throughout.

        --- START OF REQUIRED FORMAT ---
        ~H~ Investor Pitch: ${projectName}

        ~S_SUB~ Executive Summary & Hook
        ~P~ [Start with a powerful, data-driven opening that immediately captures attention. Present ${projectName} as a compelling investment opportunity with clear value proposition. Include market size or growth statistics if available.]

        ~S_SUB~ Market Problem & Opportunity
        ~P~ [Provide a detailed analysis of the problem (${problemStatement}). Include market size, affected demographics, current pain points, and quantify the opportunity. Use specific data points and industry insights.]

        ~S_SUB~ Our Innovative Solution
        ~P~ [Present ${solution} as a breakthrough innovation. Explain the technology, methodology, or approach. Highlight unique features, competitive advantages, and how it addresses the market gap effectively.]

        ~S_SUB~ Target Market & Customer Analysis
        ~P~ [Provide comprehensive analysis of ${targetAudience}. Include market segmentation, customer personas, addressable market size (TAM/SAM/SOM), customer acquisition strategy, and revenue potential per customer.]

        ~S_SUB~ Competitive Landscape & Differentiation
        ~P~ [Conduct thorough competitive analysis based on ${competition}. Identify direct and indirect competitors, market positioning, unique value propositions, barriers to entry, and sustainable competitive advantages.]

        ~S_SUB~ Business Model & Revenue Streams
        ~P~ [Detail the revenue model, pricing strategy, sales channels, customer lifetime value, and path to profitability. Include multiple revenue streams if applicable and scalability factors.]

        ~S_SUB~ Financial Projections & Key Metrics
        ~P~ [Present realistic financial forecasts for 3-5 years. Include revenue projections, key performance indicators, unit economics, burn rate, runway, and path to break-even. Use industry benchmarks where possible.]

        ~S_SUB~ Growth Strategy & Market Expansion
        ~P~ [Elaborate on ${growthPlan} with specific milestones, market expansion strategy, product roadmap, partnership opportunities, and scaling plans. Include timeline and resource requirements.]

        ~S_SUB~ Team & Execution Capability
        ~P~ [Highlight the founding team's expertise, relevant experience, advisory board, key hires planned, and why this team can execute the vision successfully. Address any skill gaps and hiring plans.]

        ~S_SUB~ Risk Assessment & Mitigation
        ~P~ [Identify key risks (market, technical, competitive, regulatory) and present clear mitigation strategies. Show awareness of challenges and preparedness to address them.]

        ~S_SUB~ Investment Ask & Use of Funds
        ~ASK~ ${theAskContent}

        ~S_SUB~ Expected Returns & Exit Strategy
        ~P~ [Present potential return scenarios, exit opportunities (IPO, acquisition), comparable transactions, and timeline to liquidity events. Include investor value proposition.]

        ~S_SUB~ Call to Action
        ~P~ [End with a compelling call to action that invites investors to participate in this opportunity. Reiterate the key value propositions and next steps for interested investors.]
        --- END OF REQUIRED FORMAT ---

        **Enhanced Requirements:**
        1. Include specific metrics, data points, and industry benchmarks throughout
        2. Use professional investment terminology appropriate for institutional investors
        3. Ensure all financial projections are realistic and well-justified
        4. Address potential investor concerns proactively
        5. Maintain consistency with uploaded business plan content if provided
        6. Generate content in ${language} with proper cultural and linguistic context
        7. Create investment-grade content suitable for actual investor presentations

        **Final Command:**
        Generate the complete, comprehensive investor pitch based on all instructions above. The output MUST be only the tagged pitch itself, formatted as a single block of text with each tagged element on a new line. Ensure the content is detailed, professional, and investment-ready.
    `;
};