var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
const byteHexMapping = new Array(256);
for (let i = 0; i < byteHexMapping.length; i++) {
    byteHexMapping[i] = i.toString(16).padStart(2, '0');
}
export class EdgeCrypto {
    randomUUID() {
        return crypto.randomUUID();
    }
    computeHmac(payload, secret) {
        return __awaiter(this, void 0, void 0, function* () {
            const encoder = new TextEncoder();
            const key = yield crypto.subtle.importKey('raw', encoder.encode(secret), {
                name: 'HMAC',
                hash: { name: 'SHA-256' },
            }, false, ['sign']);
            const signatureBuffer = yield crypto.subtle.sign('hmac', key, encoder.encode(payload));
            const signatureBytes = new Uint8Array(signatureBuffer);
            const signatureHexCodes = new Array(signatureBytes.length);
            for (let i = 0; i < signatureBytes.length; i++) {
                if (signatureBytes[i] !== undefined && signatureBytes[i] !== null) {
                    signatureHexCodes[i] = byteHexMapping[signatureBytes[i]];
                }
            }
            return signatureHexCodes.join('');
        });
    }
}
