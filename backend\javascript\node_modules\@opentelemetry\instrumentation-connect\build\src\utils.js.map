{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,4CAA0C;AAC1C,qDAA0E;AAEnE,MAAM,gBAAgB,GAAG,CAAC,OAAuB,EAAE,EAAE;IAC1D,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,uCAAsB,CAAC,CAAC,KAAK,KAAK,EAAE;QAC5D,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,uCAAsB,EAAE;YACrD,UAAU,EAAE,KAAK;YACjB,KAAK,EAAE,EAAE;SACV,CAAC,CAAC;KACJ;IACD,OAAO,CAAC,uCAAsB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAE1C,MAAM,WAAW,GAAG,OAAO,CAAC,uCAAsB,CAAC,CAAC,MAAM,CAAC;IAE3D,OAAO,GAAG,EAAE;QACV,IAAI,WAAW,KAAK,OAAO,CAAC,uCAAsB,CAAC,CAAC,MAAM,EAAE;YAC1D,OAAO,CAAC,uCAAsB,CAAC,CAAC,GAAG,EAAE,CAAC;SACvC;aAAM;YACL,UAAI,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;SAC7D;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AAlBW,QAAA,gBAAgB,oBAkB3B;AAEK,MAAM,wBAAwB,GAAG,CACtC,OAAuB,EACvB,QAAiB,EACjB,EAAE;IACF,IAAI,QAAQ,EAAE;QACZ,OAAO,CAAC,uCAAsB,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;KACzD;AACH,CAAC,CAAC;AAPW,QAAA,wBAAwB,4BAOnC;AAEF,wDAAwD;AACxD,6CAA6C;AAC7C,4DAA4D;AACrD,MAAM,aAAa,GAAG,CAAC,OAAuB,EAAE,EAAE;IACvD,OAAO,OAAO,CAAC,uCAAsB,CAAC,CAAC,MAAM,CAC3C,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,CAC5C,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,aAAa,iBAIxB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { diag } from '@opentelemetry/api';\nimport { _LAYERS_STORE_PROPERTY, PatchedRequest } from './internal-types';\n\nexport const addNewStackLayer = (request: PatchedRequest) => {\n  if (Array.isArray(request[_LAYERS_STORE_PROPERTY]) === false) {\n    Object.defineProperty(request, _LAYERS_STORE_PROPERTY, {\n      enumerable: false,\n      value: [],\n    });\n  }\n  request[_LAYERS_STORE_PROPERTY].push('/');\n\n  const stackLength = request[_LAYERS_STORE_PROPERTY].length;\n\n  return () => {\n    if (stackLength === request[_LAYERS_STORE_PROPERTY].length) {\n      request[_LAYERS_STORE_PROPERTY].pop();\n    } else {\n      diag.warn('Connect: Trying to pop the stack multiple time');\n    }\n  };\n};\n\nexport const replaceCurrentStackRoute = (\n  request: PatchedRequest,\n  newRoute?: string\n) => {\n  if (newRoute) {\n    request[_LAYERS_STORE_PROPERTY].splice(-1, 1, newRoute);\n  }\n};\n\n// generate route from existing stack on request object.\n// splash between stack layer will be deduped\n// [\"/first/\", \"/second\", \"/third/\"] => /first/second/third/\nexport const generateRoute = (request: PatchedRequest) => {\n  return request[_LAYERS_STORE_PROPERTY].reduce(\n    (acc, sub) => acc.replace(/\\/+$/, '') + sub\n  );\n};\n"]}