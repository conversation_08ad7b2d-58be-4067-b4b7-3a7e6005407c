{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type * as pgTypes from 'pg';\nimport type * as api from '@opentelemetry/api';\nimport { InstrumentationConfig } from '@opentelemetry/instrumentation';\n\nexport interface PgResponseHookInformation {\n  data: pgTypes.QueryResult | pgTypes.QueryArrayResult;\n}\n\nexport interface PgInstrumentationExecutionResponseHook {\n  (span: api.Span, responseInfo: PgResponseHookInformation): void;\n}\n\nexport interface PgRequestHookInformation {\n  query: {\n    text: string;\n    name?: string;\n    values?: unknown[];\n  };\n  connection: {\n    database?: string;\n    host?: string;\n    port?: number;\n    user?: string;\n  };\n}\n\nexport interface PgInstrumentationExecutionRequestHook {\n  (span: api.Span, queryInfo: PgRequestHookInformation): void;\n}\n\nexport interface PgInstrumentationConfig extends InstrumentationConfig {\n  /**\n   * If true, an attribute containing the query's parameters will be attached\n   * the spans generated to represent the query.\n   */\n  enhancedDatabaseReporting?: boolean;\n\n  /**\n   * Hook that allows adding custom span attributes or updating the\n   * span's name based on the data about the query to execute.\n   *\n   * @default undefined\n   */\n  requestHook?: PgInstrumentationExecutionRequestHook;\n\n  /**\n   * Hook that allows adding custom span attributes based on the data\n   * returned from \"query\" Pg actions.\n   *\n   * @default undefined\n   */\n  responseHook?: PgInstrumentationExecutionResponseHook;\n\n  /**\n   * If true, requires a parent span to create new spans.\n   *\n   * @default false\n   */\n  requireParentSpan?: boolean;\n\n  /**\n   * If true, queries are modified to also include a comment with\n   * the tracing context, following the {@link https://github.com/open-telemetry/opentelemetry-sqlcommenter sqlcommenter} format\n   */\n  addSqlCommenterCommentToQueries?: boolean;\n}\n"]}