// File Path: node_gemini_summarizer/backend/javascrupt/config/geminiConfig.js
import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } from '@google/generative-ai';
import { GEMINI_API_KEY, NODE_ENV } from './appConfig.js';

let geminiModelInstance = null; // Initialize as null

const DEFAULT_MODEL_NAME = "gemini-1.5-flash-latest"; // Default model

if (!GEMINI_API_KEY) {
    if (NODE_ENV !== 'test') { // Avoid error logs during tests if <PERSON> isn't actively used
      console.error("[GeminiConfig] FATAL: GEMINI_API_KEY is not available from appConfig. Gemini model cannot be initialized.");
    }
} else {
    try {
        const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
        const modelNameToUse = process.env.GEMINI_MODEL_PRIMARY || DEFAULT_MODEL_NAME; // Allow override via .env

        geminiModelInstance = genAI.getGenerativeModel({
            model: modelNameToUse,
            safetySettings: [
                { category: HarmCategory.HARM_CATEGORY_HARASSMENT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
                { category: HarmCategory.HARM_CATEGORY_HATE_SPEECH, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
                { category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
                { category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
            ],
            generationConfig: {
                temperature: 0.3, // Default temperature, can be overridden per request if needed
                // responseMimeType: "application/json", // Consider if your prompts always expect JSON output
            }
        });
        console.log(`[GeminiConfig] Primary Gemini model ("${modelNameToUse}") initialized successfully.`);
    } catch (error) {
        console.error(`[GeminiConfig] Error initializing Primary Gemini model: ${error.message}`, error);
        geminiModelInstance = null; // Ensure it's null on failure
    }
}

/**
 * The primary, shared Gemini model instance.
 * Import this directly in your controllers or other services.
 * Example: import geminiModel from './path/to/geminiConfig.js';
 */
export default geminiModelInstance;

/**
 * Optional: If you need to create other model instances with different configurations
 * or get the GenAI instance itself.
 */
let genAIInstance = null;
if (GEMINI_API_KEY) {
    try {
        genAIInstance = new GoogleGenerativeAI(GEMINI_API_KEY);
    } catch (e) {
        console.error("[GeminiConfig] Could not create GoogleGenerativeAI base instance.", e);
    }
}

export { genAIInstance }; // Export the base GenAI instance if needed elsewhere