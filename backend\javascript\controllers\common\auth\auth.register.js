// node_gemini_summarizer/controllers/auth/auth.register.js
import User from '../../../models/User.js'; // Adjust path as per your structure
import VerificationCode from '../../../models/VerificationCode.js'; // Adjust path
import AdminSettings from '../../../models/AdminSettings.js'; // Import AdminSettings model
import { sendVerificationEmail } from '../../../config/mailer.js'; // Adjust path
import { generateOTP } from './auth.helpers.js';
import { OTP_EXPIRY_MINUTES } from './auth.config.js';

export const registerUser = async (req, res) => {
  const { name, email, password, isAdmin } = req.body;

  if (!email || !password) {
    return res.status(400).json({ error: 'Please provide email and password.' });
  }
  if (password.length < 6) {
    return res.status(400).json({ error: 'Password must be at least 6 characters long.' });
  }

  // Check admin registration permissions
  if (isAdmin) {
    const canRegisterAdmin = await AdminSettings.canRegisterAdmin();
    if (!canRegisterAdmin) {
      return res.status(403).json({
        error: 'Admin registration is no longer allowed. The system already has an administrator.'
      });
    }
  }

  try {
    let user = await User.findOne({ email });
    if (user) {
      if (!user.isVerified) {
        if (password) user.password = password; // Re-hashed by pre-save hook
        if (name) user.name = name;
        if (isAdmin !== undefined) user.isAdmin = isAdmin;
        await user.save();
      } else {
        return res.status(409).json({ error: 'User already exists and is verified.' });
      }
    } else {
      user = new User({ name, email, password, isAdmin: isAdmin || false });
      await user.save();
    }

    // If this is an admin registration, mark it in AdminSettings
    if (isAdmin && user.isAdmin) {
      await AdminSettings.markInitialAdminRegistered(email);
    }

    const otp = generateOTP();
    const expiresAt = new Date(Date.now() + OTP_EXPIRY_MINUTES * 60 * 1000);

    await VerificationCode.deleteMany({ email: user.email });
    const verificationEntry = new VerificationCode({ email: user.email, code: otp, expiresAt });
    await verificationEntry.save();

    try {
        await sendVerificationEmail(user.email, otp);
        res.status(201).json({
            message: 'Registration successful. Please check your email to verify your account.',
            email: user.email
        });
    } catch (emailError) {
        console.error(`Failed to send verification email to ${user.email} during registration: ${emailError.message}`);
        // User created/updated, but email failed. User can use "resend".
        res.status(201).json({ // Still 201 because user registration part was okay.
            message: `Registration successful, but failed to send verification email. Please use the "Resend Verification" option.`,
            email: user.email,
            emailError: true // Flag for frontend
        });
    }

  } catch (error) {
    console.error('Register error:', error.message, error.stack);
    if (error.code === 11000) {
        return res.status(409).json({ error: 'Email already in use.' });
    }
    res.status(500).json({ error: 'Server error during registration.' });
  }
};