/**
 * Builds a simplified "Operations & Management Plan" for beginners.
 * This prompt generates a clear, step-by-step action plan that is easy to understand and follow.
 * It avoids complex jargon and focuses on practical tasks.
 * @param {object} params - The parameters object.
 * @param {string} params.period - The user's selected business plan period (e.g., "3 Months").
 * @returns {string} The generated prompt part.
 */
export const buildOperationalPlanPromptPart = ({ period }) => {
    const months = parseInt(period, 10) || 3;
    const weeks = months * 4;

    // Calculate dynamic phase distribution based on period length
    const foundationWeeks = Math.ceil(weeks * 0.25);
    const buildWeeks = Math.ceil(weeks * 0.5);
    const launchWeeks = weeks - foundationWeeks - buildWeeks;

    // Simplified roadmap with clear, actionable steps
    const milestonePrompt = `~T_ML_H~ Step,Main Goal for this Step,What You Will Create,How You'll Know You Succeeded,Timeline
~T_ML_R~ Step 1: Foundation (Weeks 1-${foundationWeeks}),[Generate a simple goal about understanding the customer and planning. Example: 'Confirm people actually want my product and create a solid plan.'],['Survey results from 20 potential users', 'One-page project plan', 'Basic brand logo and colors'],['At least 15 people say they would use it', 'Plan is complete and makes sense', 'Logo is ready'],Weeks 1-${foundationWeeks}
~T_ML_R~ Step 2: Build & Test (Weeks ${foundationWeeks + 1}-${foundationWeeks + buildWeeks}),[Generate a straightforward goal about building the first version. Example: 'Build the simplest version of the product and get feedback from a few friendly users.'],['A working prototype or MVP', 'List of user feedback', 'Plan for version 2'],['The prototype works without major crashes', 'At least 5 people have tested it', 'You know what to build next'],Weeks ${foundationWeeks + 1}-${foundationWeeks + buildWeeks}
~T_ML_R~ Step 3: Launch & Learn (Weeks ${foundationWeeks + buildWeeks + 1}-${weeks}),[Generate a simple goal about launching and getting the first users. Example: 'Launch the product to the public and get the first real users.'],['Live website or app store page', 'Announcement on social media', 'A way to track user numbers'],['First 100 people have signed up', 'Positive comments on social media', 'You have a clear idea how to grow'],Weeks ${foundationWeeks + buildWeeks + 1}-${weeks}`;

    // Simplified team structure focusing on key roles
    // Corrected teamStructure constant for buildOperationalPlanPromptPart

const teamStructure = months <= 3 ? `
~T_TM_H~ Who You Need,What They Do,When You Need Them,How to Pay Them (Example),What to Look For
~T_TM_R~ You (The Founder),"['Sets the vision', 'makes decisions', 'talks to users']",Day 1,Sweat Equity,"['Passion for the problem', 'good learner']"
~T_TM_R~ A Tech Person (Co-founder or Freelancer),"['Builds the actual product (app/website)']",Day 1,Equity or Project Fee,"['Good coding skills', 'reliable']"
~T_TM_R~ A Marketing Helper (Part-time or Freelancer),"['Spreads the word on social media', 'writes content']",Month 2,Hourly Rate,"['Good with social media', 'creative']"` : `
~T_TM_H~ Who You Need,What They Do,When You Need Them,How to Pay Them (Example),What to Look For
~T_TM_R~ You (The Founder),"['Sets the vision', 'makes decisions', 'talks to users']",Day 1,Sweat Equity,"['Passion for the problem', 'good learner']"
~T_TM_R~ A Tech Person (Co-founder or Freelancer),"['Builds the actual product (app/website)']",Day 1,Equity or Project Fee,"['Good coding skills', 'reliable']"
~T_TM_R~ A Marketing Helper (Part-time or Freelancer),"['Spreads the word on social media', 'writes content']",Month 2,Hourly Rate,"['Good with social media', 'creative']"
~T_TM_R~ A Support Person (Part-time),"['Answers user questions', 'collects feedback']",Month 3-4,Hourly Rate,"['Patient', 'good communicator']"`;

    const languageInstruction = `
~LANG_INSTRUCTION~ IMPORTANT: When generating table content, separate each cell with a comma (,) and ensure proper cell boundaries. Each table row should have exactly the same number of cells as the header row. Do not concatenate multiple values into a single cell.
`;

    const tableFormatInstruction = `
~TABLE_FORMAT~ Each table row must follow this exact format:
~T_[TAG]_R~ Cell1,Cell2,Cell3,Cell4,Cell5
Make sure each cell is separated by a single comma, and array-like content should be formatted as: ['item1', 'item2', 'item3']
`;

    const rtlInstruction = `
~RTL_FORMAT~ For Arabic content: Ensure each table cell is properly separated. When listing multiple items in a cell, use the format: ['العنصر الأول', 'العنصر الثاني', 'العنصر الثالث']
`;

    return `
${languageInstruction}
${tableFormatInstruction}
${rtlInstruction}
### PART 5: YOUR ACTION PLAN ###

~H~ How to Get Things Done
~META~ This is your step-by-step guide to turn your idea into reality. It's designed to be simple and flexible, so you can focus on what matters most.

~S~ 1. Your Daily & Weekly Routine
~P~ A simple routine helps you make progress every day without getting overwhelmed. Here’s a sample schedule to guide you.

~H_SUB~ A Simple Daily Schedule
~P~ Each morning, try to do your most important task first when your mind is fresh.

~T_DC_H~ Time,What to Focus On,Example Tasks,Goal for this Block
~T_DC_R~ Morning (2-3 hours),Your Most Important Task,[Write code, design the app, contact key partners],Make real progress on your main goal
~T_DC_R~ Late Morning (1-2 hours),Talk to People,[Email potential customers, post on social media, call a mentor],Get feedback and build relationships
~T_DC_R~ Afternoon (2-3 hours),Growth & Small Tasks,[Write a blog post, research competitors, answer emails],Spread the word and clear your to-do list
~T_DC_R~ End of Day (15 mins),Plan for Tomorrow,[Write down your top 1-3 tasks for the next day],End the day feeling prepared

~H_SUB~ Simple Weekly Check-ins
~P~ Taking a moment each week to check your progress is key. It helps you stay on track and fix problems early.

~WEEKLY_PROCESS~ **End of Week (Friday Afternoon):** Ask yourself: Did I get my most important tasks done? What went well? What got me stuck? What should I focus on next week?
~WEEKLY_PROCESS~ **Start of Week (Monday Morning):** Look at your plan for the week. Confirm your #1 priority. Make sure you have everything you need to get started.

~H_SUB~ Staying Healthy and Avoiding Burnout
~P~ You can't do your best work if you're exhausted. Here are a few simple rules to protect your energy.

~SUSTAINABILITY~ **Get Enough Rest:** Aim for 7-8 hours of sleep. Your brain needs it to solve problems.
~SUSTAINABILITY~ **Know When to Stop:** Set a time to end your workday. It's important to have time for your life outside of work.
~SUSTAINABILITY~ **Keep Learning:** Spend a little time each week reading or watching videos about your industry.

~S~ 2. Building Your Team
~P~ You don't have to do everything alone. Here’s a simple guide on who you might need and when.

${teamStructure}

~H_SUB~ Creating a Good Work Environment
~P~ If you bring people on, you want them to be happy and productive. Here are the basics.

~CULTURE~ **Setting Clear Goals:** Make sure everyone knows what they are supposed to do and what success looks like.
~CULTURE~ **Talking as a Team:** Have a quick chat each day or every few days to make sure everyone is on the same page.
~CULTURE~ **Writing Things Down:** Keep notes on important decisions or processes in one place so everyone can find them.

~S~ 3. Tools to Help You Build
~P~ The right tools can make your life much easier. Here are some common categories and examples of what they do.

~STACK~ **Frontend (What users see):** The visual part of your app or website. Tools like React or Vue.js are popular for this.
~STACK~ **Backend (The 'engine'):** The part of your app that users don't see, which handles data and logic. Tools like Node.js or Python are often used.
~STACK~ **Database (Where data is stored):** Where user information and other data is saved. PostgreSQL and MongoDB are common choices.
~STACK~ **Cloud Platform (Where it 'lives'):** A service like AWS, Google Cloud, or Vercel that hosts your app online.
~STACK~ **Productivity Tools:** Apps to help you stay organized (like Trello or Notion) and communicate (like Slack or Discord).

~H_SUB~ How to Choose New Tools
~P~ When you need a new tool, ask these simple questions to make a good choice.

~TECH_DECISION~ **Questions to Ask:** Does this solve a real problem I have right now? Is it easy to learn and use? Is it free or affordable?
~TECH_DECISION~ **How to Start Using It:** Try it out on a small test project first. If it works well, start using it for your main project. Tell your team how it works.

~S~ 4. Your Step-by-Step Roadmap
~P~ This is your big-picture plan. It breaks down your ${period} journey into simple, manageable steps.

${milestonePrompt}

~H_SUB~ What Could Go Wrong? (And How to Prepare)
~P~ Thinking about problems ahead of time makes them less scary. Here are a few common ones.

~RISK_CATEGORY~ **Tech Problems:** Your app has a major bug, or your website goes down. (Plan: Test your code, have backups).
~RISK_CATEGORY~ **Market Problems:** A new competitor appears, or you find it's hard to get users. (Plan: Focus on what makes you unique, talk to users constantly).
~RISK_CATEGORY~ **Money Problems:** You run out of money faster than expected. (Plan: Keep a close eye on your spending, have a 'plan B' for funding).

~H_SUB~ How to Measure Success
~P~ You need simple numbers to know if you're moving in the right direction. Here are a few examples.

~T_KPI_H~ Area,What to Track,Good Target,How Often to Check,When to Worry
~T_KPI_R~ Product,Number of bugs reported,Fewer than 5 per week,Weekly,If a major bug isn't fixed in 2 days
~T_KPI_R~ Users,Number of new sign-ups,10+ new users per week,Weekly,If you get no new users for 2 weeks
~T_KPI_R~ Money,Money spent vs. budget,Stay within 10% of your budget,Monthly,If your runway is less than 3 months

~META_CONCLUSION~ This action plan is your starting guide. The most important thing is to get started and learn as you go. Don't be afraid to change the plan as you learn more about your customers and your business. Good luck!
    `;
};