// --- FIX: The import path now matches your structure AND includes the required .js extension ---
import SavedBusinessPlan from '../../../models/save/Business/businessPlansave.js';

/**
 * @desc    Saves a new plan or updates an existing one for the logged-in user.
 * @route   POST /api/saved-plans
 * @access  Private
 */
export const saveOrUpdatePlan = async (req, res) => {
    const { planId, formData, reportData } = req.body;
    const userId = req.user._id; // From 'protect' middleware

    if (!planId || !formData || !reportData) {
        return res.status(400).json({ error: 'Missing required plan data for saving.' });
    }

    try {
        const planDataPayload = {
            user: userId,
            planId: planId,
            formData: formData,
            reportData: reportData,
            businessName: formData.businessName || 'Untitled Business Plan',
        };

        // Securely find and update/create (upsert) the plan for the specific user.
        const options = { new: true, upsert: true, runValidators: true };
        const savedPlan = await SavedBusinessPlan.findOneAndUpdate(
            { planId: planId, user: userId },
            planDataPayload,
            options
        );
        
        console.log(`[SAVED PLAN CTRL] Plan ${savedPlan.planId} saved/updated successfully for user ${userId}.`);
        res.status(201).json({
            message: 'Plan was saved successfully!',
            plan: savedPlan,
        });

    } catch (error) {
        console.error(`[SAVED PLAN CTRL] Error saving plan:`, error);
        res.status(500).json({ error: 'An internal server error occurred while saving the plan.' });
    }
};

/**
 * @desc    Get all saved business plans for the current user.
 * @route   GET /api/saved-plans
 * @access  Private
 */
export const getAllSavedPlans = async (req, res) => {
    try {
        const plans = await SavedBusinessPlan.find({ user: req.user._id }).sort({ updatedAt: -1 });
        res.status(200).json(plans);
    } catch (error) {
        console.error(`[SAVED PLAN CTRL] Error fetching all plans:`, error);
        res.status(500).json({ error: 'Failed to retrieve your saved plans.' });
    }
};

/**
 * @desc    Get a single saved business plan by its planId.
 * @route   GET /api/saved-plans/:planId
 * @access  Private
 */
export const getSavedPlanById = async (req, res) => {
    try {
        const plan = await SavedBusinessPlan.findOne({
            planId: req.params.planId,
            user: req.user._id // Critical security check
        });

        if (!plan) {
            return res.status(404).json({ error: 'Plan not found or you do not have permission to view it.' });
        }

        res.status(200).json(plan);
    } catch (error) {
        console.error(`[SAVED PLAN CTRL] Error fetching single plan:`, error);
        res.status(500).json({ error: 'Failed to retrieve the specified plan.' });
    }
};

/**
 * @desc    Delete a saved business plan by its planId.
 * @route   DELETE /api/saved-plans/:planId
 * @access  Private
 */
export const deleteSavedPlan = async (req, res) => {
    try {
        const plan = await SavedBusinessPlan.findOne({
            planId: req.params.planId,
            user: req.user._id // Critical security check
        });

        if (!plan) {
            return res.status(404).json({ error: 'Plan not found.' });
        }

        await plan.remove();
        res.status(200).json({ message: 'Plan has been deleted successfully.' });

    } catch (error) {
        console.error(`[SAVED PLAN CTRL] Error deleting plan:`, error);
        res.status(500).json({ error: 'Failed to delete the plan.' });
    }
};