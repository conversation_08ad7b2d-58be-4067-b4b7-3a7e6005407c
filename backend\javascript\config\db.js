// config/db.js
import mongoose from 'mongoose';
import dotenv from 'dotenv';

// Ensure .env is loaded (ideally once at app's entry point like server.js)
// If server.js already calls dotenv.config(), this one is redundant.
// However, it's kept here for potential standalone use/testing of this module.
if (!process.env.MONGO_URI) { // Check if it's already loaded by a prior dotenv.config() call
    dotenv.config({ path: './.env' }); // Adjust path if .env is not in the root
}


const MONGO_URI = process.env.MONGO_URI;

if (!MONGO_URI) {
    console.error("FATAL ERROR: MONGO_URI is not defined. Cannot connect to MongoDB. Ensure it's in your .env file.");
    process.exit(1);
}

const connectDB = async () => {
  try {
    const options = {
  
    };

    await mongoose.connect(MONGO_URI, options);
    // console.log('MongoDB Connected successfully.'); // Initial connection message

  } catch (err) {
    console.error('MongoDB Connection Error:', err.message);
    process.exit(1);
  }
};

// Mongoose Connection Events
// These should be set up after the initial connect attempt, typically in server.js
// or here if connectDB is called immediately when this module is imported.
// However, to keep connectDB as a pure connection function, it's often better
// to handle these event listeners in the main server file after calling connectDB.
// For simplicity, adding them here but be mindful of when connectDB is invoked.

mongoose.connection.on('connected', () => {
  // Get the database name from the URI for logging, if possible
  let dbName = 'database';
  try {
    const urlParts = MONGO_URI.split('/');
    const lastPart = urlParts[urlParts.length - 1];
    dbName = lastPart.includes('?') ? lastPart.substring(0, lastPart.indexOf('?')) : lastPart;
  } catch (e) { /* ignore parsing error */ }
  console.log(`MongoDB connected to database: ${dbName}`);
});

mongoose.connection.on('error', (err) => {
  console.error('MongoDB connection error event:', err.message);
});

mongoose.connection.on('disconnected', () => {
  console.log('MongoDB disconnected.');
});

// Graceful Mongoose disconnect on app termination
const gracefulExit = async () => {
  await mongoose.connection.close();
  console.log('Mongoose connection closed due to app termination.');
  process.exit(0);
};

process.on('SIGINT', gracefulExit).on('SIGTERM', gracefulExit);


export default connectDB;