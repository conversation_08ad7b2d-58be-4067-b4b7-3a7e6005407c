import { Event } from '../../../entities/events/event.js';
import { EventName } from '../../helpers/index.js';
import { TransactionNotification } from '../../entities/index.js';
export class TransactionReadyEvent extends Event {
    constructor(response) {
        super(response);
        this.eventType = EventName.TransactionReady;
        this.data = new TransactionNotification(response.data);
    }
}
