/* RTL Support for Business Q&A Display */

/* RTL Typography and Layout */
.prose-rtl {
  direction: rtl;
  text-align: right;
}

.prose-rtl h1,
.prose-rtl h2,
.prose-rtl h3,
.prose-rtl h4,
.prose-rtl h5,
.prose-rtl h6 {
  text-align: right;
}

.prose-rtl p {
  text-align: right;
}

.prose-rtl ul,
.prose-rtl ol {
  padding-right: 1.5rem;
  padding-left: 0;
}

.prose-rtl li {
  text-align: right;
}

/* RTL Flex and Grid Adjustments */
.prose-rtl .flex {
  flex-direction: row-reverse;
}

.prose-rtl .flex-row {
  flex-direction: row-reverse;
}

/* RTL Border and Spacing */
.prose-rtl .border-l-4 {
  border-left: none;
  border-right: 4px solid;
}

.prose-rtl .pl-3 {
  padding-left: 0;
  padding-right: 0.75rem;
}

.prose-rtl .pl-4 {
  padding-left: 0;
  padding-right: 1rem;
}

.prose-rtl .mr-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

.prose-rtl .mr-3 {
  margin-right: 0;
  margin-left: 0.75rem;
}

.prose-rtl .mr-4 {
  margin-right: 0;
  margin-left: 1rem;
}

.prose-rtl .ml-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}

.prose-rtl .ml-3 {
  margin-left: 0;
  margin-right: 0.75rem;
}

.prose-rtl .ml-4 {
  margin-left: 0;
  margin-right: 1rem;
}

/* RTL Table Styles */
.prose-rtl table {
  direction: rtl;
}

.prose-rtl th,
.prose-rtl td {
  text-align: right;
}

.prose-rtl th:first-child,
.prose-rtl td:first-child {
  text-align: right;
}

/* RTL Button and Icon Adjustments */
.prose-rtl .flex.items-center {
  flex-direction: row-reverse;
}

/* Specific RTL adjustments for icons in lists */
.prose-rtl .recommendation-item,
.prose-rtl .step-item,
.prose-rtl .risk-item {
  flex-direction: row-reverse;
}

.prose-rtl .recommendation-item .icon,
.prose-rtl .step-item .icon,
.prose-rtl .risk-item .icon {
  margin-right: 0;
  margin-left: 0.75rem;
}

/* RTL Form Styles */
.form-rtl {
  direction: rtl;
}

.form-rtl input,
.form-rtl textarea,
.form-rtl select {
  text-align: right;
}

.form-rtl label {
  text-align: right;
}

.form-rtl .flex {
  flex-direction: row-reverse;
}

.form-rtl .text-right {
  text-align: left;
}

.form-rtl .text-left {
  text-align: right;
}

/* RTL Tooltip Positioning */
.form-rtl .tooltip {
  right: auto;
  left: 0;
}

/* Language-specific font optimizations */
.lang-arabic {
  font-family: 'Noto Sans Arabic', 'Arial Unicode MS', sans-serif;
  line-height: 1.8;
}

.lang-hebrew {
  font-family: 'Noto Sans Hebrew', 'Arial Unicode MS', sans-serif;
  line-height: 1.7;
}

.lang-chinese {
  font-family: 'Noto Sans SC', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  line-height: 1.8;
}

.lang-japanese {
  font-family: 'Noto Sans JP', 'Hiragino Kaku Gothic Pro', 'Meiryo', sans-serif;
  line-height: 1.8;
}

.lang-korean {
  font-family: 'Noto Sans KR', 'Malgun Gothic', 'Apple Gothic', sans-serif;
  line-height: 1.8;
}

.lang-russian {
  font-family: 'Noto Sans', 'PT Sans', 'Arial', sans-serif;
  line-height: 1.6;
}

/* Responsive RTL adjustments */
@media (max-width: 768px) {
  .prose-rtl .flex-col-reverse {
    flex-direction: column-reverse;
  }
  
  .form-rtl .grid {
    direction: rtl;
  }
}
