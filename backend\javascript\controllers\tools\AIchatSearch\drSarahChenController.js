// controllers/AIchat/drSarahChenController.js
import { getGeminiModel } from '../../../services/geminiService.js';
import { drSarahChenSystemPrompt } from './system_instructions/drSarahChenSystemPrompt.js';


const formatHistory = (historyArray) => {
    if (!Array.isArray(historyArray)) {
        console.error('[Dr. <PERSON>] formatHistory received non-array input:', historyArray);
        return [];
    }
    return historyArray.map(turn => {
        if (typeof turn.role !== 'string' || typeof turn.text !== 'string') {
            console.error('[Dr. <PERSON>] formatH<PERSON>ory encountered a malformed turn:', turn);
            return { role: 'user', parts: [{ text: 'Error: Malformed history turn from client.' }] };
        }
        return {
            role: turn.role,
            parts: [{ text: turn.text }]
        };
    });
};


export const chatWithDrSarah = async (req, res) => {
    // Removed: isSearchQuery from req.body destructuring
    const { message, history } = req.body;

    if (!message || typeof message !== 'string' || message.trim() === '') {
        return res.status(400).json({ error: 'Message is required and must be a non-empty string.' });
    }

    const model = getGeminiModel();
    if (!model) {
        console.error('[Dr. Sarah Chen Controller] Gemini model is not available.');
        return res.status(500).json({ error: 'Gemini model is not available.' });
    }

    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.flushHeaders();

    const SSEWriteFunction = (dataObject) => {
        try {
            if (!res.writableEnded) {
                res.write(`data: ${JSON.stringify(dataObject)}\n\n`);
            }
        } catch (e) {
            // console.error("[Dr. Sarah Chen Controller] SSEWriteFunction Error writing to stream:", e.message);
        }
    };

    const conversationHistoryForGemini = history && Array.isArray(history) ? formatHistory(history) : [];
    let augmentedUserMessage = message;

    SSEWriteFunction({ type: 'ai_response_status', status: 'generating', detail: 'Composing response...' });

    try {
        const chatSession = model.startChat({
            history: [
                { role: "user", parts: [{ text: drSarahChenSystemPrompt }] },
                { role: "model", parts: [{ text: "Understood. I am Dr. Sarah Chen, your medical advisor. How can I help you today?" }] },
                ...conversationHistoryForGemini
            ],
            generationConfig: {
                 temperature: 0.7,
                 maxOutputTokens: 10000,
            },
        });

        const result = await chatSession.sendMessageStream(augmentedUserMessage);

        let accumulatedResponse = "";
        for await (const chunk of result.stream) {
            const chunkText = chunk.text();
            accumulatedResponse += chunkText;
            SSEWriteFunction({ text: chunkText, type: 'chunk' });
        }

        SSEWriteFunction({ fullResponse: accumulatedResponse, type: 'done' });
        if (!res.writableEnded) res.end();

    } catch (error) {
        console.error('[Dr. Sarah Chen Controller] Error during Gemini API interaction:', error);
        if (!res.writableEnded) {
            SSEWriteFunction({ error: `An error occurred with the AI model: ${error.message}`, type: 'error' });
            if (!res.writableEnded) res.end();
        }
    }
};