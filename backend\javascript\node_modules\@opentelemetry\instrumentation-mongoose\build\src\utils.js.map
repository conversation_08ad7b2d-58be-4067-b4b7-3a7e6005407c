{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,4CAA4E;AAG5E,oEAAwE;AACxE,8EAM6C;AAE7C,SAAgB,2BAA2B,CACzC,UAAsB;IAEtB,OAAO;QACL,CAAC,qDAA8B,CAAC,EAAE,UAAU,CAAC,IAAI;QACjD,CAAC,uCAAgB,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI;QACxC,CAAC,uCAAgB,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI;QACxC,CAAC,6CAAsB,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI;QAC9C,CAAC,6CAAsB,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI;KAC/C,CAAC;AACJ,CAAC;AAVD,kEAUC;AAED,SAAS,cAAc,CAAC,IAAU,EAAE,QAAa,EAAE;IACjD,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IAE5B,IAAI,CAAC,SAAS,CAAC;QACb,IAAI,EAAE,oBAAc,CAAC,KAAK;QAC1B,OAAO,EAAE,GAAG,KAAK,CAAC,OAAO,IACvB,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,0BAA0B,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EACxD,EAAE;KACH,CAAC,CAAC;AACL,CAAC;AAED,SAAS,iBAAiB,CACxB,IAAU,EACV,QAAa,EACb,YAAuD,EACvD,gBAAoC,SAAS;IAE7C,IAAI,CAAC,YAAY,EAAE;QACjB,OAAO;KACR;IAED,IAAA,wCAAsB,EACpB,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,aAAa,EAAE,QAAQ,EAAE,CAAC,EACrD,CAAC,CAAC,EAAE;QACF,IAAI,CAAC,EAAE;YACL,UAAI,CAAC,KAAK,CAAC,8CAA8C,EAAE,CAAC,CAAC,CAAC;SAC/D;IACH,CAAC,EACD,IAAI,CACL,CAAC;AACJ,CAAC;AAED,SAAgB,qBAAqB,CACnC,YAAiB,EACjB,IAAU,EACV,YAAuD,EACvD,gBAAoC,SAAS;IAE7C,IAAI,CAAC,CAAC,YAAY,YAAY,OAAO,CAAC,EAAE;QACtC,iBAAiB,CAAC,IAAI,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;QACnE,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,OAAO,YAAY,CAAC;KACrB;IAED,OAAO,YAAY;SAChB,IAAI,CAAC,QAAQ,CAAC,EAAE;QACf,iBAAiB,CAAC,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;QAC/D,OAAO,QAAQ,CAAC;IAClB,CAAC,CAAC;SACD,KAAK,CAAC,GAAG,CAAC,EAAE;QACX,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC1B,MAAM,GAAG,CAAC;IACZ,CAAC,CAAC;SACD,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AAC/B,CAAC;AAtBD,sDAsBC;AAED,SAAgB,sBAAsB,CACpC,QAAkB,EAClB,IAAc,EACd,YAAiB,EACjB,IAAU,EACV,IAAgB,EAChB,YAAuD,EACvD,gBAAoC,SAAS;IAE7C,IAAI,qBAAqB,GAAG,CAAC,CAAC;IAC9B,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACrB,qBAAqB,GAAG,CAAC,CAAC;KAC3B;IAED,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAU,EAAE,QAAa,EAAO,EAAE;QAC/D,GAAG;YACD,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC;YAC3B,CAAC,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;QAEnE,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,OAAO,QAAS,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;IAClC,CAAC,CAAC;IAEF,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AACxC,CAAC;AAxBD,wDAwBC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { Attributes, SpanStatusCode, diag, Span } from '@opentelemetry/api';\nimport type { Collection } from 'mongoose';\nimport { MongooseResponseCustomAttributesFunction } from './types';\nimport { safeExecuteInTheMiddle } from '@opentelemetry/instrumentation';\nimport {\n  SEMATTRS_DB_MONGODB_COLLECTION,\n  SEMATTRS_DB_NAME,\n  SEMATTRS_DB_USER,\n  SEMATTRS_NET_PEER_NAME,\n  SEMATTRS_NET_PEER_PORT,\n} from '@opentelemetry/semantic-conventions';\n\nexport function getAttributesFromCollection(\n  collection: Collection\n): Attributes {\n  return {\n    [SEMATTRS_DB_MONGODB_COLLECTION]: collection.name,\n    [SEMATTRS_DB_NAME]: collection.conn.name,\n    [SEMATTRS_DB_USER]: collection.conn.user,\n    [SEMATTRS_NET_PEER_NAME]: collection.conn.host,\n    [SEMATTRS_NET_PEER_PORT]: collection.conn.port,\n  };\n}\n\nfunction setErrorStatus(span: Span, error: any = {}) {\n  span.recordException(error);\n\n  span.setStatus({\n    code: SpanStatusCode.ERROR,\n    message: `${error.message} ${\n      error.code ? `\\nMongoose Error Code: ${error.code}` : ''\n    }`,\n  });\n}\n\nfunction applyResponseHook(\n  span: Span,\n  response: any,\n  responseHook?: MongooseResponseCustomAttributesFunction,\n  moduleVersion: string | undefined = undefined\n) {\n  if (!responseHook) {\n    return;\n  }\n\n  safeExecuteInTheMiddle(\n    () => responseHook(span, { moduleVersion, response }),\n    e => {\n      if (e) {\n        diag.error('mongoose instrumentation: responseHook error', e);\n      }\n    },\n    true\n  );\n}\n\nexport function handlePromiseResponse(\n  execResponse: any,\n  span: Span,\n  responseHook?: MongooseResponseCustomAttributesFunction,\n  moduleVersion: string | undefined = undefined\n): any {\n  if (!(execResponse instanceof Promise)) {\n    applyResponseHook(span, execResponse, responseHook, moduleVersion);\n    span.end();\n    return execResponse;\n  }\n\n  return execResponse\n    .then(response => {\n      applyResponseHook(span, response, responseHook, moduleVersion);\n      return response;\n    })\n    .catch(err => {\n      setErrorStatus(span, err);\n      throw err;\n    })\n    .finally(() => span.end());\n}\n\nexport function handleCallbackResponse(\n  callback: Function,\n  exec: Function,\n  originalThis: any,\n  span: Span,\n  args: IArguments,\n  responseHook?: MongooseResponseCustomAttributesFunction,\n  moduleVersion: string | undefined = undefined\n) {\n  let callbackArgumentIndex = 0;\n  if (args.length === 2) {\n    callbackArgumentIndex = 1;\n  }\n\n  args[callbackArgumentIndex] = (err: Error, response: any): any => {\n    err\n      ? setErrorStatus(span, err)\n      : applyResponseHook(span, response, responseHook, moduleVersion);\n\n    span.end();\n    return callback!(err, response);\n  };\n\n  return exec.apply(originalThis, args);\n}\n"]}