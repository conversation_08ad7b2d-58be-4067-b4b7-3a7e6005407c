# app/services/pdf/investor_pitch_service.py
from flask import current_app
import google.generativeai as genai
import logging
from google.api_core import exceptions as google_exceptions 

logger = logging.getLogger(__name__)
if not logger.handlers:
    logger.addHandler(logging.StreamHandler())
    logger.setLevel(logging.INFO)

class AIGatewayException(Exception):
    """Custom exception for when the AI service fails to connect to its upstream provider."""
    pass

def extract_business_info_from_document(document_text, language="English"):
    """
    Extract structured business information from uploaded business plan document
    """
    logger.info(f"Extracting business info from document in language: {language}")
    
    api_key = current_app.config.get('GEMINI_API_KEY')
    if not api_key:
        raise ValueError("AI Service Error: API key not configured.")
    
    try:
        genai.configure(api_key=api_key)
    except Exception as e:
        raise ValueError(f"AI Service Error: SDK configuration failed: {e}")

    model_name = current_app.config.get('GEMINI_SUMMARIZATION_MODEL', 'gemini-1.5-flash-latest')
    
    try:
        model = genai.GenerativeModel(model_name)
        
        extraction_prompt = f"""
You are an expert business analyst. Extract key business information from the following business plan document.

**CRITICAL INSTRUCTION: Respond in {language} language.**

Please extract and structure the following information in JSON format:

{{
    "projectName": "Company/Project name",
    "industry": "Industry sector",
    "projectDescription": "Brief description of the business idea",
    "problemStatement": "Problem being solved",
    "solution": "Proposed solution",
    "targetAudience": "Target customers/market",
    "competition": "Competitive landscape and advantages",
    "pitchObjective": "Main business objective",
    "fundingAmount": "Funding requirements if mentioned",
    "growthPlan": "Growth strategy and future plans",
    "marketAnalysis": "Market size and opportunity",
    "financialProjections": "Revenue projections and key metrics",
    "teamInfo": "Team background and expertise",
    "riskAssessment": "Key risks and mitigation strategies"
}}

If any information is not available in the document, use "Not specified in document" for that field.

Document content:
--- BEGIN DOCUMENT ---
{document_text}
--- END DOCUMENT ---

Return only the JSON object, no additional text.
"""
        
        response = model.generate_content(extraction_prompt)
        extracted_info = response.text
        
        if not extracted_info or not extracted_info.strip():
            raise ValueError("AI Service Error: Document analysis was empty.")
            
        logger.info(f"Business info extracted successfully in '{language}'.")
        return extracted_info
        
    except google_exceptions.DeadlineExceeded as e:
        logger.error(f"Gemini Gateway Error: The request to Google API timed out. {e}", exc_info=True)
        raise AIGatewayException("The AI analysis service timed out while processing the document.") from e
    except Exception as e:
        if 'failed to connect' in str(e).lower() or 'deadline' in str(e).lower():
            logger.error(f"Gemini Gateway Error: Detected network issue. {e}", exc_info=True)
            raise AIGatewayException("The AI analysis service could not connect to its provider.") from e
        
        logger.error(f"Unexpected error during document analysis: {e}", exc_info=True)
        raise ValueError(f"AI Service Error: An unexpected error occurred: {str(e)}")

def generate_investor_pitch(form_data, document_analysis=None, language="English"):
    """
    Generate comprehensive investor pitch content
    """
    logger.info(f"Generating investor pitch in language: {language}")
    
    api_key = current_app.config.get('GEMINI_API_KEY')
    if not api_key:
        raise ValueError("AI Service Error: API key not configured.")
    
    try:
        genai.configure(api_key=api_key)
    except Exception as e:
        raise ValueError(f"AI Service Error: SDK configuration failed: {e}")

    model_name = current_app.config.get('GEMINI_SUMMARIZATION_MODEL', 'gemini-1.5-flash-latest')
    
    try:
        model = genai.GenerativeModel(model_name)
        
        # Build context from document analysis if available
        document_context = ""
        if document_analysis:
            document_context = f"""
        
**Business Plan Document Analysis:**
{document_analysis}

**IMPORTANT:** Use the information from the uploaded business plan document to enhance and validate the pitch content. Extract specific data points, financial projections, market research, and strategic insights from the document to create a more comprehensive and accurate investor pitch.
"""
        
        # Build the comprehensive prompt
        pitch_prompt = f"""
**Role and Goal:**
You are an expert startup consultant, investment advisor, and world-class copywriter with 20+ years of experience in venture capital and startup ecosystems. Your task is to write a compelling, persuasive, and professional investor pitch that meets institutional investment standards. The output MUST strictly follow the specified tagged format for frontend rendering.

**Language and Direction:**
Generate the entire pitch in **{language}**. Ensure proper grammar, cultural context, and professional terminology appropriate for {language}-speaking investors.

**Tone of Voice:**
The overall tone of the pitch MUST be: **{form_data.get('toneOfVoice', 'Professional')}** while maintaining professional credibility and investment-grade quality.

**Input Data:**
- **Project Name:** {form_data.get('projectName', 'Not specified')}
- **Industry:** {form_data.get('industry', 'Not specified')}
- **Core Idea:** {form_data.get('projectDescription', 'Not specified')}
- **Problem to Solve:** {form_data.get('problemStatement', 'Not specified')}
- **Our Solution:** {form_data.get('solution', 'Not specified')}
- **Target Audience:** {form_data.get('targetAudience', 'Not specified')}
- **Competition & Our Advantage:** {form_data.get('competition', 'Not specified')}
- **Primary Objective of this Pitch:** {form_data.get('pitchObjective', 'Not specified')}
- **Funding Request:** {form_data.get('fundingAmount', 'Not specified')}
- **Growth Plan:** {form_data.get('growthPlan', 'Not specified')}{document_context}

**Required Output Structure & Format Instructions:**
Generate a comprehensive, investment-grade pitch following this EXACT structure. You MUST use the specified ~TAGS~ for each part. Each tagged element must be on a new line. Do NOT add any extra markdown, titles, or explanations. The entire output must be only the tagged text. Include specific data points, metrics, and professional insights throughout.

--- START OF REQUIRED FORMAT ---
~H~ Investor Pitch: {form_data.get('projectName', 'Business Opportunity')}

~S_SUB~ Executive Summary & Hook
~P~ [Start with a powerful, data-driven opening that immediately captures attention. Present the project as a compelling investment opportunity with clear value proposition. Include market size or growth statistics if available.]

~S_SUB~ Market Problem & Opportunity
~P~ [Provide a detailed analysis of the problem. Include market size, affected demographics, current pain points, and quantify the opportunity. Use specific data points and industry insights.]

~S_SUB~ Our Innovative Solution
~P~ [Present the solution as a breakthrough innovation. Explain the technology, methodology, or approach. Highlight unique features, competitive advantages, and how it addresses the market gap effectively.]

~S_SUB~ Target Market & Customer Analysis
~P~ [Provide comprehensive analysis of the target audience. Include market segmentation, customer personas, addressable market size (TAM/SAM/SOM), customer acquisition strategy, and revenue potential per customer.]

~S_SUB~ Competitive Landscape & Differentiation
~P~ [Conduct thorough competitive analysis. Identify direct and indirect competitors, market positioning, unique value propositions, barriers to entry, and sustainable competitive advantages.]

~S_SUB~ Business Model & Revenue Streams
~P~ [Detail the revenue model, pricing strategy, sales channels, customer lifetime value, and path to profitability. Include multiple revenue streams if applicable and scalability factors.]

~S_SUB~ Financial Projections & Key Metrics
~P~ [Present realistic financial forecasts for 3-5 years. Include revenue projections, key performance indicators, unit economics, burn rate, runway, and path to break-even. Use industry benchmarks where possible.]

~S_SUB~ Growth Strategy & Market Expansion
~P~ [Elaborate on growth strategy with specific milestones, market expansion strategy, product roadmap, partnership opportunities, and scaling plans. Include timeline and resource requirements.]

~S_SUB~ Team & Execution Capability
~P~ [Highlight the founding team's expertise, relevant experience, advisory board, key hires planned, and why this team can execute the vision successfully. Address any skill gaps and hiring plans.]

~S_SUB~ Risk Assessment & Mitigation
~P~ [Identify key risks (market, technical, competitive, regulatory) and present clear mitigation strategies. Show awareness of challenges and preparedness to address them.]

~S_SUB~ Investment Ask & Use of Funds
~ASK~ {f"Our ask is for {form_data.get('fundingAmount', 'strategic investment')} to be primarily allocated towards product development, aggressive marketing, and key team hires. This investment will accelerate our growth and capture a significant market share." if form_data.get('fundingAmount') else f"Our primary objective is to {form_data.get('pitchObjective', 'secure strategic partnership')}. We are seeking strategic partners who align with our vision and can help us achieve this goal."}

~S_SUB~ Expected Returns & Exit Strategy
~P~ [Present potential return scenarios, exit opportunities (IPO, acquisition), comparable transactions, and timeline to liquidity events. Include investor value proposition.]

~S_SUB~ Call to Action
~P~ [End with a compelling call to action that invites investors to participate in this opportunity. Reiterate the key value propositions and next steps for interested investors.]
--- END OF REQUIRED FORMAT ---

**Enhanced Requirements:**
1. Include specific metrics, data points, and industry benchmarks throughout
2. Use professional investment terminology appropriate for institutional investors
3. Ensure all financial projections are realistic and well-justified
4. Address potential investor concerns proactively
5. Maintain consistency with uploaded business plan content if provided
6. Generate content in {language} with proper cultural and linguistic context
7. Create investment-grade content suitable for actual investor presentations

**Final Command:**
Generate the complete, comprehensive investor pitch based on all instructions above. The output MUST be only the tagged pitch itself, formatted as a single block of text with each tagged element on a new line. Ensure the content is detailed, professional, and investment-ready.
"""
        
        response = model.generate_content(pitch_prompt)
        pitch_content = response.text
        
        if not pitch_content or not pitch_content.strip():
            raise ValueError("AI Service Error: Generated pitch was empty.")
            
        logger.info(f"Investor pitch generated successfully in '{language}'.")
        return pitch_content
        
    except google_exceptions.DeadlineExceeded as e:
        logger.error(f"Gemini Gateway Error: The request to Google API timed out. {e}", exc_info=True)
        raise AIGatewayException("The AI analysis service timed out while generating the pitch.") from e
    except Exception as e:
        if 'failed to connect' in str(e).lower() or 'deadline' in str(e).lower():
            logger.error(f"Gemini Gateway Error: Detected network issue. {e}", exc_info=True)
            raise AIGatewayException("The AI analysis service could not connect to its provider.") from e
        
        logger.error(f"Unexpected error during pitch generation: {e}", exc_info=True)
        raise ValueError(f"AI Service Error: An unexpected error occurred: {str(e)}")
