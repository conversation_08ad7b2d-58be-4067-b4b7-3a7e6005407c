import { Event } from '../../../entities/events/event.js';
import { PaymentMethodNotification } from '../../entities/index.js';
import { EventName } from '../../helpers/index.js';
export class PaymentMethodSavedEvent extends Event {
    constructor(response) {
        super(response);
        this.eventType = EventName.PaymentMethodSaved;
        this.data = new PaymentMethodNotification(response.data);
    }
}
