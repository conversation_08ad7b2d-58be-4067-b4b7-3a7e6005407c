// middleware/validation.js
import { body, param, query, validationResult } from 'express-validator';

/**
 * Middleware to handle validation errors
 */
export const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            error: 'Validation failed',
            details: errors.array().map(err => ({
                field: err.path,
                message: err.msg,
                value: err.value
            }))
        });
    }
    next();
};

/**
 * Validation rules for user registration
 */
export const validateUserRegistration = [
    body('name')
        .optional()
        .trim()
        .isLength({ min: 0, max: 100 })
        .withMessage('Name must be less than 100 characters'),

    body('email')
        .isEmail()
        .withMessage('Please provide a valid email address'),

    body('password')
        .isLength({ min: 6 })
        .withMessage('Password must be at least 6 characters long'),

    body('isAdmin')
        .optional()
        .toBoolean(),

    handleValidationErrors
];

/**
 * Validation rules for user login
 */
export const validateUserLogin = [
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email address'),
    
    body('password')
        .notEmpty()
        .withMessage('Password is required'),
    
    handleValidationErrors
];

/**
 * Validation rules for updating user (admin operation)
 */
export const validateUserUpdate = [
    param('id')
        .isMongoId()
        .withMessage('Invalid user ID'),
    
    body('name')
        .optional()
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('Name must be between 1 and 100 characters'),
    
    body('email')
        .optional()
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email address'),
    
    body('isAdmin')
        .optional()
        .isBoolean()
        .withMessage('isAdmin must be a boolean value'),
    
    body('isVerified')
        .optional()
        .isBoolean()
        .withMessage('isVerified must be a boolean value'),
    
    handleValidationErrors
];

/**
 * Validation rules for user ID parameter
 */
export const validateUserId = [
    param('id')
        .isMongoId()
        .withMessage('Invalid user ID'),
    
    handleValidationErrors
];

/**
 * Validation rules for pagination and search
 */
export const validateUserQuery = [
    query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
    
    query('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100'),
    
    query('search')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Search term must be less than 100 characters'),
    
    query('sortBy')
        .optional()
        .isIn(['name', 'email', 'createdAt', 'isVerified', 'isAdmin'])
        .withMessage('Invalid sort field'),
    
    query('sortOrder')
        .optional()
        .isIn(['asc', 'desc'])
        .withMessage('Sort order must be asc or desc'),
    
    handleValidationErrors
];

/**
 * Validation rules for system settings update
 */
export const validateSystemSettings = [
    body('allowNewAdminRegistration')
        .optional()
        .isBoolean()
        .withMessage('allowNewAdminRegistration must be a boolean value'),
    
    body('maintenanceMode')
        .optional()
        .isBoolean()
        .withMessage('maintenanceMode must be a boolean value'),
    
    body('systemName')
        .optional()
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('System name must be between 1 and 100 characters'),
    
    handleValidationErrors
];

/**
 * Validation rules for email verification
 */
export const validateEmailVerification = [
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email address'),
    
    body('verificationCode')
        .isLength({ min: 6, max: 6 })
        .isNumeric()
        .withMessage('Verification code must be a 6-digit number'),
    
    handleValidationErrors
];

/**
 * Sanitize and validate file uploads (if needed for future features)
 */
export const validateFileUpload = [
    body('fileType')
        .optional()
        .isIn(['pdf', 'doc', 'docx', 'txt'])
        .withMessage('Invalid file type'),
    
    handleValidationErrors
];
