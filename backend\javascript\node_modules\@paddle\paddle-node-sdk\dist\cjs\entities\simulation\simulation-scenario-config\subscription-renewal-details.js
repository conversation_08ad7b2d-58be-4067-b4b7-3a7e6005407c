"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubscriptionRenewalDetails = void 0;
const subscription_renewal_entities_js_1 = require("./subscription-renewal-entities.js");
const subscription_renewal_options_js_1 = require("./subscription-renewal-options.js");
class SubscriptionRenewalDetails {
    constructor(config) {
        this.entities = config.entities ? new subscription_renewal_entities_js_1.SubscriptionRenewalEntities(config.entities) : null;
        this.options = config.options ? new subscription_renewal_options_js_1.SubscriptionRenewalOptions(config.options) : null;
    }
}
exports.SubscriptionRenewalDetails = SubscriptionRenewalDetails;
