import { TimePeriodNotification } from './time-period-notification.js';
export class BillingDetailsNotification {
    constructor(billingDetails) {
        var _a;
        this.enableCheckout = (_a = billingDetails.enable_checkout) !== null && _a !== void 0 ? _a : null;
        this.purchaseOrderNumber = billingDetails.purchase_order_number ? billingDetails.purchase_order_number : null;
        this.additionalInformation = billingDetails.additional_information ? billingDetails.additional_information : null;
        this.paymentTerms = new TimePeriodNotification(billingDetails.payment_terms);
    }
}
