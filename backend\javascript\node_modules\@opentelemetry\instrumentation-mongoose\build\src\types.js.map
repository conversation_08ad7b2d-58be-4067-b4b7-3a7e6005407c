{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": "", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { Span } from '@opentelemetry/api';\nimport { InstrumentationConfig } from '@opentelemetry/instrumentation';\n\nexport interface SerializerPayload {\n  condition?: any;\n  options?: any;\n  updates?: any;\n  document?: any;\n  aggregatePipeline?: any;\n  fields?: any;\n}\n\nexport type DbStatementSerializer = (\n  operation: string,\n  payload: SerializerPayload\n) => string;\n\nexport interface ResponseInfo {\n  moduleVersion: string | undefined;\n  response: any;\n}\n\nexport type MongooseResponseCustomAttributesFunction = (\n  span: Span,\n  responseInfo: ResponseInfo\n) => void;\n\nexport interface MongooseInstrumentationConfig extends InstrumentationConfig {\n  /**\n   * Mongoose operation use mongodb under the hood.\n   * If mongodb instrumentation is enabled, a mongoose operation will also create\n   * a mongodb operation describing the communication with mongoDB servers.\n   * Setting the `suppressInternalInstrumentation` config value to `true` will\n   * cause the instrumentation to suppress instrumentation of underlying operations,\n   * effectively causing mongodb spans to be non-recordable.\n   */\n  suppressInternalInstrumentation?: boolean;\n\n  /** Custom serializer function for the db.statement tag */\n  dbStatementSerializer?: DbStatementSerializer;\n\n  /** hook for adding custom attributes using the response payload */\n  responseHook?: MongooseResponseCustomAttributesFunction;\n\n  /** Set to true if you do not want to collect traces that start with mongoose */\n  requireParentSpan?: boolean;\n}\n"]}