import { <PERSON>r<PERSON><PERSON><PERSON>, MeterProvider } from '@opentelemetry/api';
import { Instrumentation } from './types';
import { LoggerProvider } from '@opentelemetry/api-logs';
/**
 * Enable instrumentations
 * @param instrumentations
 * @param tracerProvider
 * @param meterProvider
 */
export declare function enableInstrumentations(instrumentations: Instrumentation[], tracerProvider?: TracerProvider, meterProvider?: MeterProvider, loggerProvider?: LoggerProvider): void;
/**
 * Disable instrumentations
 * @param instrumentations
 */
export declare function disableInstrumentations(instrumentations: Instrumentation[]): void;
//# sourceMappingURL=autoLoaderUtils.d.ts.map