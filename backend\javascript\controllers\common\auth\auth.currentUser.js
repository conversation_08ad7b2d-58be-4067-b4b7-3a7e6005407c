// node_gemini_summarizer/controllers/auth/auth.currentUser.js
import User from '../../../models/User.js'; // Adjust path
import { formatUserResponse } from './auth.helpers.js';

export const getCurrentUser = async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select('-password');
    if (!user) {
      return res.status(404).json({ error: 'User not found.' });
    }
    res.status(200).json({ user: formatUserResponse(user) });
  } catch (error) {
    console.error('Error in getCurrentUser controller:', error);
    res.status(500).json({ error: 'Server error while fetching user profile.' });
  }
};