// src/controllers/Tools/Business/investorPitchController.js
import { generateContent } from '../../../services/geminiBusinessService.js';
import { buildInvestorPitchPrompt } from './investorPitchPrompts.js';
import User from '../../../models/User.js';
import {
    ensureSubscription,
    FREE_TIER_PLAN_NAME_BACKEND,
    PRO_PLAN_NAME_BACKEND,
    shouldEnforceLimit,
    getLimit,
} from '../../common/user/limit/planConfig.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Placeholder text extraction function...
const extractTextFromBusinessPlan = async (file) => {
    // ... function content remains the same
};

export const generateInvestorPitch = async (req, res) => {
    // ... validation logic remains the same

    try {
        console.log(`[PITCH_CONTROLLER] Generating investor pitch for project: "${req.body.projectName}"`);

        let user = await User.findById(req.user.id);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        user = ensureSubscription(user);
        const { planName } = user.subscription;

        // --- FIX: Correctly check limits for the FREE plan only ---
        if (shouldEnforceLimit(planName)) {
            const currentCount = user.subscription.freeTierInvestorPitchCount || 0;
            const limit = getLimit(planName, 'investorPitch');

            if (currentCount >= limit) {
                return res.status(403).json({
                    error: `${planName} plan investor pitch limit reached (${limit}). Please upgrade for unlimited access.`
                });
            }
        }

        let businessPlanContent = null;
        if (req.file) {
            businessPlanContent = await extractTextFromBusinessPlan(req.file);
        }
        
        const enhancedFormData = { ...req.body, businessPlanContent };
        const pitchPrompt = buildInvestorPitchPrompt(enhancedFormData);
        const generatedPitch = await generateContent(pitchPrompt);

        // Increment the correct usage count
        if (planName === FREE_TIER_PLAN_NAME_BACKEND) {
            user.subscription.freeTierInvestorPitchCount = (user.subscription.freeTierInvestorPitchCount || 0) + 1;
        } else if (planName === PRO_PLAN_NAME_BACKEND) {
            user.subscription.proTierInvestorPitchCount = (user.subscription.proTierInvestorPitchCount || 0) + 1;
        }
        await user.save();

        res.status(200).json({
            generatedPitch,
            subscription: user.subscription
        });

    } catch (error) {
        console.error(`[PITCH_CONTROLLER] Error during pitch generation:`, error.message);
        res.status(500).json({
            error: 'An internal server error occurred while generating the investor pitch.'
        });
    }
};