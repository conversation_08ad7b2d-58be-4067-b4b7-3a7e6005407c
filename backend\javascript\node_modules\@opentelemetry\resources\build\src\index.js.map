{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,uCAAsC;AAA7B,oGAAA,QAAQ,OAAA;AAEjB,uCAAgD;AAAvC,8GAAA,kBAAkB,OAAA;AAG3B,yCAYqB;AAXnB,4GAAA,eAAe,OAAA;AACf,gHAAA,mBAAmB,OAAA;AACnB,wGAAA,WAAW,OAAA;AACX,4GAAA,eAAe,OAAA;AACf,yGAAA,YAAY,OAAA;AACZ,6GAAA,gBAAgB,OAAA;AAChB,uGAAA,UAAU,OAAA;AACV,2GAAA,cAAc,OAAA;AACd,4GAAA,eAAe,OAAA;AACf,gHAAA,mBAAmB,OAAA;AACnB,0HAAA,6BAA6B,OAAA;AAE/B,uDAA0E;AAAjE,uHAAA,mBAAmB,OAAA;AAAE,mHAAA,eAAe,OAAA", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Resource } from './Resource';\nexport { IResource } from './IResource';\nexport { defaultServiceName } from './platform';\nexport { DetectorSync, ResourceAttributes, Detector } from './types';\nexport { ResourceDetectionConfig } from './config';\nexport {\n  browserDetector,\n  browserDetectorSync,\n  envDetector,\n  envDetectorSync,\n  hostDetector,\n  hostDetectorSync,\n  osDetector,\n  osDetectorSync,\n  processDetector,\n  processDetectorSync,\n  serviceInstanceIdDetectorSync,\n} from './detectors';\nexport { detectResourcesSync, detectResources } from './detect-resources';\n"]}