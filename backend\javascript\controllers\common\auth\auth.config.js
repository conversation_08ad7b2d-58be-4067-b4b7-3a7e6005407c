// node_gemini_summarizer/controllers/auth/auth.config.js
import dotenv from 'dotenv';
// Call dotenv.config() once, typically at the very top of your main server entry file (e.g., server.js)
// If you've already called it in server.js, you don't strictly need to call it again here.
// However, to ensure JWT_SECRET is available if this module is somehow imported before server.js fully initializes it:
dotenv.config(); // This will load .env if not already loaded.

export const JWT_SECRET = process.env.JWT_SECRET;
export const OTP_EXPIRY_MINUTES = 10;
export const JWT_EXPIRY_DURATION = '7d'; // Token expires in 7 days

if (!JWT_SECRET) {
    console.error("FATAL ERROR: JWT_SECRET is not defined in .env file. Authentication will not work.");
    // In a real app, you might want to throw an error here to prevent the app from starting without a JWT_SECRET
    // throw new Error("FATAL ERROR: JWT_SECRET is not defined.");
}