{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,oBAAoB,EAAE,MAAM,4CAA4C,CAAC;AAClF,OAAO,EAAE,aAAa,EAAS,MAAM,yBAAyB,CAAC;AAC/D,OAAO,EACL,cAAc,EACd,gBAAgB,EAChB,kBAAkB,GACnB,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EACL,kBAAkB,EAClB,qBAAqB,GACtB,MAAM,+BAA+B,CAAC;AACvC,OAAO,EAAE,mBAAmB,EAAE,MAAM,gCAAgC,CAAC;AACrE,OAAO,EACL,UAAU,EACV,aAAa,EACb,MAAM,EACN,cAAc,EACd,oBAAoB,EACpB,oBAAoB,EACpB,mBAAmB,EACnB,iBAAiB,EACjB,WAAW,EACX,iBAAiB,EACjB,cAAc,EACd,iBAAiB,GAClB,MAAM,eAAe,CAAC;AAQvB,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAgB,gBAAgB,EAAE,MAAM,gBAAgB,CAAC;AAChE,OAAO,EACL,WAAW,EACX,iBAAiB,EACjB,uBAAuB,EACvB,iBAAiB,GAClB,MAAM,iBAAiB,CAAC;AACzB,MAAM,CAAC,MAAM,YAAY,GAAG;IAC1B,WAAW;IACX,iBAAiB;IACjB,uBAAuB;IACvB,iBAAiB;CAClB,CAAC;AACF,OAAO,EACL,iBAAiB,EACjB,QAAQ,EACR,WAAW,EACX,MAAM,EACN,qBAAqB,EACrB,WAAW,EACX,aAAa,EACb,UAAU,GACX,MAAM,YAAY,CAAC;AACpB,OAAO,EACL,mBAAmB,GAEpB,MAAM,yBAAyB,CAAC;AACjC,OAAO,EACL,mBAAmB,EACnB,kBAAkB,EAClB,yBAAyB,EACzB,gBAAgB,GACjB,MAAM,mCAAmC,CAAC;AAE3C,OAAO,EAEL,OAAO,EACP,iBAAiB,EACjB,cAAc,EACd,cAAc,GACf,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAAE,gBAAgB,EAAE,MAAM,kCAAkC,CAAC;AACpE,OAAO,EAAE,eAAe,EAAE,MAAM,iCAAiC,CAAC;AAClE,OAAO,EAAE,kBAAkB,EAAE,MAAM,oCAAoC,CAAC;AACxE,OAAO,EAAE,wBAAwB,EAAE,MAAM,0CAA0C,CAAC;AACpF,OAAO,EACL,mBAAmB,EACnB,eAAe,EACf,iBAAiB,GAClB,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EACL,6BAA6B,EAC7B,oCAAoC,EACpC,mBAAmB,EACnB,4CAA4C,EAC5C,2CAA2C,EAG3C,gBAAgB,GACjB,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,mBAAmB,EAAE,MAAM,kBAAkB,CAAC;AACvD,OAAO,EAAE,YAAY,EAAE,eAAe,EAAE,MAAM,iBAAiB,CAAC;AAChE,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AACvD,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AACzC,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,OAAO,EAAE,MAAM,qBAAqB,CAAC;AAC9C,MAAM,CAAC,MAAM,QAAQ,GAAG;IACtB,OAAO;CACR,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { W3CBaggagePropagator } from './baggage/propagation/W3CBaggagePropagator';\nexport { AnchoredClock, Clock } from './common/anchored-clock';\nexport {\n  isAttributeKey,\n  isAttributeValue,\n  sanitizeAttributes,\n} from './common/attributes';\nexport {\n  globalErrorHandler,\n  setGlobalErrorHandler,\n} from './common/global-error-handler';\nexport { loggingErrorHandler } from './common/logging-error-handler';\nexport {\n  addHrTimes,\n  getTimeOrigin,\n  hrTime,\n  hrTimeDuration,\n  hrTimeToMicroseconds,\n  hrTimeToMilliseconds,\n  hrTimeToNanoseconds,\n  hrTimeToTimeStamp,\n  isTimeInput,\n  isTimeInputHrTime,\n  millisToHrTime,\n  timeInputToHrTime,\n} from './common/time';\nexport {\n  ErrorHandler,\n  InstrumentationLibrary,\n  InstrumentationScope,\n  ShimWrapped,\n  TimeOriginLegacy,\n} from './common/types';\nexport { hexToBinary } from './common/hex-to-binary';\nexport { ExportResult, ExportResultCode } from './ExportResult';\nimport {\n  getKeyPairs,\n  serializeKeyPairs,\n  parseKeyPairsIntoRecord,\n  parsePairKeyValue,\n} from './baggage/utils';\nexport const baggageUtils = {\n  getKeyPairs,\n  serializeKeyPairs,\n  parseKeyPairsIntoRecord,\n  parsePairKeyValue,\n};\nexport {\n  RandomIdGenerator,\n  SDK_INFO,\n  _globalThis,\n  getEnv,\n  getEnvWithoutDefaults,\n  hexToBase64,\n  otperformance,\n  unrefTimer,\n} from './platform';\nexport {\n  CompositePropagator,\n  CompositePropagatorConfig,\n} from './propagation/composite';\nexport {\n  TRACE_PARENT_HEADER,\n  TRACE_STATE_HEADER,\n  W3CTraceContextPropagator,\n  parseTraceParent,\n} from './trace/W3CTraceContextPropagator';\nexport { IdGenerator } from './trace/IdGenerator';\nexport {\n  RPCMetadata,\n  RPCType,\n  deleteRPCMetadata,\n  getRPCMetadata,\n  setRPCMetadata,\n} from './trace/rpc-metadata';\nexport { AlwaysOffSampler } from './trace/sampler/AlwaysOffSampler';\nexport { AlwaysOnSampler } from './trace/sampler/AlwaysOnSampler';\nexport { ParentBasedSampler } from './trace/sampler/ParentBasedSampler';\nexport { TraceIdRatioBasedSampler } from './trace/sampler/TraceIdRatioBasedSampler';\nexport {\n  isTracingSuppressed,\n  suppressTracing,\n  unsuppressTracing,\n} from './trace/suppress-tracing';\nexport { TraceState } from './trace/TraceState';\nexport {\n  DEFAULT_ATTRIBUTE_COUNT_LIMIT,\n  DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n  DEFAULT_ENVIRONMENT,\n  DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,\n  DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT,\n  ENVIRONMENT,\n  RAW_ENVIRONMENT,\n  parseEnvironment,\n} from './utils/environment';\nexport { merge } from './utils/merge';\nexport { TracesSamplerValues } from './utils/sampling';\nexport { TimeoutError, callWithTimeout } from './utils/timeout';\nexport { isUrlIgnored, urlMatches } from './utils/url';\nexport { isWrapped } from './utils/wrap';\nexport { BindOnceFuture } from './utils/callback';\nexport { VERSION } from './version';\nimport { _export } from './internal/exporter';\nexport const internal = {\n  _export,\n};\n"]}