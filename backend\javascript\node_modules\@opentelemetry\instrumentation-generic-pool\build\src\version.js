"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;
// this is autogenerated file, see scripts/version-update.js
exports.PACKAGE_VERSION = '0.43.1';
exports.PACKAGE_NAME = '@opentelemetry/instrumentation-generic-pool';
//# sourceMappingURL=version.js.map